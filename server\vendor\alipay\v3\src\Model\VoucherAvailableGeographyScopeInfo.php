<?php
/**
 * VoucherAvailableGeographyScopeInfo
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * VoucherAvailableGeographyScopeInfo Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class VoucherAvailableGeographyScopeInfo implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'VoucherAvailableGeographyScopeInfo';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'availableGeographyCityInfo' => '\Alipay\OpenAPISDK\Model\VoucherAvailableGeographyCityInfo',
        'availableGeographyScopeType' => 'string',
        'availableGeographyShopInfo' => '\Alipay\OpenAPISDK\Model\VoucherAvailableGeographyShopInfo'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'availableGeographyCityInfo' => null,
        'availableGeographyScopeType' => null,
        'availableGeographyShopInfo' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'availableGeographyCityInfo' => 'available_geography_city_info',
        'availableGeographyScopeType' => 'available_geography_scope_type',
        'availableGeographyShopInfo' => 'available_geography_shop_info'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'availableGeographyCityInfo' => 'setAvailableGeographyCityInfo',
        'availableGeographyScopeType' => 'setAvailableGeographyScopeType',
        'availableGeographyShopInfo' => 'setAvailableGeographyShopInfo'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'availableGeographyCityInfo' => 'getAvailableGeographyCityInfo',
        'availableGeographyScopeType' => 'getAvailableGeographyScopeType',
        'availableGeographyShopInfo' => 'getAvailableGeographyShopInfo'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['availableGeographyCityInfo'] = $data['availableGeographyCityInfo'] ?? null;
        $this->container['availableGeographyScopeType'] = $data['availableGeographyScopeType'] ?? null;
        $this->container['availableGeographyShopInfo'] = $data['availableGeographyShopInfo'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets availableGeographyCityInfo
     *
     * @return \Alipay\OpenAPISDK\Model\VoucherAvailableGeographyCityInfo|null
     */
    public function getAvailableGeographyCityInfo()
    {
        return $this->container['availableGeographyCityInfo'];
    }

    /**
     * Sets availableGeographyCityInfo
     *
     * @param \Alipay\OpenAPISDK\Model\VoucherAvailableGeographyCityInfo|null $availableGeographyCityInfo availableGeographyCityInfo
     *
     * @return self
     */
    public function setAvailableGeographyCityInfo($availableGeographyCityInfo)
    {
        $this->container['availableGeographyCityInfo'] = $availableGeographyCityInfo;

        return $this;
    }

    /**
     * Gets availableGeographyScopeType
     *
     * @return string|null
     */
    public function getAvailableGeographyScopeType()
    {
        return $this->container['availableGeographyScopeType'];
    }

    /**
     * Sets availableGeographyScopeType
     *
     * @param string|null $availableGeographyScopeType 券可用地理位置类型。
     *
     * @return self
     */
    public function setAvailableGeographyScopeType($availableGeographyScopeType)
    {
        $this->container['availableGeographyScopeType'] = $availableGeographyScopeType;

        return $this;
    }

    /**
     * Gets availableGeographyShopInfo
     *
     * @return \Alipay\OpenAPISDK\Model\VoucherAvailableGeographyShopInfo|null
     */
    public function getAvailableGeographyShopInfo()
    {
        return $this->container['availableGeographyShopInfo'];
    }

    /**
     * Sets availableGeographyShopInfo
     *
     * @param \Alipay\OpenAPISDK\Model\VoucherAvailableGeographyShopInfo|null $availableGeographyShopInfo availableGeographyShopInfo
     *
     * @return self
     */
    public function setAvailableGeographyShopInfo($availableGeographyShopInfo)
    {
        $this->container['availableGeographyShopInfo'] = $availableGeographyShopInfo;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


