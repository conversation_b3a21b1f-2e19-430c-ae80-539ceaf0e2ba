# -*- coding: utf-8 -*-
"""
支付处理模块
版本: 1.0.0
创建时间: 2024-12-19
"""

import time
import qrcode
from io import BytesIO
from typing import Dict, List, Optional, Any
from .utils import config, logger, http_client

class PaymentManager:
    """支付管理器"""
    
    def __init__(self, token: Optional[str] = None):
        self.token = token
        self.api_base = config.get('server.api_base')
        
    def set_token(self, token: str):
        """设置用户令牌"""
        self.token = token
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        headers = {'Content-Type': 'application/json'}
        if self.token:
            headers['Authorization'] = f'Bearer {self.token}'
        return headers
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """发送API请求"""
        if not self.token:
            raise Exception("用户未登录，请先登录")
        
        url = f"{self.api_base}{endpoint}"
        headers = self._get_headers()
        
        try:
            if method.upper() == 'GET':
                response = http_client.get(url, headers=headers, params=data)
            else:
                response = http_client.post(url, headers=headers, json=data)
            
            result = response.json()
            
            if not result.get('success', False):
                raise Exception(result.get('message', '请求失败'))
            
            return result
            
        except Exception as e:
            logger.error(f"支付API请求失败: {e}")
            raise e
    
    def create_order(self, amount: float) -> Dict[str, Any]:
        """创建支付订单"""
        if amount <= 0:
            raise ValueError("支付金额必须大于0")
        
        if amount > 10000:
            raise ValueError("单次充值金额不能超过10000元")
        
        try:
            data = {'amount': amount}
            result = self._make_request('POST', 'payment/create.php', data)
            
            order_info = result['data']
            logger.info(f"支付订单创建成功: 订单号 {order_info['order_number']}, 金额 {amount} 元")
            
            return order_info
            
        except Exception as e:
            logger.error(f"创建支付订单失败: {e}")
            raise e
    
    def query_order_status(self, order_number: str) -> Dict[str, Any]:
        """查询订单状态"""
        if not order_number:
            raise ValueError("订单号不能为空")
        
        try:
            params = {'order_number': order_number}
            result = self._make_request('GET', 'payment/query.php', params)
            
            order_info = result['data']
            logger.info(f"订单状态查询成功: 订单号 {order_number}, 状态 {order_info['status']}")
            
            return order_info
            
        except Exception as e:
            logger.error(f"查询订单状态失败: {e}")
            raise e
    
    def get_user_orders(self, page: int = 1, limit: int = 20, 
                       status: Optional[str] = None) -> Dict[str, Any]:
        """获取用户订单列表"""
        try:
            params = {
                'page': page,
                'limit': limit
            }
            
            if status:
                params['status'] = status
            
            result = self._make_request('GET', 'payment/orders.php', params)
            
            logger.info(f"获取订单列表成功: 页码 {page}, 总数 {result['data']['total']}")
            return result['data']
            
        except Exception as e:
            logger.error(f"获取订单列表失败: {e}")
            raise e
    
    def generate_qr_code(self, qr_content: str, size: int = 200) -> bytes:
        """生成二维码图片"""
        try:
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(qr_content)
            qr.make(fit=True)
            
            img = qr.make_image(fill_color="black", back_color="white")
            img = img.resize((size, size))
            
            # 转换为字节流
            img_buffer = BytesIO()
            img.save(img_buffer, format='PNG')
            img_bytes = img_buffer.getvalue()
            
            logger.info("二维码生成成功")
            return img_bytes
            
        except Exception as e:
            logger.error(f"生成二维码失败: {e}")
            raise e
    
    def save_qr_code(self, qr_content: str, file_path: str, size: int = 200) -> bool:
        """保存二维码到文件"""
        try:
            img_bytes = self.generate_qr_code(qr_content, size)
            
            with open(file_path, 'wb') as f:
                f.write(img_bytes)
            
            logger.info(f"二维码保存成功: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存二维码失败: {e}")
            return False
    
    def wait_for_payment(self, order_number: str, timeout: int = 300, 
                        check_interval: int = 3) -> Dict[str, Any]:
        """等待支付完成"""
        start_time = time.time()
        
        logger.info(f"开始等待支付: 订单号 {order_number}, 超时时间 {timeout} 秒")
        
        while time.time() - start_time < timeout:
            try:
                order_info = self.query_order_status(order_number)
                
                if order_info['status'] == 'completed':
                    logger.info(f"支付成功: 订单号 {order_number}")
                    return {
                        'success': True,
                        'status': 'completed',
                        'order_info': order_info,
                        'message': '支付成功'
                    }
                elif order_info['status'] in ['failed', 'cancelled', 'expired']:
                    logger.warning(f"支付失败: 订单号 {order_number}, 状态 {order_info['status']}")
                    return {
                        'success': False,
                        'status': order_info['status'],
                        'order_info': order_info,
                        'message': f"支付{self.format_order_status(order_info['status'])}"
                    }
                
                # 等待下次检查
                time.sleep(check_interval)
                
            except Exception as e:
                logger.error(f"检查支付状态失败: {e}")
                time.sleep(check_interval)
        
        # 超时
        logger.warning(f"支付等待超时: 订单号 {order_number}")
        return {
            'success': False,
            'status': 'timeout',
            'order_info': None,
            'message': '支付等待超时'
        }
    
    def format_order_status(self, status: str) -> str:
        """格式化订单状态"""
        status_map = {
            'pending': '待支付',
            'completed': '已完成',
            'failed': '失败',
            'cancelled': '已取消',
            'expired': '已过期'
        }
        return status_map.get(status, status)
    
    def format_amount(self, amount: float) -> str:
        """格式化金额显示"""
        return f"¥{amount:.2f}"
    
    def calculate_points(self, amount: float, points_per_yuan: int = 1) -> int:
        """计算充值积分数量"""
        return int(amount * points_per_yuan)
    
    def get_payment_methods(self) -> List[Dict[str, str]]:
        """获取支持的支付方式"""
        return [
            {
                'id': 'alipay',
                'name': '支付宝',
                'description': '支付宝扫码支付',
                'icon': '💰'
            }
        ]
    
    def validate_amount(self, amount: float) -> bool:
        """验证支付金额"""
        if not isinstance(amount, (int, float)):
            return False
        
        if amount <= 0:
            return False
        
        if amount > 10000:
            return False
        
        # 检查小数位数
        if round(amount, 2) != amount:
            return False
        
        return True

__all__ = ['PaymentManager']
