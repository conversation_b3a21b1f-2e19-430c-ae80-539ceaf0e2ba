<?php
/**
 * InvoiceTitleModel
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * InvoiceTitleModel Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class InvoiceTitleModel implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'InvoiceTitleModel';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'isDefault' => 'bool',
        'logonId' => 'string',
        'openBankAccount' => 'string',
        'openBankName' => 'string',
        'openId' => 'string',
        'taxRegisterNo' => 'string',
        'telePhoneNo' => 'string',
        'titleName' => 'string',
        'titleType' => 'string',
        'userAddress' => 'string',
        'userEmail' => 'string',
        'userId' => 'string',
        'userMobile' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'isDefault' => null,
        'logonId' => null,
        'openBankAccount' => null,
        'openBankName' => null,
        'openId' => null,
        'taxRegisterNo' => null,
        'telePhoneNo' => null,
        'titleName' => null,
        'titleType' => null,
        'userAddress' => null,
        'userEmail' => null,
        'userId' => null,
        'userMobile' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'isDefault' => 'is_default',
        'logonId' => 'logon_id',
        'openBankAccount' => 'open_bank_account',
        'openBankName' => 'open_bank_name',
        'openId' => 'open_id',
        'taxRegisterNo' => 'tax_register_no',
        'telePhoneNo' => 'tele_phone_no',
        'titleName' => 'title_name',
        'titleType' => 'title_type',
        'userAddress' => 'user_address',
        'userEmail' => 'user_email',
        'userId' => 'user_id',
        'userMobile' => 'user_mobile'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'isDefault' => 'setIsDefault',
        'logonId' => 'setLogonId',
        'openBankAccount' => 'setOpenBankAccount',
        'openBankName' => 'setOpenBankName',
        'openId' => 'setOpenId',
        'taxRegisterNo' => 'setTaxRegisterNo',
        'telePhoneNo' => 'setTelePhoneNo',
        'titleName' => 'setTitleName',
        'titleType' => 'setTitleType',
        'userAddress' => 'setUserAddress',
        'userEmail' => 'setUserEmail',
        'userId' => 'setUserId',
        'userMobile' => 'setUserMobile'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'isDefault' => 'getIsDefault',
        'logonId' => 'getLogonId',
        'openBankAccount' => 'getOpenBankAccount',
        'openBankName' => 'getOpenBankName',
        'openId' => 'getOpenId',
        'taxRegisterNo' => 'getTaxRegisterNo',
        'telePhoneNo' => 'getTelePhoneNo',
        'titleName' => 'getTitleName',
        'titleType' => 'getTitleType',
        'userAddress' => 'getUserAddress',
        'userEmail' => 'getUserEmail',
        'userId' => 'getUserId',
        'userMobile' => 'getUserMobile'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['isDefault'] = $data['isDefault'] ?? null;
        $this->container['logonId'] = $data['logonId'] ?? null;
        $this->container['openBankAccount'] = $data['openBankAccount'] ?? null;
        $this->container['openBankName'] = $data['openBankName'] ?? null;
        $this->container['openId'] = $data['openId'] ?? null;
        $this->container['taxRegisterNo'] = $data['taxRegisterNo'] ?? null;
        $this->container['telePhoneNo'] = $data['telePhoneNo'] ?? null;
        $this->container['titleName'] = $data['titleName'] ?? null;
        $this->container['titleType'] = $data['titleType'] ?? null;
        $this->container['userAddress'] = $data['userAddress'] ?? null;
        $this->container['userEmail'] = $data['userEmail'] ?? null;
        $this->container['userId'] = $data['userId'] ?? null;
        $this->container['userMobile'] = $data['userMobile'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets isDefault
     *
     * @return bool|null
     */
    public function getIsDefault()
    {
        return $this->container['isDefault'];
    }

    /**
     * Sets isDefault
     *
     * @param bool|null $isDefault 是否为用户设置默认抬头  字段值包括两种情况：  false（非默认）  true（默认抬头）
     *
     * @return self
     */
    public function setIsDefault($isDefault)
    {
        $this->container['isDefault'] = $isDefault;

        return $this;
    }

    /**
     * Gets logonId
     *
     * @return string|null
     */
    public function getLogonId()
    {
        return $this->container['logonId'];
    }

    /**
     * Sets logonId
     *
     * @param string|null $logonId 支付宝用户登录名
     *
     * @return self
     */
    public function setLogonId($logonId)
    {
        $this->container['logonId'] = $logonId;

        return $this;
    }

    /**
     * Gets openBankAccount
     *
     * @return string|null
     */
    public function getOpenBankAccount()
    {
        return $this->container['openBankAccount'];
    }

    /**
     * Sets openBankAccount
     *
     * @param string|null $openBankAccount 银行账号
     *
     * @return self
     */
    public function setOpenBankAccount($openBankAccount)
    {
        $this->container['openBankAccount'] = $openBankAccount;

        return $this;
    }

    /**
     * Gets openBankName
     *
     * @return string|null
     */
    public function getOpenBankName()
    {
        return $this->container['openBankName'];
    }

    /**
     * Sets openBankName
     *
     * @param string|null $openBankName 开户银行
     *
     * @return self
     */
    public function setOpenBankName($openBankName)
    {
        $this->container['openBankName'] = $openBankName;

        return $this;
    }

    /**
     * Gets openId
     *
     * @return string|null
     */
    public function getOpenId()
    {
        return $this->container['openId'];
    }

    /**
     * Sets openId
     *
     * @param string|null $openId 支付宝用户id
     *
     * @return self
     */
    public function setOpenId($openId)
    {
        $this->container['openId'] = $openId;

        return $this;
    }

    /**
     * Gets taxRegisterNo
     *
     * @return string|null
     */
    public function getTaxRegisterNo()
    {
        return $this->container['taxRegisterNo'];
    }

    /**
     * Sets taxRegisterNo
     *
     * @param string|null $taxRegisterNo 纳税人识别号
     *
     * @return self
     */
    public function setTaxRegisterNo($taxRegisterNo)
    {
        $this->container['taxRegisterNo'] = $taxRegisterNo;

        return $this;
    }

    /**
     * Gets telePhoneNo
     *
     * @return string|null
     */
    public function getTelePhoneNo()
    {
        return $this->container['telePhoneNo'];
    }

    /**
     * Sets telePhoneNo
     *
     * @param string|null $telePhoneNo 用户私人手机号
     *
     * @return self
     */
    public function setTelePhoneNo($telePhoneNo)
    {
        $this->container['telePhoneNo'] = $telePhoneNo;

        return $this;
    }

    /**
     * Gets titleName
     *
     * @return string|null
     */
    public function getTitleName()
    {
        return $this->container['titleName'];
    }

    /**
     * Sets titleName
     *
     * @param string|null $titleName 抬头名称
     *
     * @return self
     */
    public function setTitleName($titleName)
    {
        $this->container['titleName'] = $titleName;

        return $this;
    }

    /**
     * Gets titleType
     *
     * @return string|null
     */
    public function getTitleType()
    {
        return $this->container['titleType'];
    }

    /**
     * Sets titleType
     *
     * @param string|null $titleType 抬头类型 字段值有两种情况抬: PERSONAL（个人）  CORPORATION（企业）
     *
     * @return self
     */
    public function setTitleType($titleType)
    {
        $this->container['titleType'] = $titleType;

        return $this;
    }

    /**
     * Gets userAddress
     *
     * @return string|null
     */
    public function getUserAddress()
    {
        return $this->container['userAddress'];
    }

    /**
     * Sets userAddress
     *
     * @param string|null $userAddress 地址
     *
     * @return self
     */
    public function setUserAddress($userAddress)
    {
        $this->container['userAddress'] = $userAddress;

        return $this;
    }

    /**
     * Gets userEmail
     *
     * @return string|null
     */
    public function getUserEmail()
    {
        return $this->container['userEmail'];
    }

    /**
     * Sets userEmail
     *
     * @param string|null $userEmail 邮箱
     *
     * @return self
     */
    public function setUserEmail($userEmail)
    {
        $this->container['userEmail'] = $userEmail;

        return $this;
    }

    /**
     * Gets userId
     *
     * @return string|null
     */
    public function getUserId()
    {
        return $this->container['userId'];
    }

    /**
     * Sets userId
     *
     * @param string|null $userId 支付宝用户id
     *
     * @return self
     */
    public function setUserId($userId)
    {
        $this->container['userId'] = $userId;

        return $this;
    }

    /**
     * Gets userMobile
     *
     * @return string|null
     */
    public function getUserMobile()
    {
        return $this->container['userMobile'];
    }

    /**
     * Sets userMobile
     *
     * @param string|null $userMobile 电话号码
     *
     * @return self
     */
    public function setUserMobile($userMobile)
    {
        $this->container['userMobile'] = $userMobile;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


