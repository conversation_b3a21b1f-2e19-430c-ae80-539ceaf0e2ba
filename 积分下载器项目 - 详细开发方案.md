[MODE: PLAN]

# 积分下载器项目 - 详细开发方案

## 1. 项目概述

将"课程猫下载器"简化并改造为基于积分制的下载器，集成支付宝当面付功能。用户通过充值获取积分，消费积分下载资源。

## 2. 系统架构

### 2.1 目录结构
```
积分下载器/
├── main.py                  # 主程序入口
├── config.json              # 配置文件
├── version.json             # 版本信息
├── requirements.txt         # 依赖包列表
│
├── core/                    # 核心代码目录
│   ├── __init__.py          # 包初始化
│   ├── points.py            # 积分管理模块(替代原auth.py)
│   ├── downloader.py        # 下载器基类(简化版)
│   ├── display.py           # 界面显示模块
│   ├── payment.py           # 支付处理模块(新增)
│   └── utils.py             # 工具函数模块
│
├── platforms/               # 精简的平台实现目录
│   ├── __init__.py          # 平台导入
│   └── [platform_name]/     # 各平台实现
│
├── downloads/               # 下载文件存储目录
├── logs/                    # 日志文件目录
└── keys/                    # 密钥存储目录
```

### 2.2 服务端结构
```
server/
├── api/                     # API接口
│   ├── user/
│   │   ├── login.php        # 用户登录
│   │   └── register.php     # 用户注册
│   ├── points/
│   │   ├── balance.php      # 获取积分余额
│   │   ├── consume.php      # 消费积分
│   │   ├── add.php          # 添加积分
│   │   ├── refund.php       # 退还积分
│   │   └── history.php      # 积分历史
│   └── payment/
│       ├── create.php       # 创建支付订单
│       ├── query.php        # 查询订单状态
│       └── notify.php       # 支付回调通知
│
├── includes/
│   ├── config.php           # 配置文件
│   ├── database.php         # 数据库连接
│   ├── Points.php           # 积分管理类
│   ├── Payment.php          # 支付处理类
│   └── User.php             # 用户管理类
│
├── admin/                   # 管理后台
│   ├── index.php            # 后台首页
│   ├── users.php            # 用户管理
│   ├── points.php           # 积分管理
│   └── resources.php        # 资源管理
│
└── database.sql             # 数据库结构
```

## 3. 核心功能模块详细设计

### 3.1 积分系统设计

**核心功能**:
- 用户积分账户管理
- 积分充值(通过支付宝当面付)
- 积分消费(下载资源)
- 积分余额查询
- 积分交易历史记录

**积分规则**:
1. 建议充值比例: 1元 = 1积分
2. 不同资源消耗不同积分
3. 下载失败自动退还积分
4. 可设置首次注册赠送积分

### 3.2 支付系统设计

**支付流程**:
1. 用户选择充值金额
2. 系统创建支付宝当面付订单
3. 生成支付二维码展示给用户
4. 用户扫码支付
5. 系统接收支付宝回调通知
6. 验证支付成功后为用户账户增加积分

**支付宝集成要点**:
- 需要企业支付宝账户
- 申请支付宝开放平台账号
- 创建应用并获取AppID
- 配置RSA2密钥对
- 设置服务器异步通知地址

### 3.3 下载系统设计

**下载流程**:
1. 用户浏览可下载资源列表
2. 显示资源详情和所需积分
3. 用户确认下载并扣除积分
4. 开始下载资源(支持断点续传)
5. 下载完成后更新下载历史

**资源管理**:
- 根据资源类型和大小设置不同积分要求
- 可设置资源有效期和下载次数限制
- 支持资源预览(如视频缩略图)

## 4. 数据库设计

### 4.1 数据表结构

**用户表(users)**
```sql
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**积分账户表(points)**
```sql
CREATE TABLE points (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    balance INT NOT NULL DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

**积分交易表(point_transactions)**
```sql
CREATE TABLE point_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    amount INT NOT NULL,
    transaction_type ENUM('recharge', 'consume', 'refund', 'gift') NOT NULL,
    resource_id VARCHAR(100),
    order_id VARCHAR(100),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

**订单表(orders)**
```sql
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    user_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    points INT NOT NULL,
    status ENUM('pending', 'completed', 'failed', 'cancelled') NOT NULL DEFAULT 'pending',
    payment_method VARCHAR(20) NOT NULL DEFAULT 'alipay',
    trade_no VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

**资源表(resources)**
```sql
CREATE TABLE resources (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    platform VARCHAR(50) NOT NULL,
    resource_id VARCHAR(100) NOT NULL,
    points_cost INT NOT NULL DEFAULT 0,
    download_count INT NOT NULL DEFAULT 0,
    resource_type VARCHAR(50),
    size BIGINT,
    preview_url VARCHAR(255),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**下载记录表(downloads)**
```sql
CREATE TABLE downloads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    resource_id INT NOT NULL,
    points_cost INT NOT NULL,
    download_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    status ENUM('success', 'failed', 'in_progress') NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (resource_id) REFERENCES resources(id)
);
```

## 5. API接口设计

### 5.1 用户接口

| 接口 | 方法 | 参数 | 返回数据 | 描述 |
|------|------|------|----------|------|
| /api/user/register | POST | username, password, email | user_id, token | 用户注册 |
| /api/user/login | POST | username, password | user_id, token, user_info | 用户登录 |
| /api/user/info | GET | token | user_info | 获取用户信息 |

### 5.2 积分接口

| 接口 | 方法 | 参数 | 返回数据 | 描述 |
|------|------|------|----------|------|
| /api/points/balance | GET | token | points | 获取积分余额 |
| /api/points/consume | POST | token, points, resource_id | success, balance | 消费积分 |
| /api/points/add | POST | token, points, order_id | success, balance | 添加积分 |
| /api/points/refund | POST | token, points, resource_id | success, balance | 退还积分 |
| /api/points/history | GET | token, page, limit | transactions[] | 获取积分历史 |

### 5.3 支付接口

| 接口 | 方法 | 参数 | 返回数据 | 描述 |
|------|------|------|----------|------|
| /api/payment/create | POST | token, amount | order_id, qr_code | 创建支付订单 |
| /api/payment/query | GET | token, order_id | status, trade_no | 查询订单状态 |
| /api/payment/notify | POST | 支付宝回调参数 | success | 支付回调通知 |

### 5.4 资源接口

| 接口 | 方法 | 参数 | 返回数据 | 描述 |
|------|------|------|----------|------|
| /api/resources/list | GET | token, platform, page, limit | resources[] | 获取资源列表 |
| /api/resources/info | GET | token, resource_id | resource_info | 获取资源详情 |
| /api/resources/download | POST | token, resource_id | download_url | 获取资源下载链接 |

## 6. 详细开发步骤

### 6.1 项目准备阶段

1. **环境搭建**
   - 搭建Python开发环境(推荐Python 3.8+)
   - 安装必要依赖包: requests, alipay-sdk-python, pillow, qrcode等
   - 配置PHP服务器环境(Apache/Nginx + PHP 7.4+ + MySQL 5.7+)

2. **支付宝账户准备**
   - 申请企业支付宝账户
   - 注册支付宝开放平台账号
   - 创建应用并获取AppID
   - 生成RSA2密钥对
   - 配置应用信息和接口权限

3. **服务器准备**
   - 准备域名和SSL证书(HTTPS)
   - 设置Web服务器和数据库
   - 配置防火墙和安全设置
   - 创建数据库和导入表结构

### 6.2 核心模块开发

1. **积分管理模块(core/points.py)**
   - 实现用户积分查询、消费、充值功能
   - 对接服务端积分API
   - 实现积分交易历史记录

2. **支付处理模块(core/payment.py)**
   - 集成支付宝当面付SDK
   - 实现支付订单创建和查询
   - 生成支付二维码显示功能
   - 轮询支付结果

3. **下载器基类(core/downloader.py)**
   - 简化原有下载器基类
   - 集成积分验证和消费逻辑
   - 实现通用下载功能(支持断点续传)

4. **显示界面模块(core/display.py)**
   - 实现命令行用户界面
   - 显示积分余额和充值界面
   - 改进资源列表和下载进度显示

5. **工具函数模块(core/utils.py)**
   - 配置文件加载和保存
   - 文件和目录操作
   - HTTP请求封装
   - 加密和解密功能

### 6.3 服务端开发

1. **数据库设计与实现**
   - 创建数据库和表结构
   - 实现必要的索引和约束
   - 编写数据库交互类

2. **API接口开发**
   - 实现用户认证接口
   - 实现积分管理接口
   - 实现支付处理接口
   - 实现资源管理接口

3. **支付宝集成**
   - 实现订单创建
   - 配置异步通知接口
   - 处理支付回调
   - 实现订单状态查询

4. **管理后台开发**
   - 用户管理功能
   - 积分充值和消费记录
   - 订单管理
   - 资源管理

### 6.4 平台适配

1. **平台通用接口(platforms/__init__.py)**
   - 实现平台下载器的动态加载
   - 提供统一的平台获取接口

2. **各平台下载器实现**
   - 继承下载器基类
   - 实现资源列表获取
   - 实现资源下载
   - 处理平台特殊逻辑

### 6.5 测试与完善

1. **单元测试**
   - 测试积分管理模块
   - 测试支付处理模块
   - 测试下载功能

2. **集成测试**
   - 测试完整用户流程
   - 测试支付流程
   - 测试下载流程

3. **性能优化**
   - 优化下载速度
   - 降低服务端负载
   - 改进用户体验

4. **安全加固**
   - 加强授权验证
   - 添加日志记录
   - 防止刷积分和并发下载

## 7. 关键技术点

### 7.1 支付宝当面付集成

```python
# 支付宝SDK初始化示例
from alipay.aop.api.AlipayClientConfig import AlipayClientConfig
from alipay.aop.api.DefaultAlipayClient import DefaultAlipayClient
from alipay.aop.api.domain.AlipayTradePrecreateModel import AlipayTradePrecreateModel
from alipay.aop.api.request.AlipayTradePrecreateRequest import AlipayTradePrecreateRequest

# 配置客户端
alipay_client_config = AlipayClientConfig()
alipay_client_config.server_url = 'https://openapi.alipay.com/gateway.do'
alipay_client_config.app_id = config['payment']['alipay']['app_id']
alipay_client_config.app_private_key = private_key
alipay_client_config.alipay_public_key = alipay_public_key

# 初始化客户端
client = DefaultAlipayClient(alipay_client_config)

# 创建当面付订单
model = AlipayTradePrecreateModel()
model.out_trade_no = order_id
model.total_amount = str(amount)
model.subject = "积分充值"
model.body = f"充值 {amount}元 获得 {amount*10} 积分"

request = AlipayTradePrecreateRequest(biz_model=model)
response = client.execute(request)
```

### 7.2 积分消费流程

```python
def download_resource(self, resource_id, save_path):
    """下载资源并处理积分"""
    # 1. 获取资源信息和所需积分
    resource_info = self.get_resource_info(resource_id)
    points_required = resource_info['points_cost']
    
    # 2. 尝试消费积分
    if not self.points_manager.consume_points(points_required, resource_id):
        return {"success": False, "message": "积分不足或消费失败"}
    
    try:
        # 3. 开始下载资源
        file_path = os.path.join(save_path, resource_info['filename'])
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 4. 实际下载逻辑
        success = self._download_file(resource_info['download_url'], file_path)
        
        if success:
            return {"success": True, "file_path": file_path}
        else:
            # 5. 下载失败退还积分
            self.points_manager.refund_points(points_required, resource_id)
            return {"success": False, "message": "下载失败"}
            
    except Exception as e:
        # 6. 异常情况退还积分
        self.points_manager.refund_points(points_required, resource_id)
        return {"success": False, "message": f"下载出错: {str(e)}"}
```

### 7.3 服务端积分处理

```php
<?php
// 积分消费API示例
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/User.php';
require_once '../includes/Points.php';

// 验证用户Token
$user = new User();
$user_id = $user->validateToken($_SERVER['HTTP_AUTHORIZATION']);

if (!$user_id) {
    echo json_encode(['success' => false, 'message' => '用户未登录或Token无效']);
    exit;
}

// 获取请求数据
$data = json_decode(file_get_contents('php://input'), true);
$points = intval($data['points']);
$resource_id = $data['resource_id'];
$description = $data['description'];

// 处理积分消费
$points_manager = new Points($user_id);
$result = $points_manager->consumePoints($points, $resource_id, $description);

if ($result) {
    $balance = $points_manager->getBalance();
    echo json_encode(['success' => true, 'message' => '积分消费成功', 'data' => ['balance' => $balance]]);
} else {
    echo json_encode(['success' => false, 'message' => '积分不足或消费失败']);
}
?>
```

## 8. 部署与运维

### 8.1 客户端打包

1. **安装PyInstaller**
   ```bash
   pip install pyinstaller
   ```

2. **创建打包脚本(build.py)**
   ```python
   import PyInstaller.__main__
   import os
   
   PyInstaller.__main__.run([
       'main.py',
       '--name=积分下载器',
       '--onefile',
       '--windowed',
       '--icon=assets/icon.ico',
       '--add-data=config.json;.',
       '--add-data=version.json;.'
   ])
   ```

3. **执行打包**
   ```bash
   python build.py
   ```

### 8.2 服务端部署

1. **Web服务器配置**
   - 配置Nginx或Apache
   - 启用SSL证书
   - 设置PHP运行环境
   - 配置URL重写规则

2. **数据库部署**
   - 创建数据库用户和权限
   - 导入数据库结构
   - 配置数据库连接

3. **安全设置**
   - 限制敏感文件访问权限
   - 配置防火墙规则
   - 定期备份数据库
   - 配置HTTPS强制跳转

### 8.3 更新维护

1. **客户端更新**
   - 维护版本信息文件
   - 实现自动更新检查
   - 支持增量更新

2. **服务端维护**
   - 定期检查日志
   - 监控服务器状态
   - 定期备份数据库
   - 安全漏洞修复

## 9. 项目时间线

| 阶段 | 工作内容 | 预计时间 |
|------|---------|----------|
| 1 | 需求分析与项目规划 | 1周 |
| 2 | 环境搭建与准备工作 | 1周 |
| 3 | 核心模块开发 | 2周 |
| 4 | 服务端开发 | 2周 |
| 5 | 平台适配实现 | 1-2周 |
| 6 | 集成测试与bug修复 | 1周 |
| 7 | 性能优化与安全加固 | 1周 |
| 8 | 部署与文档 | 1周 |

**总计**: 9-10周

实施检查清单：
1. 创建项目基础目录结构与文件
2. 配置开发环境与依赖
3. 设置数据库和表结构
4. 实现积分管理模块
5. 实现支付处理模块
6. 开发下载器基类及平台实现
7. 完成服务端API接口
8. 集成支付宝当面付功能
9. 开发用户界面
10. 测试所有功能流程
11. 优化性能与安全
12. 打包客户端与部署服务端

[MODE: REVIEW]

检查了详细开发方案后，我认为这是一个完整且合理的方案，符合您简化框架结构和添加积分+支付宝当面付功能的需求。

这个方案的优点:
- 保留了原项目的核心下载功能
- 简化了原有复杂的授权系统，改为积分制
- 成功集成了支付宝当面付功能
- 项目结构清晰，易于维护
- 详细说明了从开发到部署的每个步骤

需要您提供的额外信息:
1. 您的服务器环境详情(操作系统、Web服务器类型等)
2. 支付宝商户信息(AppID等)
3. 您希望保留原项目中哪些平台的下载功能
4. 是否需要开发管理后台进行积分和用户管理

这个方案可以根据您的具体需求进行调整。例如，如果您更偏好某种特定的用户界面风格，或者需要添加更多的支付方式，都可以在此基础上进行修改。

实施与最终计划完全匹配。