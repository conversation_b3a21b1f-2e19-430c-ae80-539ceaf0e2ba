<?php
/**
 * ModifyStandardInfo
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * ModifyStandardInfo Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class ModifyStandardInfo implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'ModifyStandardInfo';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'addConditionList' => '\Alipay\OpenAPISDK\Model\StandardConditionInfo[]',
        'consumeMode' => 'string',
        'deleteConditionIdList' => 'string[]',
        'modifyConditionList' => '\Alipay\OpenAPISDK\Model\StandardConditionInfo[]',
        'openRuleId' => 'string',
        'paymentPolicy' => 'string',
        'personalQrcodeMode' => 'int',
        'standardDesc' => 'string',
        'standardId' => 'string',
        'standardName' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'addConditionList' => null,
        'consumeMode' => null,
        'deleteConditionIdList' => null,
        'modifyConditionList' => null,
        'openRuleId' => null,
        'paymentPolicy' => null,
        'personalQrcodeMode' => null,
        'standardDesc' => null,
        'standardId' => null,
        'standardName' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'addConditionList' => 'add_condition_list',
        'consumeMode' => 'consume_mode',
        'deleteConditionIdList' => 'delete_condition_id_list',
        'modifyConditionList' => 'modify_condition_list',
        'openRuleId' => 'open_rule_id',
        'paymentPolicy' => 'payment_policy',
        'personalQrcodeMode' => 'personal_qrcode_mode',
        'standardDesc' => 'standard_desc',
        'standardId' => 'standard_id',
        'standardName' => 'standard_name'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'addConditionList' => 'setAddConditionList',
        'consumeMode' => 'setConsumeMode',
        'deleteConditionIdList' => 'setDeleteConditionIdList',
        'modifyConditionList' => 'setModifyConditionList',
        'openRuleId' => 'setOpenRuleId',
        'paymentPolicy' => 'setPaymentPolicy',
        'personalQrcodeMode' => 'setPersonalQrcodeMode',
        'standardDesc' => 'setStandardDesc',
        'standardId' => 'setStandardId',
        'standardName' => 'setStandardName'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'addConditionList' => 'getAddConditionList',
        'consumeMode' => 'getConsumeMode',
        'deleteConditionIdList' => 'getDeleteConditionIdList',
        'modifyConditionList' => 'getModifyConditionList',
        'openRuleId' => 'getOpenRuleId',
        'paymentPolicy' => 'getPaymentPolicy',
        'personalQrcodeMode' => 'getPersonalQrcodeMode',
        'standardDesc' => 'getStandardDesc',
        'standardId' => 'getStandardId',
        'standardName' => 'getStandardName'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['addConditionList'] = $data['addConditionList'] ?? null;
        $this->container['consumeMode'] = $data['consumeMode'] ?? null;
        $this->container['deleteConditionIdList'] = $data['deleteConditionIdList'] ?? null;
        $this->container['modifyConditionList'] = $data['modifyConditionList'] ?? null;
        $this->container['openRuleId'] = $data['openRuleId'] ?? null;
        $this->container['paymentPolicy'] = $data['paymentPolicy'] ?? null;
        $this->container['personalQrcodeMode'] = $data['personalQrcodeMode'] ?? null;
        $this->container['standardDesc'] = $data['standardDesc'] ?? null;
        $this->container['standardId'] = $data['standardId'] ?? null;
        $this->container['standardName'] = $data['standardName'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets addConditionList
     *
     * @return \Alipay\OpenAPISDK\Model\StandardConditionInfo[]|null
     */
    public function getAddConditionList()
    {
        return $this->container['addConditionList'];
    }

    /**
     * Sets addConditionList
     *
     * @param \Alipay\OpenAPISDK\Model\StandardConditionInfo[]|null $addConditionList 要添加的条件列表
     *
     * @return self
     */
    public function setAddConditionList($addConditionList)
    {
        $this->container['addConditionList'] = $addConditionList;

        return $this;
    }

    /**
     * Gets consumeMode
     *
     * @return string|null
     */
    public function getConsumeMode()
    {
        return $this->container['consumeMode'];
    }

    /**
     * Sets consumeMode
     *
     * @param string|null $consumeMode 消费模式
     *
     * @return self
     */
    public function setConsumeMode($consumeMode)
    {
        $this->container['consumeMode'] = $consumeMode;

        return $this;
    }

    /**
     * Gets deleteConditionIdList
     *
     * @return string[]|null
     */
    public function getDeleteConditionIdList()
    {
        return $this->container['deleteConditionIdList'];
    }

    /**
     * Sets deleteConditionIdList
     *
     * @param string[]|null $deleteConditionIdList 待删除的条件id列表
     *
     * @return self
     */
    public function setDeleteConditionIdList($deleteConditionIdList)
    {
        $this->container['deleteConditionIdList'] = $deleteConditionIdList;

        return $this;
    }

    /**
     * Gets modifyConditionList
     *
     * @return \Alipay\OpenAPISDK\Model\StandardConditionInfo[]|null
     */
    public function getModifyConditionList()
    {
        return $this->container['modifyConditionList'];
    }

    /**
     * Sets modifyConditionList
     *
     * @param \Alipay\OpenAPISDK\Model\StandardConditionInfo[]|null $modifyConditionList 修改的条件列表
     *
     * @return self
     */
    public function setModifyConditionList($modifyConditionList)
    {
        $this->container['modifyConditionList'] = $modifyConditionList;

        return $this;
    }

    /**
     * Gets openRuleId
     *
     * @return string|null
     */
    public function getOpenRuleId()
    {
        return $this->container['openRuleId'];
    }

    /**
     * Sets openRuleId
     *
     * @param string|null $openRuleId 使用规则绑定的开票规则id
     *
     * @return self
     */
    public function setOpenRuleId($openRuleId)
    {
        $this->container['openRuleId'] = $openRuleId;

        return $this;
    }

    /**
     * Gets paymentPolicy
     *
     * @return string|null
     */
    public function getPaymentPolicy()
    {
        return $this->container['paymentPolicy'];
    }

    /**
     * Sets paymentPolicy
     *
     * @param string|null $paymentPolicy 支付策略 当笔消费金额大于规则可用余额时，用于控制支付策略。COMBINATION表示支持因公资产和个人资产组合支付，PERSONAL表示整单个人支付。
     *
     * @return self
     */
    public function setPaymentPolicy($paymentPolicy)
    {
        $this->container['paymentPolicy'] = $paymentPolicy;

        return $this;
    }

    /**
     * Gets personalQrcodeMode
     *
     * @return int|null
     */
    public function getPersonalQrcodeMode()
    {
        return $this->container['personalQrcodeMode'];
    }

    /**
     * Sets personalQrcodeMode
     *
     * @param int|null $personalQrcodeMode 个人收款码转账是否支持因公付。可选值：0（不支持）、1（支持）
     *
     * @return self
     */
    public function setPersonalQrcodeMode($personalQrcodeMode)
    {
        $this->container['personalQrcodeMode'] = $personalQrcodeMode;

        return $this;
    }

    /**
     * Gets standardDesc
     *
     * @return string|null
     */
    public function getStandardDesc()
    {
        return $this->container['standardDesc'];
    }

    /**
     * Sets standardDesc
     *
     * @param string|null $standardDesc 使用规则描述
     *
     * @return self
     */
    public function setStandardDesc($standardDesc)
    {
        $this->container['standardDesc'] = $standardDesc;

        return $this;
    }

    /**
     * Gets standardId
     *
     * @return string|null
     */
    public function getStandardId()
    {
        return $this->container['standardId'];
    }

    /**
     * Sets standardId
     *
     * @param string|null $standardId 修改的使用规则id
     *
     * @return self
     */
    public function setStandardId($standardId)
    {
        $this->container['standardId'] = $standardId;

        return $this;
    }

    /**
     * Gets standardName
     *
     * @return string|null
     */
    public function getStandardName()
    {
        return $this->container['standardName'];
    }

    /**
     * Sets standardName
     *
     * @param string|null $standardName 使用规则名称
     *
     * @return self
     */
    public function setStandardName($standardName)
    {
        $this->container['standardName'] = $standardName;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


