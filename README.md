# 积分下载器

基于积分制的资源下载器，集成支付宝当面付功能。

## 功能特性

- 🎯 **积分制下载** - 用积分下载资源，简单直观
- 💰 **支付宝充值** - 支持支付宝当面付充值积分
- 🚀 **多平台支持** - 支持多个资源平台
- ⚡ **断点续传** - 支持下载中断后继续下载
- 🔐 **用户认证** - 完整的用户注册登录系统
- 📊 **下载管理** - 完整的下载历史和积分管理
- 🎨 **友好界面** - 基于Rich的美观命令行界面

## 系统要求

- Python 3.8+
- Windows / Linux / macOS
- 网络连接

## 安装方法

### 方法一：直接运行

1. 克隆或下载项目
2. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```
3. 运行程序：
   ```bash
   python main.py
   ```

### 方法二：安装包

```bash
pip install -e .
points-downloader
```

## 配置说明

### 客户端配置

编辑 `config.json` 文件：

```json
{
    "server": {
        "base_url": "https://mengmeng.kechengmao.top/",
        "api_base": "https://mengmeng.kechengmao.top/api/"
    },
    "download": {
        "default_path": "downloads/",
        "max_concurrent": 3
    }
}
```

### 服务端配置

编辑 `server/includes/config.php` 文件：

```php
// 数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'your_database');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');

// 支付宝配置
define('ALIPAY_APP_ID', 'your_app_id');
define('ALIPAY_PRIVATE_KEY', 'your_private_key');
define('ALIPAY_PUBLIC_KEY', 'alipay_public_key');
```

## 使用方法

### 1. 用户注册/登录

首次使用需要注册账户：

```
选择操作 (1-登录, 2-注册, 0-退出): 2
用户名: your_username
密码: your_password
邮箱（可选）: <EMAIL>
```

### 2. 浏览资源

登录后选择"浏览资源"查看可下载的资源列表。

### 3. 充值积分

选择"充值积分"，输入金额后扫码支付：

```
请输入充值金额: 10
确认充值 10.00 元，获得 10 积分？ [y/N]: y
```

### 4. 下载资源

在资源列表中输入序号下载对应资源。

## 积分规则

- 1元 = 1积分
- 不同资源消耗不同积分
- 下载失败自动退还积分
- 注册赠送10积分

## 支付方式

目前支持：
- 支付宝当面付（扫码支付）

## 开发指南

### 添加新平台

1. 在 `platforms/` 目录下创建新平台目录
2. 继承 `BaseDownloader` 类
3. 实现必要的方法：

```python
from core.downloader import BaseDownloader

class YourPlatformDownloader(BaseDownloader):
    def get_platform_name(self):
        return "您的平台名称"
    
    def search_resources(self, keyword, **kwargs):
        # 实现搜索逻辑
        pass
```

### API接口

服务端提供完整的RESTful API：

- `POST /api/user/register` - 用户注册
- `POST /api/user/login` - 用户登录
- `GET /api/points/balance` - 获取积分余额
- `POST /api/payment/create` - 创建支付订单
- `GET /api/resources/list` - 获取资源列表

详细API文档请参考源码中的注释。

## 目录结构

```
积分下载器/
├── main.py                  # 主程序入口
├── config.json              # 配置文件
├── requirements.txt         # 依赖包列表
├── core/                    # 核心模块
│   ├── points.py            # 积分管理
│   ├── payment.py           # 支付处理
│   ├── downloader.py        # 下载器基类
│   └── display.py           # 界面显示
├── platforms/               # 平台实现
│   └── demo/                # 示例平台
├── server/                  # 服务端代码
│   ├── api/                 # API接口
│   ├── includes/            # 核心类库
│   └── admin/               # 管理后台
└── downloads/               # 下载文件目录
```

## 常见问题

### Q: 下载失败怎么办？
A: 系统会自动退还消费的积分，可以重新尝试下载。

### Q: 支付后积分没到账？
A: 请检查网络连接，或联系客服处理。

### Q: 如何添加新的下载平台？
A: 参考 `platforms/demo/` 示例，继承 `BaseDownloader` 类实现。

## 技术支持

- 官网：https://mengmeng.kechengmao.top/
- 邮箱：<EMAIL>

## 开源协议

MIT License

## 更新日志

### v1.0.0 (2024-12-19)
- 初始版本发布
- 实现积分制下载系统
- 集成支付宝当面付功能
- 支持多平台资源下载
- 完整的用户管理系统
- 断点续传下载功能

## 贡献指南

欢迎提交Issue和Pull Request！

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 致谢

感谢所有贡献者和用户的支持！
