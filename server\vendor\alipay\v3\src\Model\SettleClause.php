<?php
/**
 * SettleClause
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * SettleClause Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class SettleClause implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'SettleClause';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'amount' => 'string',
        'currency' => 'string',
        'settleAccountEntity' => 'string',
        'settleAccountId' => 'string',
        'settleAccountIdType' => 'string',
        'settleAccountType' => 'string',
        'settleEntityBizType' => 'string',
        'settleEntityId' => 'string',
        'settleEntityType' => 'string',
        'subMerchant' => '\Alipay\OpenAPISDK\Model\SubMerchant'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'amount' => null,
        'currency' => null,
        'settleAccountEntity' => null,
        'settleAccountId' => null,
        'settleAccountIdType' => null,
        'settleAccountType' => null,
        'settleEntityBizType' => null,
        'settleEntityId' => null,
        'settleEntityType' => null,
        'subMerchant' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'amount' => 'amount',
        'currency' => 'currency',
        'settleAccountEntity' => 'settle_account_entity',
        'settleAccountId' => 'settle_account_id',
        'settleAccountIdType' => 'settle_account_id_type',
        'settleAccountType' => 'settle_account_type',
        'settleEntityBizType' => 'settle_entity_biz_type',
        'settleEntityId' => 'settle_entity_id',
        'settleEntityType' => 'settle_entity_type',
        'subMerchant' => 'sub_merchant'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'amount' => 'setAmount',
        'currency' => 'setCurrency',
        'settleAccountEntity' => 'setSettleAccountEntity',
        'settleAccountId' => 'setSettleAccountId',
        'settleAccountIdType' => 'setSettleAccountIdType',
        'settleAccountType' => 'setSettleAccountType',
        'settleEntityBizType' => 'setSettleEntityBizType',
        'settleEntityId' => 'setSettleEntityId',
        'settleEntityType' => 'setSettleEntityType',
        'subMerchant' => 'setSubMerchant'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'amount' => 'getAmount',
        'currency' => 'getCurrency',
        'settleAccountEntity' => 'getSettleAccountEntity',
        'settleAccountId' => 'getSettleAccountId',
        'settleAccountIdType' => 'getSettleAccountIdType',
        'settleAccountType' => 'getSettleAccountType',
        'settleEntityBizType' => 'getSettleEntityBizType',
        'settleEntityId' => 'getSettleEntityId',
        'settleEntityType' => 'getSettleEntityType',
        'subMerchant' => 'getSubMerchant'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['amount'] = $data['amount'] ?? null;
        $this->container['currency'] = $data['currency'] ?? null;
        $this->container['settleAccountEntity'] = $data['settleAccountEntity'] ?? null;
        $this->container['settleAccountId'] = $data['settleAccountId'] ?? null;
        $this->container['settleAccountIdType'] = $data['settleAccountIdType'] ?? null;
        $this->container['settleAccountType'] = $data['settleAccountType'] ?? null;
        $this->container['settleEntityBizType'] = $data['settleEntityBizType'] ?? null;
        $this->container['settleEntityId'] = $data['settleEntityId'] ?? null;
        $this->container['settleEntityType'] = $data['settleEntityType'] ?? null;
        $this->container['subMerchant'] = $data['subMerchant'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets amount
     *
     * @return string|null
     */
    public function getAmount()
    {
        return $this->container['amount'];
    }

    /**
     * Sets amount
     *
     * @param string|null $amount 结算金额，单位为元
     *
     * @return self
     */
    public function setAmount($amount)
    {
        $this->container['amount'] = $amount;

        return $this;
    }

    /**
     * Gets currency
     *
     * @return string|null
     */
    public function getCurrency()
    {
        return $this->container['currency'];
    }

    /**
     * Sets currency
     *
     * @param string|null $currency 结算币种，支持人民币：CNY
     *
     * @return self
     */
    public function setCurrency($currency)
    {
        $this->container['currency'] = $currency;

        return $this;
    }

    /**
     * Gets settleAccountEntity
     *
     * @return string|null
     */
    public function getSettleAccountEntity()
    {
        return $this->container['settleAccountEntity'];
    }

    /**
     * Sets settleAccountEntity
     *
     * @param string|null $settleAccountEntity 结算账号所属于的结算主体类型。 secondMerchant 二级商户的结算账号 store 门店的结算账号 如果不填，默认为settleEntityType对应的类型
     *
     * @return self
     */
    public function setSettleAccountEntity($settleAccountEntity)
    {
        $this->container['settleAccountEntity'] = $settleAccountEntity;

        return $this;
    }

    /**
     * Gets settleAccountId
     *
     * @return string|null
     */
    public function getSettleAccountId()
    {
        return $this->container['settleAccountId'];
    }

    /**
     * Sets settleAccountId
     *
     * @param string|null $settleAccountId 结算账户id。  当结算账户id类型是cardSerialNo时，本参数为用户在支付宝绑定的卡编号；当结算账户id类型是userId时，本参数为用户的支付宝账号对应的支付宝唯一用户号；当结算账户id类型是loginName时，本参数为用户的支付宝登录号; 当 settle_account_type 为defaultSettle时，本参数必须为空
     *
     * @return self
     */
    public function setSettleAccountId($settleAccountId)
    {
        $this->container['settleAccountId'] = $settleAccountId;

        return $this;
    }

    /**
     * Gets settleAccountIdType
     *
     * @return string|null
     */
    public function getSettleAccountIdType()
    {
        return $this->container['settleAccountIdType'];
    }

    /**
     * Sets settleAccountIdType
     *
     * @param string|null $settleAccountIdType 结算账户id类型。当 settle_account_type 为 defaultSettle 时，本参数必须为空。  当settle_account_type 为bankCard时，本参数为cardSerialNo，表示结算账户id是银行卡编号; 当settle_account_type 为alipayBalance时，本参数为userId或者loginName，其中userId表示结算账户id是支付宝唯一用户号，loginName表示结算账户id是支付宝登录号。
     *
     * @return self
     */
    public function setSettleAccountIdType($settleAccountIdType)
    {
        $this->container['settleAccountIdType'] = $settleAccountIdType;

        return $this;
    }

    /**
     * Gets settleAccountType
     *
     * @return string|null
     */
    public function getSettleAccountType()
    {
        return $this->container['settleAccountType'];
    }

    /**
     * Sets settleAccountType
     *
     * @param string|null $settleAccountType 结算账户类型。  bankCard: 结算账户为银行卡； alipayBalance: 结算账户为支付宝余额户； defaultSettle: 按默认结算规则结算
     *
     * @return self
     */
    public function setSettleAccountType($settleAccountType)
    {
        $this->container['settleAccountType'] = $settleAccountType;

        return $this;
    }

    /**
     * Gets settleEntityBizType
     *
     * @return string|null
     */
    public function getSettleEntityBizType()
    {
        return $this->container['settleEntityBizType'];
    }

    /**
     * Sets settleEntityBizType
     *
     * @param string|null $settleEntityBizType 已结算资产
     *
     * @return self
     */
    public function setSettleEntityBizType($settleEntityBizType)
    {
        $this->container['settleEntityBizType'] = $settleEntityBizType;

        return $this;
    }

    /**
     * Gets settleEntityId
     *
     * @return string|null
     */
    public function getSettleEntityId()
    {
        return $this->container['settleEntityId'];
    }

    /**
     * Sets settleEntityId
     *
     * @param string|null $settleEntityId 结算主体账号。 当结算主体类型为SecondMerchant，传二级商户ID(smid)；结算主体类型为Store时，传门店ID
     *
     * @return self
     */
    public function setSettleEntityId($settleEntityId)
    {
        $this->container['settleEntityId'] = $settleEntityId;

        return $this;
    }

    /**
     * Gets settleEntityType
     *
     * @return string|null
     */
    public function getSettleEntityType()
    {
        return $this->container['settleEntityType'];
    }

    /**
     * Sets settleEntityType
     *
     * @param string|null $settleEntityType 结算主体类型。 SecondMerchant：结算主体为二级商户 Store：结算主体为门店
     *
     * @return self
     */
    public function setSettleEntityType($settleEntityType)
    {
        $this->container['settleEntityType'] = $settleEntityType;

        return $this;
    }

    /**
     * Gets subMerchant
     *
     * @return \Alipay\OpenAPISDK\Model\SubMerchant|null
     */
    public function getSubMerchant()
    {
        return $this->container['subMerchant'];
    }

    /**
     * Sets subMerchant
     *
     * @param \Alipay\OpenAPISDK\Model\SubMerchant|null $subMerchant subMerchant
     *
     * @return self
     */
    public function setSubMerchant($subMerchant)
    {
        $this->container['subMerchant'] = $subMerchant;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


