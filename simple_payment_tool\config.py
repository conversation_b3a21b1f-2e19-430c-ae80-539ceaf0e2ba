#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化收款工具配置文件
版本: 1.0.0
创建时间: 2024-12-19
"""

import os
from typing import Dict, Any

class Config:
    """配置管理类"""
    
    # 服务器配置
    API_BASE_URL = "https://mengmeng.kechengmao.top/api"
    
    # 认证配置
    # 注意：这里需要一个有效的用户token或者创建一个专用的API端点
    # 您可以：
    # 1. 使用现有用户的token
    # 2. 创建一个不需要认证的支付API端点
    # 3. 创建一个专用的收款工具token
    USER_TOKEN = "your_user_token_here"  # 请替换为实际的token
    
    # 二维码配置
    QR_CODE_SIZE = 300  # 二维码图片大小（像素）
    QR_CODE_BORDER = 4  # 二维码边框大小
    
    # 支付监控配置
    PAYMENT_TIMEOUT = 300  # 支付超时时间（秒）
    CHECK_INTERVAL = 3     # 状态检查间隔（秒）
    
    # 文件保存配置
    SAVE_QR_CODES = True   # 是否保存二维码图片
    QR_SAVE_DIR = "qr_codes"  # 二维码保存目录
    
    # 显示配置
    AUTO_OPEN_QR = True    # 是否自动打开二维码图片
    SHOW_ASCII_QR = False  # 是否显示ASCII二维码（备用方案）
    
    # 金额限制配置
    MIN_AMOUNT = 0.01      # 最小金额
    MAX_AMOUNT = 10000.0   # 最大金额
    
    # 日志配置
    ENABLE_LOGGING = True  # 是否启用日志
    LOG_LEVEL = "INFO"     # 日志级别
    
    @classmethod
    def get_api_url(cls) -> str:
        """获取API基础URL"""
        return cls.API_BASE_URL.rstrip('/')
    
    @classmethod
    def get_token(cls) -> str:
        """获取用户token"""
        # 优先从环境变量获取
        token = os.getenv('PAYMENT_TOKEN')
        if token:
            return token
        return cls.USER_TOKEN
    
    @classmethod
    def get_qr_save_path(cls, order_number: str) -> str:
        """获取二维码保存路径"""
        if not cls.SAVE_QR_CODES:
            return None
        
        # 确保目录存在
        os.makedirs(cls.QR_SAVE_DIR, exist_ok=True)
        
        return os.path.join(cls.QR_SAVE_DIR, f"payment_{order_number}.png")
    
    @classmethod
    def validate_amount(cls, amount: float) -> bool:
        """验证金额是否在允许范围内"""
        return cls.MIN_AMOUNT <= amount <= cls.MAX_AMOUNT
    
    @classmethod
    def to_dict(cls) -> Dict[str, Any]:
        """将配置转换为字典"""
        return {
            'api_base_url': cls.API_BASE_URL,
            'user_token': cls.USER_TOKEN,
            'qr_code_size': cls.QR_CODE_SIZE,
            'qr_code_border': cls.QR_CODE_BORDER,
            'payment_timeout': cls.PAYMENT_TIMEOUT,
            'check_interval': cls.CHECK_INTERVAL,
            'save_qr_codes': cls.SAVE_QR_CODES,
            'qr_save_dir': cls.QR_SAVE_DIR,
            'auto_open_qr': cls.AUTO_OPEN_QR,
            'show_ascii_qr': cls.SHOW_ASCII_QR,
            'min_amount': cls.MIN_AMOUNT,
            'max_amount': cls.MAX_AMOUNT,
            'enable_logging': cls.ENABLE_LOGGING,
            'log_level': cls.LOG_LEVEL
        }

# 创建全局配置实例
config = Config()

# 配置说明
CONFIG_HELP = """
配置说明：

1. API_BASE_URL: 支付API的基础URL
   - 默认: https://mengmeng.kechengmao.top/api
   - 请确保URL正确且服务器可访问

2. USER_TOKEN: 用户认证token
   - 这是必需的配置项
   - 您可以：
     a) 从现有系统获取用户token
     b) 设置环境变量 PAYMENT_TOKEN
     c) 创建专用的收款工具token

3. 二维码配置:
   - QR_CODE_SIZE: 二维码图片大小（像素）
   - QR_CODE_BORDER: 二维码边框大小

4. 支付监控配置:
   - PAYMENT_TIMEOUT: 支付超时时间（秒）
   - CHECK_INTERVAL: 状态检查间隔（秒）

5. 文件保存配置:
   - SAVE_QR_CODES: 是否保存二维码图片
   - QR_SAVE_DIR: 二维码保存目录

6. 显示配置:
   - AUTO_OPEN_QR: 是否自动打开二维码图片
   - SHOW_ASCII_QR: 是否显示ASCII二维码

7. 金额限制:
   - MIN_AMOUNT: 最小金额
   - MAX_AMOUNT: 最大金额

使用环境变量：
您可以通过设置环境变量来覆盖配置：
- PAYMENT_TOKEN: 用户认证token

示例：
export PAYMENT_TOKEN="your_actual_token_here"
python main.py
"""

__all__ = ['Config', 'config', 'CONFIG_HELP']
