<?php
/**
 * 支付宝回调通知API
 * 版本: 1.0.0
 * 创建时间: 2024-12-19
 */

require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/Payment.php';

// 验证请求方法
validateRequestMethod(['POST']);

try {
    // 获取支付宝回调数据
    $notifyData = $_POST;

    if (empty($notifyData)) {
        writeLog("支付宝回调数据为空", 'ERROR');
        echo 'fail';
        exit;
    }

    // 记录原始回调数据
    writeLog("收到支付宝回调: " . json_encode($notifyData), 'INFO');
    
    // 验证必需参数
    $requiredParams = ['out_trade_no', 'trade_no', 'trade_status', 'total_amount'];
    foreach ($requiredParams as $param) {
        if (!isset($notifyData[$param])) {
            writeLog("支付宝回调缺少必需参数: $param", 'ERROR');
            echo 'fail';
            exit;
        }
    }
    
    // 获取订单信息
    $orderNumber = $notifyData['out_trade_no'];
    $db = getDB();
    $order = $db->selectOne('orders', ['order_number' => $orderNumber]);
    
    if (!$order) {
        writeLog("支付宝回调订单不存在: $orderNumber", 'ERROR');
        echo 'fail';
        exit;
    }
    
    // 处理支付回调
    $payment = new Payment($order['user_id']);
    $result = $payment->handleAlipayNotify($notifyData);
    
    if ($result['success']) {
        writeLog("支付宝回调处理成功: 订单号 $orderNumber", 'INFO');
        echo 'success';
    } else {
        writeLog("支付宝回调处理失败: 订单号 $orderNumber", 'ERROR');
        echo 'fail';
    }
    
} catch (Exception $e) {
    // 记录错误日志
    writeLog("支付宝回调处理异常: " . $e->getMessage(), 'ERROR');
    echo 'fail';
}

?>
