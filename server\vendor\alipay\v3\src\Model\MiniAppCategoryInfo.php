<?php
/**
 * MiniAppCategoryInfo
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * MiniAppCategoryInfo Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class MiniAppCategoryInfo implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'MiniAppCategoryInfo';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'firstCategoryId' => 'string',
        'firstCategoryName' => 'string',
        'secondCategoryId' => 'string',
        'secondCategoryName' => 'string',
        'thirdCategoryId' => 'string',
        'thirdCategoryName' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'firstCategoryId' => null,
        'firstCategoryName' => null,
        'secondCategoryId' => null,
        'secondCategoryName' => null,
        'thirdCategoryId' => null,
        'thirdCategoryName' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'firstCategoryId' => 'first_category_id',
        'firstCategoryName' => 'first_category_name',
        'secondCategoryId' => 'second_category_id',
        'secondCategoryName' => 'second_category_name',
        'thirdCategoryId' => 'third_category_id',
        'thirdCategoryName' => 'third_category_name'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'firstCategoryId' => 'setFirstCategoryId',
        'firstCategoryName' => 'setFirstCategoryName',
        'secondCategoryId' => 'setSecondCategoryId',
        'secondCategoryName' => 'setSecondCategoryName',
        'thirdCategoryId' => 'setThirdCategoryId',
        'thirdCategoryName' => 'setThirdCategoryName'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'firstCategoryId' => 'getFirstCategoryId',
        'firstCategoryName' => 'getFirstCategoryName',
        'secondCategoryId' => 'getSecondCategoryId',
        'secondCategoryName' => 'getSecondCategoryName',
        'thirdCategoryId' => 'getThirdCategoryId',
        'thirdCategoryName' => 'getThirdCategoryName'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['firstCategoryId'] = $data['firstCategoryId'] ?? null;
        $this->container['firstCategoryName'] = $data['firstCategoryName'] ?? null;
        $this->container['secondCategoryId'] = $data['secondCategoryId'] ?? null;
        $this->container['secondCategoryName'] = $data['secondCategoryName'] ?? null;
        $this->container['thirdCategoryId'] = $data['thirdCategoryId'] ?? null;
        $this->container['thirdCategoryName'] = $data['thirdCategoryName'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets firstCategoryId
     *
     * @return string|null
     */
    public function getFirstCategoryId()
    {
        return $this->container['firstCategoryId'];
    }

    /**
     * Sets firstCategoryId
     *
     * @param string|null $firstCategoryId 一级类目id
     *
     * @return self
     */
    public function setFirstCategoryId($firstCategoryId)
    {
        $this->container['firstCategoryId'] = $firstCategoryId;

        return $this;
    }

    /**
     * Gets firstCategoryName
     *
     * @return string|null
     */
    public function getFirstCategoryName()
    {
        return $this->container['firstCategoryName'];
    }

    /**
     * Sets firstCategoryName
     *
     * @param string|null $firstCategoryName 一级类目名称
     *
     * @return self
     */
    public function setFirstCategoryName($firstCategoryName)
    {
        $this->container['firstCategoryName'] = $firstCategoryName;

        return $this;
    }

    /**
     * Gets secondCategoryId
     *
     * @return string|null
     */
    public function getSecondCategoryId()
    {
        return $this->container['secondCategoryId'];
    }

    /**
     * Sets secondCategoryId
     *
     * @param string|null $secondCategoryId 二级类目id
     *
     * @return self
     */
    public function setSecondCategoryId($secondCategoryId)
    {
        $this->container['secondCategoryId'] = $secondCategoryId;

        return $this;
    }

    /**
     * Gets secondCategoryName
     *
     * @return string|null
     */
    public function getSecondCategoryName()
    {
        return $this->container['secondCategoryName'];
    }

    /**
     * Sets secondCategoryName
     *
     * @param string|null $secondCategoryName 二级类目名称
     *
     * @return self
     */
    public function setSecondCategoryName($secondCategoryName)
    {
        $this->container['secondCategoryName'] = $secondCategoryName;

        return $this;
    }

    /**
     * Gets thirdCategoryId
     *
     * @return string|null
     */
    public function getThirdCategoryId()
    {
        return $this->container['thirdCategoryId'];
    }

    /**
     * Sets thirdCategoryId
     *
     * @param string|null $thirdCategoryId 三级类目id，可空
     *
     * @return self
     */
    public function setThirdCategoryId($thirdCategoryId)
    {
        $this->container['thirdCategoryId'] = $thirdCategoryId;

        return $this;
    }

    /**
     * Gets thirdCategoryName
     *
     * @return string|null
     */
    public function getThirdCategoryName()
    {
        return $this->container['thirdCategoryName'];
    }

    /**
     * Sets thirdCategoryName
     *
     * @param string|null $thirdCategoryName 三级类目名称，可空
     *
     * @return self
     */
    public function setThirdCategoryName($thirdCategoryName)
    {
        $this->container['thirdCategoryName'] = $thirdCategoryName;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


