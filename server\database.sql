-- 积分下载器数据库结构
-- 版本: 1.0.0
-- 创建时间: 2024-12-19

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 用户表
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码哈希',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `token` varchar(255) DEFAULT NULL COMMENT '登录令牌',
  `token_expires` timestamp NULL DEFAULT NULL COMMENT '令牌过期时间',
  `last_login` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `status` enum('active','disabled','banned') NOT NULL DEFAULT 'active' COMMENT '用户状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_token` (`token`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 积分账户表
DROP TABLE IF EXISTS `points`;
CREATE TABLE `points` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `balance` int(11) NOT NULL DEFAULT '0' COMMENT '积分余额',
  `total_recharged` int(11) NOT NULL DEFAULT '0' COMMENT '累计充值积分',
  `total_consumed` int(11) NOT NULL DEFAULT '0' COMMENT '累计消费积分',
  `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  CONSTRAINT `fk_points_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分账户表';

-- 积分交易表
DROP TABLE IF EXISTS `point_transactions`;
CREATE TABLE `point_transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '交易ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `amount` int(11) NOT NULL COMMENT '积分数量(正数为增加,负数为减少)',
  `transaction_type` enum('recharge','consume','refund','gift','admin') NOT NULL COMMENT '交易类型',
  `resource_id` varchar(100) DEFAULT NULL COMMENT '关联资源ID',
  `order_id` varchar(100) DEFAULT NULL COMMENT '关联订单ID',
  `description` text COMMENT '交易描述',
  `balance_before` int(11) NOT NULL COMMENT '交易前余额',
  `balance_after` int(11) NOT NULL COMMENT '交易后余额',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_transaction_type` (`transaction_type`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_transactions_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分交易表';

-- 订单表
DROP TABLE IF EXISTS `orders`;
CREATE TABLE `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_number` varchar(50) NOT NULL COMMENT '订单号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `points` int(11) NOT NULL COMMENT '获得积分',
  `status` enum('pending','completed','failed','cancelled','expired') NOT NULL DEFAULT 'pending' COMMENT '订单状态',
  `payment_method` varchar(20) NOT NULL DEFAULT 'alipay' COMMENT '支付方式',
  `trade_no` varchar(100) DEFAULT NULL COMMENT '支付宝交易号',
  `qr_code` text COMMENT '支付二维码',
  `notify_data` text COMMENT '支付回调数据',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `expires_at` timestamp NULL DEFAULT NULL COMMENT '订单过期时间',
  `paid_at` timestamp NULL DEFAULT NULL COMMENT '支付完成时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_number` (`order_number`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_trade_no` (`trade_no`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_orders_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 资源表
DROP TABLE IF EXISTS `resources`;
CREATE TABLE `resources` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '资源ID',
  `name` varchar(255) NOT NULL COMMENT '资源名称',
  `platform` varchar(50) NOT NULL COMMENT '平台名称',
  `resource_id` varchar(100) NOT NULL COMMENT '平台资源ID',
  `points_cost` int(11) NOT NULL DEFAULT '0' COMMENT '所需积分',
  `download_count` int(11) NOT NULL DEFAULT '0' COMMENT '下载次数',
  `resource_type` varchar(50) DEFAULT NULL COMMENT '资源类型',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小(字节)',
  `duration` int(11) DEFAULT NULL COMMENT '时长(秒)',
  `preview_url` varchar(255) DEFAULT NULL COMMENT '预览图URL',
  `download_url` text COMMENT '下载链接',
  `description` text COMMENT '资源描述',
  `tags` varchar(500) DEFAULT NULL COMMENT '标签(逗号分隔)',
  `status` enum('active','disabled','deleted') NOT NULL DEFAULT 'active' COMMENT '资源状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `platform_resource` (`platform`,`resource_id`),
  KEY `idx_platform` (`platform`),
  KEY `idx_points_cost` (`points_cost`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资源表';

-- 下载记录表
DROP TABLE IF EXISTS `downloads`;
CREATE TABLE `downloads` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '下载ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `resource_id` int(11) NOT NULL COMMENT '资源ID',
  `points_cost` int(11) NOT NULL COMMENT '消费积分',
  `download_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '下载时间',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `status` enum('success','failed','in_progress','cancelled') NOT NULL COMMENT '下载状态',
  `file_path` varchar(500) DEFAULT NULL COMMENT '本地文件路径',
  `error_message` text COMMENT '错误信息',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_resource_id` (`resource_id`),
  KEY `idx_download_time` (`download_time`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_downloads_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_downloads_resource` FOREIGN KEY (`resource_id`) REFERENCES `resources` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='下载记录表';

-- 系统配置表
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 插入默认配置
INSERT INTO `system_config` (`config_key`, `config_value`, `description`) VALUES
('points_per_yuan', '1', '每元人民币对应的积分数'),
('register_gift_points', '10', '注册赠送积分'),
('order_expire_minutes', '30', '订单过期时间(分钟)'),
('max_download_concurrent', '3', '最大并发下载数'),
('site_name', '积分下载器', '网站名称'),
('site_url', 'https://mengmeng.kechengmao.top/', '网站地址');

-- 创建管理员账户 (密码: admin123)
INSERT INTO `users` (`username`, `password`, `email`, `status`) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'active');

-- 为管理员创建积分账户
INSERT INTO `points` (`user_id`, `balance`) VALUES (1, 1000);

SET FOREIGN_KEY_CHECKS = 1;
