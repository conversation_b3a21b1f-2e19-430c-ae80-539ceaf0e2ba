<?php
/**
 * ZhimaCustomerJobworthInfoQueryResponseModel
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * ZhimaCustomerJobworthInfoQueryResponseModel Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class ZhimaCustomerJobworthInfoQueryResponseModel implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'ZhimaCustomerJobworthInfoQueryResponseModel';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'acceptanceId' => 'string',
        'authToken' => 'string',
        'htmlUrl' => 'string',
        'pictureUrl' => 'string',
        'sceneResult' => 'bool',
        'subCode' => 'string',
        'subMsg' => 'string',
        'updateUrl' => 'string',
        'url' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'acceptanceId' => null,
        'authToken' => null,
        'htmlUrl' => null,
        'pictureUrl' => null,
        'sceneResult' => null,
        'subCode' => null,
        'subMsg' => null,
        'updateUrl' => null,
        'url' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'acceptanceId' => 'acceptance_id',
        'authToken' => 'auth_token',
        'htmlUrl' => 'html_url',
        'pictureUrl' => 'picture_url',
        'sceneResult' => 'scene_result',
        'subCode' => 'sub_code',
        'subMsg' => 'sub_msg',
        'updateUrl' => 'update_url',
        'url' => 'url'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'acceptanceId' => 'setAcceptanceId',
        'authToken' => 'setAuthToken',
        'htmlUrl' => 'setHtmlUrl',
        'pictureUrl' => 'setPictureUrl',
        'sceneResult' => 'setSceneResult',
        'subCode' => 'setSubCode',
        'subMsg' => 'setSubMsg',
        'updateUrl' => 'setUpdateUrl',
        'url' => 'setUrl'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'acceptanceId' => 'getAcceptanceId',
        'authToken' => 'getAuthToken',
        'htmlUrl' => 'getHtmlUrl',
        'pictureUrl' => 'getPictureUrl',
        'sceneResult' => 'getSceneResult',
        'subCode' => 'getSubCode',
        'subMsg' => 'getSubMsg',
        'updateUrl' => 'getUpdateUrl',
        'url' => 'getUrl'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['acceptanceId'] = $data['acceptanceId'] ?? null;
        $this->container['authToken'] = $data['authToken'] ?? null;
        $this->container['htmlUrl'] = $data['htmlUrl'] ?? null;
        $this->container['pictureUrl'] = $data['pictureUrl'] ?? null;
        $this->container['sceneResult'] = $data['sceneResult'] ?? null;
        $this->container['subCode'] = $data['subCode'] ?? null;
        $this->container['subMsg'] = $data['subMsg'] ?? null;
        $this->container['updateUrl'] = $data['updateUrl'] ?? null;
        $this->container['url'] = $data['url'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets acceptanceId
     *
     * @return string|null
     */
    public function getAcceptanceId()
    {
        return $this->container['acceptanceId'];
    }

    /**
     * Sets acceptanceId
     *
     * @param string|null $acceptanceId 受理台单号作为jsapi的一个参数。如果不使用jsapi可以忽略
     *
     * @return self
     */
    public function setAcceptanceId($acceptanceId)
    {
        $this->container['acceptanceId'] = $acceptanceId;

        return $this;
    }

    /**
     * Gets authToken
     *
     * @return string|null
     */
    public function getAuthToken()
    {
        return $this->container['authToken'];
    }

    /**
     * Sets authToken
     *
     * @param string|null $authToken 用于授权校验,授权之前需要通过token校验
     *
     * @return self
     */
    public function setAuthToken($authToken)
    {
        $this->container['authToken'] = $authToken;

        return $this;
    }

    /**
     * Gets htmlUrl
     *
     * @return string|null
     */
    public function getHtmlUrl()
    {
        return $this->container['htmlUrl'];
    }

    /**
     * Sets htmlUrl
     *
     * @param string|null $htmlUrl h5url
     *
     * @return self
     */
    public function setHtmlUrl($htmlUrl)
    {
        $this->container['htmlUrl'] = $htmlUrl;

        return $this;
    }

    /**
     * Gets pictureUrl
     *
     * @return string|null
     */
    public function getPictureUrl()
    {
        return $this->container['pictureUrl'];
    }

    /**
     * Sets pictureUrl
     *
     * @param string|null $pictureUrl 职得工作证图片url,可以直接用于展示
     *
     * @return self
     */
    public function setPictureUrl($pictureUrl)
    {
        $this->container['pictureUrl'] = $pictureUrl;

        return $this;
    }

    /**
     * Gets sceneResult
     *
     * @return bool|null
     */
    public function getSceneResult()
    {
        return $this->container['sceneResult'];
    }

    /**
     * Sets sceneResult
     *
     * @param bool|null $sceneResult 场景授权结果
     *
     * @return self
     */
    public function setSceneResult($sceneResult)
    {
        $this->container['sceneResult'] = $sceneResult;

        return $this;
    }

    /**
     * Gets subCode
     *
     * @return string|null
     */
    public function getSubCode()
    {
        return $this->container['subCode'];
    }

    /**
     * Sets subCode
     *
     * @param string|null $subCode 业务编码
     *
     * @return self
     */
    public function setSubCode($subCode)
    {
        $this->container['subCode'] = $subCode;

        return $this;
    }

    /**
     * Gets subMsg
     *
     * @return string|null
     */
    public function getSubMsg()
    {
        return $this->container['subMsg'];
    }

    /**
     * Sets subMsg
     *
     * @param string|null $subMsg 业务中文结果信息
     *
     * @return self
     */
    public function setSubMsg($subMsg)
    {
        $this->container['subMsg'] = $subMsg;

        return $this;
    }

    /**
     * Gets updateUrl
     *
     * @return string|null
     */
    public function getUpdateUrl()
    {
        return $this->container['updateUrl'];
    }

    /**
     * Sets updateUrl
     *
     * @param string|null $updateUrl 工作证图片更新的url,需要跳转到工作证小程序中进行更新
     *
     * @return self
     */
    public function setUpdateUrl($updateUrl)
    {
        $this->container['updateUrl'] = $updateUrl;

        return $this;
    }

    /**
     * Gets url
     *
     * @return string|null
     */
    public function getUrl()
    {
        return $this->container['url'];
    }

    /**
     * Sets url
     *
     * @param string|null $url 职得工作证跳转小程序链接
     *
     * @return self
     */
    public function setUrl($url)
    {
        $this->container['url'] = $url;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


