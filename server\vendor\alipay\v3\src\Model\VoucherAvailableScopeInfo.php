<?php
/**
 * VoucherAvailableScopeInfo
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * VoucherAvailableScopeInfo Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class VoucherAvailableScopeInfo implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'VoucherAvailableScopeInfo';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'voucherAvailableAccountInfo' => '\Alipay\OpenAPISDK\Model\VoucherAvailableAccountInfo',
        'voucherAvailableAppInfo' => '\Alipay\OpenAPISDK\Model\VoucherAvailableAppInfo',
        'voucherAvailableGeographyScopeInfo' => '\Alipay\OpenAPISDK\Model\VoucherAvailableGeographyScopeInfo',
        'voucherAvailableGoodsInfo' => '\Alipay\OpenAPISDK\Model\VoucherAvailableGoodsInfo',
        'voucherAvailableItemInfo' => '\Alipay\OpenAPISDK\Model\VoucherAvailableItemInfo'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'voucherAvailableAccountInfo' => null,
        'voucherAvailableAppInfo' => null,
        'voucherAvailableGeographyScopeInfo' => null,
        'voucherAvailableGoodsInfo' => null,
        'voucherAvailableItemInfo' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'voucherAvailableAccountInfo' => 'voucher_available_account_info',
        'voucherAvailableAppInfo' => 'voucher_available_app_info',
        'voucherAvailableGeographyScopeInfo' => 'voucher_available_geography_scope_info',
        'voucherAvailableGoodsInfo' => 'voucher_available_goods_info',
        'voucherAvailableItemInfo' => 'voucher_available_item_info'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'voucherAvailableAccountInfo' => 'setVoucherAvailableAccountInfo',
        'voucherAvailableAppInfo' => 'setVoucherAvailableAppInfo',
        'voucherAvailableGeographyScopeInfo' => 'setVoucherAvailableGeographyScopeInfo',
        'voucherAvailableGoodsInfo' => 'setVoucherAvailableGoodsInfo',
        'voucherAvailableItemInfo' => 'setVoucherAvailableItemInfo'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'voucherAvailableAccountInfo' => 'getVoucherAvailableAccountInfo',
        'voucherAvailableAppInfo' => 'getVoucherAvailableAppInfo',
        'voucherAvailableGeographyScopeInfo' => 'getVoucherAvailableGeographyScopeInfo',
        'voucherAvailableGoodsInfo' => 'getVoucherAvailableGoodsInfo',
        'voucherAvailableItemInfo' => 'getVoucherAvailableItemInfo'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['voucherAvailableAccountInfo'] = $data['voucherAvailableAccountInfo'] ?? null;
        $this->container['voucherAvailableAppInfo'] = $data['voucherAvailableAppInfo'] ?? null;
        $this->container['voucherAvailableGeographyScopeInfo'] = $data['voucherAvailableGeographyScopeInfo'] ?? null;
        $this->container['voucherAvailableGoodsInfo'] = $data['voucherAvailableGoodsInfo'] ?? null;
        $this->container['voucherAvailableItemInfo'] = $data['voucherAvailableItemInfo'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets voucherAvailableAccountInfo
     *
     * @return \Alipay\OpenAPISDK\Model\VoucherAvailableAccountInfo|null
     */
    public function getVoucherAvailableAccountInfo()
    {
        return $this->container['voucherAvailableAccountInfo'];
    }

    /**
     * Sets voucherAvailableAccountInfo
     *
     * @param \Alipay\OpenAPISDK\Model\VoucherAvailableAccountInfo|null $voucherAvailableAccountInfo voucherAvailableAccountInfo
     *
     * @return self
     */
    public function setVoucherAvailableAccountInfo($voucherAvailableAccountInfo)
    {
        $this->container['voucherAvailableAccountInfo'] = $voucherAvailableAccountInfo;

        return $this;
    }

    /**
     * Gets voucherAvailableAppInfo
     *
     * @return \Alipay\OpenAPISDK\Model\VoucherAvailableAppInfo|null
     */
    public function getVoucherAvailableAppInfo()
    {
        return $this->container['voucherAvailableAppInfo'];
    }

    /**
     * Sets voucherAvailableAppInfo
     *
     * @param \Alipay\OpenAPISDK\Model\VoucherAvailableAppInfo|null $voucherAvailableAppInfo voucherAvailableAppInfo
     *
     * @return self
     */
    public function setVoucherAvailableAppInfo($voucherAvailableAppInfo)
    {
        $this->container['voucherAvailableAppInfo'] = $voucherAvailableAppInfo;

        return $this;
    }

    /**
     * Gets voucherAvailableGeographyScopeInfo
     *
     * @return \Alipay\OpenAPISDK\Model\VoucherAvailableGeographyScopeInfo|null
     */
    public function getVoucherAvailableGeographyScopeInfo()
    {
        return $this->container['voucherAvailableGeographyScopeInfo'];
    }

    /**
     * Sets voucherAvailableGeographyScopeInfo
     *
     * @param \Alipay\OpenAPISDK\Model\VoucherAvailableGeographyScopeInfo|null $voucherAvailableGeographyScopeInfo voucherAvailableGeographyScopeInfo
     *
     * @return self
     */
    public function setVoucherAvailableGeographyScopeInfo($voucherAvailableGeographyScopeInfo)
    {
        $this->container['voucherAvailableGeographyScopeInfo'] = $voucherAvailableGeographyScopeInfo;

        return $this;
    }

    /**
     * Gets voucherAvailableGoodsInfo
     *
     * @return \Alipay\OpenAPISDK\Model\VoucherAvailableGoodsInfo|null
     */
    public function getVoucherAvailableGoodsInfo()
    {
        return $this->container['voucherAvailableGoodsInfo'];
    }

    /**
     * Sets voucherAvailableGoodsInfo
     *
     * @param \Alipay\OpenAPISDK\Model\VoucherAvailableGoodsInfo|null $voucherAvailableGoodsInfo voucherAvailableGoodsInfo
     *
     * @return self
     */
    public function setVoucherAvailableGoodsInfo($voucherAvailableGoodsInfo)
    {
        $this->container['voucherAvailableGoodsInfo'] = $voucherAvailableGoodsInfo;

        return $this;
    }

    /**
     * Gets voucherAvailableItemInfo
     *
     * @return \Alipay\OpenAPISDK\Model\VoucherAvailableItemInfo|null
     */
    public function getVoucherAvailableItemInfo()
    {
        return $this->container['voucherAvailableItemInfo'];
    }

    /**
     * Sets voucherAvailableItemInfo
     *
     * @param \Alipay\OpenAPISDK\Model\VoucherAvailableItemInfo|null $voucherAvailableItemInfo voucherAvailableItemInfo
     *
     * @return self
     */
    public function setVoucherAvailableItemInfo($voucherAvailableItemInfo)
    {
        $this->container['voucherAvailableItemInfo'] = $voucherAvailableItemInfo;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


