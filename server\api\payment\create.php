<?php
/**
 * 创建支付订单API
 * 版本: 1.0.0
 * 创建时间: 2024-12-19
 */

require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/User.php';
require_once '../../includes/Payment.php';

// 验证请求方法
validateRequestMethod(['POST']);

try {
    // 验证用户Token
    $token = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    if (empty($token)) {
        handleError('缺少授权令牌', API_AUTH_ERROR_CODE);
    }
    
    $user = new User();
    $userId = $user->validateToken($token);
    
    if (!$userId) {
        handleError('无效的授权令牌', API_AUTH_ERROR_CODE);
    }
    
    // 获取请求数据
    $data = getRequestData();
    
    // 验证必需参数
    if (!isset($data['amount']) || !is_numeric($data['amount'])) {
        handleError('支付金额必须是有效数字', API_ERROR_CODE);
    }
    
    $amount = (float)$data['amount'];
    if ($amount <= 0) {
        handleError('支付金额必须大于0', API_ERROR_CODE);
    }
    
    if ($amount > 10000) {
        handleError('单次充值金额不能超过10000元', API_ERROR_CODE);
    }
    
    // 计算积分数量
    $configResult = getDB()->selectOne('system_config', ['config_key' => 'points_per_yuan']);
    $pointsPerYuan = $configResult ? (int)$configResult['config_value'] : DEFAULT_POINTS_PER_YUAN;
    $points = $amount * $pointsPerYuan;
    
    // 创建支付订单
    $payment = new Payment($userId);
    $result = $payment->createOrder($amount, $points);
    
    // 记录订单创建日志
    writeLog("支付订单创建成功: 用户ID $userId, 订单号 {$result['order_number']}, 金额 $amount 元", 'INFO');
    
    // 返回成功响应
    jsonResponse([
        'order_id' => $result['order_id'],
        'order_number' => $result['order_number'],
        'amount' => $result['amount'],
        'points' => $result['points'],
        'qr_code' => $result['qr_code'],
        'expires_at' => $result['expires_at']
    ], API_SUCCESS_CODE, '订单创建成功');
    
} catch (Exception $e) {
    // 记录错误日志
    writeLog("创建支付订单失败: " . $e->getMessage(), 'ERROR');
    
    // 返回错误响应
    handleError($e->getMessage(), API_ERROR_CODE);
}

?>
