<?php
/**
 * UserRiskPrediction
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * UserRiskPrediction Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class UserRiskPrediction implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'UserRiskPrediction';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'phoneRecycleRiskLeve' => 'string',
        'refusedPaymentRiskLevel' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'phoneRecycleRiskLeve' => null,
        'refusedPaymentRiskLevel' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'phoneRecycleRiskLeve' => 'phone_recycle_risk_leve',
        'refusedPaymentRiskLevel' => 'refused_payment_risk_level'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'phoneRecycleRiskLeve' => 'setPhoneRecycleRiskLeve',
        'refusedPaymentRiskLevel' => 'setRefusedPaymentRiskLevel'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'phoneRecycleRiskLeve' => 'getPhoneRecycleRiskLeve',
        'refusedPaymentRiskLevel' => 'getRefusedPaymentRiskLevel'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['phoneRecycleRiskLeve'] = $data['phoneRecycleRiskLeve'] ?? null;
        $this->container['refusedPaymentRiskLevel'] = $data['refusedPaymentRiskLevel'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets phoneRecycleRiskLeve
     *
     * @return string|null
     */
    public function getPhoneRecycleRiskLeve()
    {
        return $this->container['phoneRecycleRiskLeve'];
    }

    /**
     * Sets phoneRecycleRiskLeve
     *
     * @param string|null $phoneRecycleRiskLeve 用户绑定手机号被二次放号风险等级。 NO_RESULT：手机号风险未入库 NO_RISK：用户绑定手机无二次放号风险，高置信度 LOW_RISK：用户绑定手机二次放号风险较小，商户自行决策是否相信。 HIGH_RISK：用户绑定手机已被放号，高置信度
     *
     * @return self
     */
    public function setPhoneRecycleRiskLeve($phoneRecycleRiskLeve)
    {
        $this->container['phoneRecycleRiskLeve'] = $phoneRecycleRiskLeve;

        return $this;
    }

    /**
     * Gets refusedPaymentRiskLevel
     *
     * @return string|null
     */
    public function getRefusedPaymentRiskLevel()
    {
        return $this->container['refusedPaymentRiskLevel'];
    }

    /**
     * Sets refusedPaymentRiskLevel
     *
     * @param string|null $refusedPaymentRiskLevel 用户拒付风险等级。 NO_SIGN：商户未签约。 NO_RESULT：未查询到账户信息。 LOW_RISK：用户拒付风险为低；处理建议：用户可以先享受服务，再进行支付。 MEDIUM_RISK：用户拒付风险为中；处理建议：根据业务场景客户自行判断提供或者不提供。 HIGH_RISK：用户拒付风险为高；处理建议：不建议先提供给用户服务。
     *
     * @return self
     */
    public function setRefusedPaymentRiskLevel($refusedPaymentRiskLevel)
    {
        $this->container['refusedPaymentRiskLevel'] = $refusedPaymentRiskLevel;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


