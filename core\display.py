# -*- coding: utf-8 -*-
"""
用户界面显示模块
版本: 1.0.0
创建时间: 2024-12-19
"""

import os
import sys
import time
from typing import Dict, List, Optional, Any
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, BarColumn, TextColumn, TimeRemainingColumn
from rich.panel import Panel
from rich.text import Text
from rich.prompt import Prompt, Confirm
from rich.layout import Layout
from rich.live import Live
from .utils import config, logger, FileUtils, TimeUtils

# 常量定义
APP_NAME = "积分下载器"
APP_VERSION = "1.0.0"
APP_SLOGAN = "简单高效的资源下载工具"

# 边框和分隔线样式
BORDER_LINE = "=" * 80
DOUBLE_BORDER_LINE = "=" * 80
THIN_BORDER_LINE = "-" * 80

class DisplayManager:
    """显示管理器"""
    
    def __init__(self):
        self.console = Console()
        self.page_size = config.get('display.page_size', 20)
        self.theme = config.get('display.theme', 'default')
        self.app_name = APP_NAME
        self.app_version = APP_VERSION
        self.app_slogan = APP_SLOGAN
        
    def clear_screen(self):
        """清屏"""
        if config.get('display.auto_clear_screen', False):
            os.system('cls' if os.name == 'nt' else 'clear')
    
    def draw_colored_border(self, style="bold blue"):
        """绘制彩色边框"""
        self.console.print(DOUBLE_BORDER_LINE, style=style)
    
    def draw_header_with_slogan(self):
        """绘制带有口号的标题栏"""
        # 绘制顶部边框
        self.draw_colored_border()
        
        # 绘制标题行 - 更像课程猫的风格
        left_part = "^_^ 启智若春风拂面 ^_^"
        middle_part = f"{self.app_name}{self.app_version}下载器"
        right_part = "^_^ 探寻似星河行舟 ^_^"
        
        # 构建完整的标题行
        self.console.print(f"{left_part} {middle_part} {right_part}", style="bold yellow", justify="center")
        
        # 绘制底部边框
        self.draw_colored_border()
    
    def draw_status_bar(self, user_info=None):
        """绘制状态栏，显示用户信息、积分等"""
        if not user_info:
            return
            
        # 格式化用户信息
        user_id = user_info.get('id', 'Guest')
        username = user_info.get('username', '游客')
        points = user_info.get('points', 0)
        expiry = user_info.get('expiry_date', '未设置')
        
        # 绘制状态信息
        self.console.print(THIN_BORDER_LINE, style="blue")
        status_text = f"用户ID: {user_id}   用户名: {username}   积分余额: {points}   到期时间: {expiry}"
        self.console.print(status_text, style="green")
        self.console.print(THIN_BORDER_LINE, style="blue")
    
    def print_menu_section(self, title, items, start_index=1):
        """打印菜单分区"""
        self.console.print(f"\n[bold green]{title}:[/bold green]")
        for i, item in enumerate(items, start_index):
            self.console.print(f"  [cyan][{i}][/cyan] [yellow]{item}[/yellow]")
    
    def print_header(self, title: str = None):
        """打印程序头部"""
        title = title or self.app_name
        
        # 使用新的标题栏
        self.draw_header_with_slogan()
        
        # 如果有特定的标题，显示它
        if title != self.app_name:
            self.console.print(f"\n[bold blue]{title}[/bold blue]", justify="center")
    
    def print_footer(self):
        """打印程序底部"""
        self.draw_colored_border("blue")
        self.console.print("[dim]按 Ctrl+C 退出程序[/dim]", justify="center")
    
    def show_main_menu(self) -> str:
        """显示主菜单"""
        self.clear_screen()
        self.print_header()
        
        # 资源管理菜单项
        resource_menu = [
            "浏览资源",
            "搜索资源"
        ]
        
        # 用户管理菜单项
        user_menu = [
            "积分管理",
            "充值积分",
            "下载历史",
            "个人信息"
        ]
        
        # 系统菜单项
        system_menu = [
            "设置",
            "退出程序"
        ]
        
        # 显示分类菜单
        self.print_menu_section("资源管理", resource_menu, 1)
        self.print_menu_section("用户管理", user_menu, 3)
        self.print_menu_section("系统功能", system_menu, 7)
        
        # 构建有效选项列表
        valid_choices = [str(i) for i in range(9)]
        
        # 获取用户选择
        choice = Prompt.ask("\n请选择功能", choices=valid_choices, default="1")
        return choice
    
    def show_login_form(self) -> Dict[str, str]:
        """显示登录表单"""
        self.clear_screen()
        self.print_header("用户登录")
        
        # 显示登录提示和说明
        self.console.print("\n[bold cyan]请输入您的账号信息:[/bold cyan]")
        self.console.print("[dim]新用户请选择注册选项[/dim]")
        
        # 绘制表单分隔线
        self.console.print(THIN_BORDER_LINE, style="blue")
        
        # 获取输入
        username = Prompt.ask("[bold]用户名[/bold]")
        password = Prompt.ask("[bold]密码[/bold]", password=True)
        
        # 绘制底部分隔线
        self.console.print(THIN_BORDER_LINE, style="blue")
        
        return {
            'username': username,
            'password': password
        }
    
    def show_register_form(self) -> Dict[str, str]:
        """显示注册表单"""
        self.clear_screen()
        self.print_header("用户注册")
        
        # 显示注册提示和说明
        self.console.print("\n[bold cyan]请设置您的账号信息:[/bold cyan]")
        self.console.print("[dim]已有账号请选择登录选项[/dim]")
        
        # 绘制表单分隔线
        self.console.print(THIN_BORDER_LINE, style="blue")
        
        # 获取输入
        username = Prompt.ask("[bold]用户名[/bold]")
        password = Prompt.ask("[bold]密码[/bold]", password=True)
        confirm_password = Prompt.ask("[bold]确认密码[/bold]", password=True)
        email = Prompt.ask("[bold]邮箱（可选）[/bold]", default="")
        
        # 绘制底部分隔线
        self.console.print(THIN_BORDER_LINE, style="blue")
        
        if password != confirm_password:
            self.show_error("两次输入的密码不一致")
            return self.show_register_form()
        
        return {
            'username': username,
            'password': password,
            'email': email if email else None
        }
    
    def show_resources_list(self, resources_data: Dict[str, Any], current_page: int = 1):
        """显示资源列表"""
        self.clear_screen()
        self.print_header("资源列表")
        
        resources = resources_data.get('data', [])
        total = resources_data.get('total', 0)
        pages = resources_data.get('pages', 1)
        
        if not resources:
            self.console.print("[yellow]暂无资源[/yellow]")
            return
        
        # 创建表格
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("序号", style="dim", width=6)
        table.add_column("名称", style="cyan", min_width=20)
        table.add_column("平台", style="green", width=10)
        table.add_column("类型", style="blue", width=10)
        table.add_column("积分", style="red", width=8)
        table.add_column("大小", style="yellow", width=10)
        table.add_column("下载量", style="magenta", width=8)
        
        for i, resource in enumerate(resources, 1):
            file_size = FileUtils.format_size(resource.get('file_size', 0)) if resource.get('file_size') else "未知"
            
            table.add_row(
                str(i),
                resource.get('name', '未知'),
                resource.get('platform', '未知'),
                resource.get('resource_type', '未知'),
                str(resource.get('points_cost', 0)),
                file_size,
                str(resource.get('download_count', 0))
            )
        
        self.console.print(table)
        
        # 显示分页信息
        self.console.print(f"\n第 {current_page}/{pages} 页，共 {total} 个资源")
        
        # 显示操作提示
        self.console.print("\n[dim]输入序号下载资源，输入 'n' 下一页，'p' 上一页，'q' 返回主菜单[/dim]")
    
    def show_resource_detail(self, resource: Dict[str, Any]):
        """显示资源详情"""
        self.clear_screen()
        self.print_header("资源详情")
        
        # 创建详情面板
        details = [
            f"[bold]名称:[/bold] {resource.get('name', '未知')}",
            f"[bold]平台:[/bold] {resource.get('platform', '未知')}",
            f"[bold]类型:[/bold] {resource.get('resource_type', '未知')}",
            f"[bold]积分:[/bold] {resource.get('points_cost', 0)}",
            f"[bold]大小:[/bold] {FileUtils.format_size(resource.get('file_size', 0)) if resource.get('file_size') else '未知'}",
            f"[bold]下载量:[/bold] {resource.get('download_count', 0)}",
            f"[bold]创建时间:[/bold] {resource.get('created_at', '未知')}",
        ]
        
        if resource.get('description'):
            details.append(f"[bold]描述:[/bold] {resource.get('description')}")
        
        if resource.get('tags'):
            tags = ', '.join(resource.get('tags', []))
            details.append(f"[bold]标签:[/bold] {tags}")
        
        panel_content = '\n'.join(details)
        panel = Panel(panel_content, title="资源信息", border_style="blue")
        
        self.console.print(panel)
        
        # 显示操作选项
        if resource.get('downloaded'):
            self.console.print(f"\n[green]✓ 已下载 ({resource.get('download_time')})[/green]")
        else:
            self.console.print(f"\n[yellow]需要 {resource.get('points_cost', 0)} 积分下载[/yellow]")
    
    def show_points_info(self, points_data: Dict[str, Any]):
        """显示积分信息"""
        self.clear_screen()
        self.print_header("积分信息")
        
        # 基本信息
        balance = points_data.get('balance', 0)
        total_recharged = points_data.get('total_recharged', 0)
        total_consumed = points_data.get('total_consumed', 0)
        
        info_text = [
            f"[bold green]当前余额: {balance} 积分[/bold green]",
            f"累计充值: {total_recharged} 积分",
            f"累计消费: {total_consumed} 积分",
        ]
        
        if points_data.get('last_updated'):
            info_text.append(f"最后更新: {points_data.get('last_updated')}")
        
        panel = Panel('\n'.join(info_text), title="积分账户", border_style="green")
        self.console.print(panel)
        
        # 统计信息
        if 'statistics' in points_data:
            stats = points_data['statistics']
            stats_text = [
                f"最近充值: {stats.get('recent_recharge', 0)} 积分",
                f"最近消费: {stats.get('recent_consume', 0)} 积分",
                f"最近交易: {stats.get('recent_transactions', 0)} 笔",
            ]
            
            stats_panel = Panel('\n'.join(stats_text), title="最近统计", border_style="yellow")
            self.console.print(stats_panel)
    
    def show_payment_form(self) -> float:
        """显示充值表单"""
        self.clear_screen()
        self.print_header("积分充值")
        
        self.console.print("[yellow]充值说明: 1元 = 1积分[/yellow]")
        self.console.print("[dim]支持金额: 1-10000元[/dim]\n")
        
        while True:
            try:
                amount_str = Prompt.ask("请输入充值金额")
                amount = float(amount_str)
                
                if amount <= 0:
                    self.show_error("充值金额必须大于0")
                    continue
                
                if amount > 10000:
                    self.show_error("单次充值金额不能超过10000元")
                    continue
                
                # 确认充值
                points = int(amount)
                confirm_text = f"确认充值 {amount:.2f} 元，获得 {points} 积分？"
                
                if Confirm.ask(confirm_text):
                    return amount
                else:
                    return 0
                    
            except ValueError:
                self.show_error("请输入有效的数字")
    
    def show_qr_code_payment(self, order_info: Dict[str, Any]):
        """显示二维码支付界面"""
        self.clear_screen()
        self.print_header("扫码支付")
        
        # 订单信息
        order_text = [
            f"订单号: {order_info.get('order_number')}",
            f"金额: ¥{order_info.get('amount'):.2f}",
            f"积分: {order_info.get('points')} 积分",
            f"过期时间: {order_info.get('expires_at')}",
        ]
        
        order_panel = Panel('\n'.join(order_text), title="订单信息", border_style="blue")
        self.console.print(order_panel)
        
        # 支付提示
        self.console.print("\n[bold yellow]请使用支付宝扫描二维码完成支付[/bold yellow]")
        self.console.print("[dim]二维码已保存到 qr_code.png[/dim]")
        self.console.print("\n[green]正在等待支付...[/green]")
    
    def show_download_progress(self, progress_info: Dict[str, Any]):
        """显示下载进度"""
        with Progress(
            TextColumn("[bold blue]{task.fields[filename]}", justify="right"),
            BarColumn(bar_width=None),
            "[progress.percentage]{task.percentage:>3.1f}%",
            "•",
            TextColumn("{task.fields[speed]}", justify="right"),
            "•",
            TimeRemainingColumn(),
        ) as progress:
            
            task = progress.add_task(
                "下载中...",
                filename=progress_info.get('filename', '未知文件'),
                speed="0 B/s",
                total=progress_info.get('total_size', 100)
            )
            
            # 这里应该在实际下载过程中更新进度
            # progress.update(task, completed=downloaded_size, speed=speed_str)
    
    def show_success(self, message: str):
        """显示成功消息"""
        self.console.print(f"[bold green]✓ {message}[/bold green]")
        time.sleep(1)
    
    def show_error(self, message: str):
        """显示错误消息"""
        self.console.print(f"[bold red]✗ {message}[/bold red]")
        time.sleep(2)
    
    def show_warning(self, message: str):
        """显示警告消息"""
        self.console.print(f"[bold yellow]⚠ {message}[/bold yellow]")
        time.sleep(1)
    
    def show_info(self, message: str):
        """显示信息消息"""
        self.console.print(f"[blue]ℹ {message}[/blue]")
    
    def confirm_action(self, message: str) -> bool:
        """确认操作"""
        return Confirm.ask(message)
    
    def get_input(self, prompt: str, default: str = None) -> str:
        """获取用户输入"""
        return Prompt.ask(prompt, default=default)
    
    def wait_for_key(self, message: str = "按任意键继续..."):
        """等待用户按键"""
        self.console.print(f"\n[dim]{message}[/dim]")
        input()
    
    def show_login_prompt(self):
        """显示登录提示界面"""
        self.clear_screen()
        self.print_header()
        
        # 绘制欢迎信息
        self.console.print("\n[bold cyan]欢迎使用积分下载器[/bold cyan]", justify="center")
        self.console.print("[blue]ℹ 请先登录或注册[/blue]", justify="center")
        
        # 绘制选项边框
        self.console.print(THIN_BORDER_LINE, style="cyan")
        
        # 显示选项
        self.console.print("[bold]选择操作:[/bold]")
        self.console.print("  [cyan][1][/cyan] [yellow]登录[/yellow]")
        self.console.print("  [cyan][2][/cyan] [yellow]注册[/yellow]")
        self.console.print("  [cyan][0][/cyan] [yellow]退出[/yellow]")
        
        # 绘制底部边框
        self.console.print(THIN_BORDER_LINE, style="cyan")
        
        # 获取用户选择
        choice = Prompt.ask("请选择", choices=["0", "1", "2"], default="1")
        return choice

__all__ = ['DisplayManager']
