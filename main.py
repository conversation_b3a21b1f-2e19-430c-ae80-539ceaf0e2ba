#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
积分下载器主程序
版本: 1.0.0
创建时间: 2024-12-19
"""

import os
import sys
import json
import time
import signal
from typing import Dict, Optional, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core import PointsManager, PaymentManager, DisplayManager
from core.utils import config, logger, http_client, FileUtils, Crypto
from platforms import platform_manager

class PointsDownloader:
    """积分下载器主类"""
    
    def __init__(self):
        self.display = DisplayManager()
        self.points_manager = None
        self.payment_manager = None
        self.current_user = None
        self.token = None
        self.running = True
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        
        # 初始化
        self.init_app()
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        self.display.show_info("\n正在退出程序...")
        self.running = False
        sys.exit(0)
    
    def init_app(self):
        """初始化应用"""
        try:
            # 创建必要目录
            directories = ['downloads', 'logs', 'keys']
            for directory in directories:
                FileUtils.ensure_dir(directory)
            
            # 尝试自动登录
            self.try_auto_login()
            
            logger.info("积分下载器启动成功")
            
        except Exception as e:
            logger.error(f"应用初始化失败: {e}")
            self.display.show_error(f"初始化失败: {e}")
            sys.exit(1)
    
    def try_auto_login(self):
        """尝试自动登录"""
        if not config.get('user.auto_login', False):
            return
        
        token_file = config.get('user.token_file', 'keys/user_token.txt')
        
        if not os.path.exists(token_file):
            return
        
        try:
            with open(token_file, 'r', encoding='utf-8') as f:
                token_data = json.load(f)
            
            # 解密token（如果启用了加密）
            if config.get('security.encrypt_token', True):
                crypto = Crypto()
                token = crypto.decrypt(token_data['token'])
            else:
                token = token_data['token']
            
            # 验证token
            if self.validate_token(token):
                self.token = token
                self.setup_managers()
                self.current_user = token_data.get('user_info', {})
                logger.info("自动登录成功")
                
        except Exception as e:
            logger.warning(f"自动登录失败: {e}")
            # 删除无效的token文件
            try:
                os.remove(token_file)
            except Exception:
                pass
    
    def validate_token(self, token: str) -> bool:
        """验证token有效性"""
        try:
            headers = {'Authorization': f'Bearer {token}'}
            response = http_client.get(f"{config.get('server.api_base')}user/info.php", headers=headers)
            return response.status_code == 200
        except Exception:
            return False
    
    def setup_managers(self):
        """设置管理器"""
        self.points_manager = PointsManager(self.token)
        self.payment_manager = PaymentManager(self.token)
    
    def save_token(self, token: str, user_info: Dict[str, Any]):
        """保存用户token"""
        if not config.get('user.remember_token', True):
            return
        
        token_file = config.get('user.token_file', 'keys/user_token.txt')
        
        try:
            # 加密token（如果启用了加密）
            if config.get('security.encrypt_token', True):
                crypto = Crypto()
                encrypted_token = crypto.encrypt(token)
            else:
                encrypted_token = token
            
            token_data = {
                'token': encrypted_token,
                'user_info': user_info,
                'saved_at': time.time()
            }
            
            with open(token_file, 'w', encoding='utf-8') as f:
                json.dump(token_data, f, ensure_ascii=False, indent=2)
            
            logger.info("用户token已保存")
            
        except Exception as e:
            logger.error(f"保存token失败: {e}")
    
    def login(self) -> bool:
        """用户登录"""
        try:
            login_data = self.display.show_login_form()
            
            # 发送登录请求
            url = f"{config.get('server.api_base')}user/login.php"
            response = http_client.post(url, json=login_data)
            result = response.json()
            
            if result.get('success'):
                self.token = result['data']['token']
                self.current_user = result['data']
                self.setup_managers()
                
                # 保存token
                self.save_token(self.token, self.current_user)
                
                self.display.show_success(f"登录成功，欢迎 {self.current_user['username']}")
                return True
            else:
                self.display.show_error(result.get('message', '登录失败'))
                return False
                
        except Exception as e:
            logger.error(f"登录失败: {e}")
            self.display.show_error(f"登录失败: {e}")
            return False
    
    def register(self) -> bool:
        """用户注册"""
        try:
            register_data = self.display.show_register_form()
            
            # 发送注册请求
            url = f"{config.get('server.api_base')}user/register.php"
            response = http_client.post(url, json=register_data)
            result = response.json()
            
            if result.get('success'):
                self.display.show_success("注册成功，请登录")
                return True
            else:
                self.display.show_error(result.get('message', '注册失败'))
                return False
                
        except Exception as e:
            logger.error(f"注册失败: {e}")
            self.display.show_error(f"注册失败: {e}")
            return False
    
    def browse_resources(self):
        """浏览资源"""
        try:
            page = 1
            
            while True:
                # 获取资源列表
                resources_data = self.points_manager._make_request('GET', 'resources/list.php', {
                    'page': page,
                    'limit': self.display.page_size
                })['data']
                
                # 显示资源列表
                self.display.show_resources_list(resources_data, page)
                
                # 获取用户输入
                choice = self.display.get_input("请选择操作")
                
                if choice.lower() == 'q':
                    break
                elif choice.lower() == 'n' and page < resources_data.get('pages', 1):
                    page += 1
                elif choice.lower() == 'p' and page > 1:
                    page -= 1
                elif choice.isdigit():
                    index = int(choice) - 1
                    resources = resources_data.get('data', [])
                    if 0 <= index < len(resources):
                        self.download_resource(resources[index])
                else:
                    self.display.show_warning("无效的选择")
                    
        except Exception as e:
            logger.error(f"浏览资源失败: {e}")
            self.display.show_error(f"浏览资源失败: {e}")
    
    def download_resource(self, resource: Dict[str, Any]):
        """下载资源"""
        try:
            # 显示资源详情
            self.display.show_resource_detail(resource)
            
            # 确认下载
            points_cost = resource.get('points_cost', 0)
            if points_cost > 0:
                confirm_msg = f"下载此资源需要 {points_cost} 积分，确认下载？"
            else:
                confirm_msg = "确认下载此资源？"
            
            if not self.display.confirm_action(confirm_msg):
                return
            
            # 检查积分余额
            if points_cost > 0:
                current_balance = self.points_manager.get_current_balance()
                if current_balance < points_cost:
                    current_balance = self.points_manager.refresh_balance()
                    if current_balance < points_cost:
                        self.display.show_error(f"积分余额不足，当前余额: {current_balance}，需要: {points_cost}")
                        return
            
            # 获取下载器
            platform_name = resource.get('platform')
            downloader_class = platform_manager.get_platform(platform_name)
            
            if not downloader_class:
                self.display.show_error(f"不支持的平台: {platform_name}")
                return
            
            # 创建下载器实例
            downloader = downloader_class(self.token)
            
            # 开始下载
            self.display.show_info("开始下载...")
            result = downloader.download_resource(str(resource['id']))
            
            if result['success']:
                self.display.show_success(f"下载完成: {result['file_path']}")
            else:
                self.display.show_error(f"下载失败: {result.get('message', '未知错误')}")
                
        except Exception as e:
            logger.error(f"下载资源失败: {e}")
            self.display.show_error(f"下载失败: {e}")
    
    def manage_points(self):
        """积分管理"""
        try:
            # 获取积分信息
            points_data = self.points_manager.get_balance()
            self.display.show_points_info(points_data)
            
            self.display.wait_for_key()
            
        except Exception as e:
            logger.error(f"获取积分信息失败: {e}")
            self.display.show_error(f"获取积分信息失败: {e}")
    
    def recharge_points(self):
        """充值积分"""
        try:
            # 显示充值表单
            amount = self.display.show_payment_form()
            
            if amount <= 0:
                return
            
            # 创建支付订单
            order_info = self.payment_manager.create_order(amount)
            
            # 生成并保存二维码
            qr_file = "qr_code.png"
            self.payment_manager.save_qr_code(order_info['qr_code'], qr_file)
            
            # 显示支付界面
            self.display.show_qr_code_payment(order_info)
            
            # 等待支付完成
            result = self.payment_manager.wait_for_payment(order_info['order_number'])
            
            if result['success']:
                self.display.show_success("支付成功，积分已到账")
                # 刷新积分余额
                self.points_manager.refresh_balance()
            else:
                self.display.show_error(f"支付失败: {result['message']}")
                
        except Exception as e:
            logger.error(f"充值失败: {e}")
            self.display.show_error(f"充值失败: {e}")
    
    def run(self):
        """运行主程序"""
        self.display.print_header()
        
        # 检查是否已登录
        if not self.token:
            # 使用新的登录提示界面
            while not self.token:
                choice = self.display.show_login_prompt()
                
                if choice == "1":
                    if self.login():
                        break
                elif choice == "2":
                    if self.register():
                        continue
                elif choice == "0":
                    return
        
        # 主循环
        while self.running:
            try:
                choice = self.display.show_main_menu()
                
                if choice == "0":
                    break
                elif choice == "1":
                    self.browse_resources()
                elif choice == "2":
                    self.display.show_info("搜索功能开发中...")
                elif choice == "3":
                    self.manage_points()
                elif choice == "4":
                    self.recharge_points()
                elif choice == "5":
                    self.display.show_info("下载历史功能开发中...")
                elif choice == "6":
                    self.display.show_info("个人信息功能开发中...")
                elif choice == "7":
                    self.display.show_info("设置功能开发中...")
                else:
                    self.display.show_warning("无效的选择")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                logger.error(f"程序运行错误: {e}")
                self.display.show_error(f"程序错误: {e}")
        
        self.display.show_info("感谢使用积分下载器！")

def main():
    """主函数"""
    try:
        app = PointsDownloader()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
