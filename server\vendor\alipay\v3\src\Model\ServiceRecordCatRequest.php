<?php
/**
 * ServiceRecordCatRequest
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * ServiceRecordCatRequest Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class ServiceRecordCatRequest implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'ServiceRecordCatRequest';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'catName' => 'string',
        'firstCat' => 'string',
        'fourthCat' => 'string',
        'instenceCode' => 'string',
        'secondCat' => 'string',
        'thirdCat' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'catName' => null,
        'firstCat' => null,
        'fourthCat' => null,
        'instenceCode' => null,
        'secondCat' => null,
        'thirdCat' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'catName' => 'cat_name',
        'firstCat' => 'first_cat',
        'fourthCat' => 'fourth_cat',
        'instenceCode' => 'instence_code',
        'secondCat' => 'second_cat',
        'thirdCat' => 'third_cat'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'catName' => 'setCatName',
        'firstCat' => 'setFirstCat',
        'fourthCat' => 'setFourthCat',
        'instenceCode' => 'setInstenceCode',
        'secondCat' => 'setSecondCat',
        'thirdCat' => 'setThirdCat'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'catName' => 'getCatName',
        'firstCat' => 'getFirstCat',
        'fourthCat' => 'getFourthCat',
        'instenceCode' => 'getInstenceCode',
        'secondCat' => 'getSecondCat',
        'thirdCat' => 'getThirdCat'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['catName'] = $data['catName'] ?? null;
        $this->container['firstCat'] = $data['firstCat'] ?? null;
        $this->container['fourthCat'] = $data['fourthCat'] ?? null;
        $this->container['instenceCode'] = $data['instenceCode'] ?? null;
        $this->container['secondCat'] = $data['secondCat'] ?? null;
        $this->container['thirdCat'] = $data['thirdCat'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets catName
     *
     * @return string|null
     */
    public function getCatName()
    {
        return $this->container['catName'];
    }

    /**
     * Sets catName
     *
     * @param string|null $catName 类目名称(需要每一级用;分隔封装)
     *
     * @return self
     */
    public function setCatName($catName)
    {
        $this->container['catName'] = $catName;

        return $this;
    }

    /**
     * Gets firstCat
     *
     * @return string|null
     */
    public function getFirstCat()
    {
        return $this->container['firstCat'];
    }

    /**
     * Sets firstCat
     *
     * @param string|null $firstCat 一级类目编码
     *
     * @return self
     */
    public function setFirstCat($firstCat)
    {
        $this->container['firstCat'] = $firstCat;

        return $this;
    }

    /**
     * Gets fourthCat
     *
     * @return string|null
     */
    public function getFourthCat()
    {
        return $this->container['fourthCat'];
    }

    /**
     * Sets fourthCat
     *
     * @param string|null $fourthCat 四级类目编码
     *
     * @return self
     */
    public function setFourthCat($fourthCat)
    {
        $this->container['fourthCat'] = $fourthCat;

        return $this;
    }

    /**
     * Gets instenceCode
     *
     * @return string|null
     */
    public function getInstenceCode()
    {
        return $this->container['instenceCode'];
    }

    /**
     * Sets instenceCode
     *
     * @param string|null $instenceCode 类目树编码
     *
     * @return self
     */
    public function setInstenceCode($instenceCode)
    {
        $this->container['instenceCode'] = $instenceCode;

        return $this;
    }

    /**
     * Gets secondCat
     *
     * @return string|null
     */
    public function getSecondCat()
    {
        return $this->container['secondCat'];
    }

    /**
     * Sets secondCat
     *
     * @param string|null $secondCat 二级类目编码
     *
     * @return self
     */
    public function setSecondCat($secondCat)
    {
        $this->container['secondCat'] = $secondCat;

        return $this;
    }

    /**
     * Gets thirdCat
     *
     * @return string|null
     */
    public function getThirdCat()
    {
        return $this->container['thirdCat'];
    }

    /**
     * Sets thirdCat
     *
     * @param string|null $thirdCat 三级类目编码
     *
     * @return self
     */
    public function setThirdCat($thirdCat)
    {
        $this->container['thirdCat'] = $thirdCat;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


