<?php
/**
 * VoucherPackageSalesInfo
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * VoucherPackageSalesInfo Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class VoucherPackageSalesInfo implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'VoucherPackageSalesInfo';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'budget' => 'int',
        'payChannel' => 'string',
        'purchaseUrl' => 'string',
        'saleCountLimitInPeriod' => 'int',
        'salePeriodLimit' => 'string',
        'salePrice' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'budget' => null,
        'payChannel' => null,
        'purchaseUrl' => null,
        'saleCountLimitInPeriod' => null,
        'salePeriodLimit' => null,
        'salePrice' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'budget' => 'budget',
        'payChannel' => 'pay_channel',
        'purchaseUrl' => 'purchase_url',
        'saleCountLimitInPeriod' => 'sale_count_limit_in_period',
        'salePeriodLimit' => 'sale_period_limit',
        'salePrice' => 'sale_price'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'budget' => 'setBudget',
        'payChannel' => 'setPayChannel',
        'purchaseUrl' => 'setPurchaseUrl',
        'saleCountLimitInPeriod' => 'setSaleCountLimitInPeriod',
        'salePeriodLimit' => 'setSalePeriodLimit',
        'salePrice' => 'setSalePrice'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'budget' => 'getBudget',
        'payChannel' => 'getPayChannel',
        'purchaseUrl' => 'getPurchaseUrl',
        'saleCountLimitInPeriod' => 'getSaleCountLimitInPeriod',
        'salePeriodLimit' => 'getSalePeriodLimit',
        'salePrice' => 'getSalePrice'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['budget'] = $data['budget'] ?? null;
        $this->container['payChannel'] = $data['payChannel'] ?? null;
        $this->container['purchaseUrl'] = $data['purchaseUrl'] ?? null;
        $this->container['saleCountLimitInPeriod'] = $data['saleCountLimitInPeriod'] ?? null;
        $this->container['salePeriodLimit'] = $data['salePeriodLimit'] ?? null;
        $this->container['salePrice'] = $data['salePrice'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets budget
     *
     * @return int|null
     */
    public function getBudget()
    {
        return $this->container['budget'];
    }

    /**
     * Sets budget
     *
     * @param int|null $budget 券包售卖预算，单位是份数
     *
     * @return self
     */
    public function setBudget($budget)
    {
        $this->container['budget'] = $budget;

        return $this;
    }

    /**
     * Gets payChannel
     *
     * @return string|null
     */
    public function getPayChannel()
    {
        return $this->container['payChannel'];
    }

    /**
     * Sets payChannel
     *
     * @param string|null $payChannel 券包购买支付渠道 pcredit：花呗 creditCard：信用卡 credit_group：花呗与信用卡 为空则不限渠道
     *
     * @return self
     */
    public function setPayChannel($payChannel)
    {
        $this->container['payChannel'] = $payChannel;

        return $this;
    }

    /**
     * Gets purchaseUrl
     *
     * @return string|null
     */
    public function getPurchaseUrl()
    {
        return $this->container['purchaseUrl'];
    }

    /**
     * Sets purchaseUrl
     *
     * @param string|null $purchaseUrl 券包购买链接
     *
     * @return self
     */
    public function setPurchaseUrl($purchaseUrl)
    {
        $this->container['purchaseUrl'] = $purchaseUrl;

        return $this;
    }

    /**
     * Gets saleCountLimitInPeriod
     *
     * @return int|null
     */
    public function getSaleCountLimitInPeriod()
    {
        return $this->container['saleCountLimitInPeriod'];
    }

    /**
     * Sets saleCountLimitInPeriod
     *
     * @param int|null $saleCountLimitInPeriod 券包售卖期限内最大购买次数
     *
     * @return self
     */
    public function setSaleCountLimitInPeriod($saleCountLimitInPeriod)
    {
        $this->container['saleCountLimitInPeriod'] = $saleCountLimitInPeriod;

        return $this;
    }

    /**
     * Gets salePeriodLimit
     *
     * @return string|null
     */
    public function getSalePeriodLimit()
    {
        return $this->container['salePeriodLimit'];
    }

    /**
     * Sets salePeriodLimit
     *
     * @param string|null $salePeriodLimit 券包购买期限类型 NO：不限制  ALL：售卖时间内  DAY：天  WEEK：周  MONTH：月
     *
     * @return self
     */
    public function setSalePeriodLimit($salePeriodLimit)
    {
        $this->container['salePeriodLimit'] = $salePeriodLimit;

        return $this;
    }

    /**
     * Gets salePrice
     *
     * @return string|null
     */
    public function getSalePrice()
    {
        return $this->container['salePrice'];
    }

    /**
     * Sets salePrice
     *
     * @param string|null $salePrice 券包售卖价格，单位是元
     *
     * @return self
     */
    public function setSalePrice($salePrice)
    {
        $this->container['salePrice'] = $salePrice;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


