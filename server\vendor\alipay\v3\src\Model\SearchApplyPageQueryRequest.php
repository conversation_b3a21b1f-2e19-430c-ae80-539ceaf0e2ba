<?php
/**
 * SearchApplyPageQueryRequest
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * SearchApplyPageQueryRequest Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class SearchApplyPageQueryRequest implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'SearchApplyPageQueryRequest';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'applyType' => 'string',
        'auditStatusList' => 'string[]',
        'categoryCode' => 'string',
        'name' => 'string',
        'pageNum' => 'string',
        'pageSize' => 'string',
        'serviceCode' => 'string',
        'serviceId' => 'string',
        'startRow' => 'string',
        'subServiceCode' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'applyType' => null,
        'auditStatusList' => null,
        'categoryCode' => null,
        'name' => null,
        'pageNum' => null,
        'pageSize' => null,
        'serviceCode' => null,
        'serviceId' => null,
        'startRow' => null,
        'subServiceCode' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'applyType' => 'apply_type',
        'auditStatusList' => 'audit_status_list',
        'categoryCode' => 'category_code',
        'name' => 'name',
        'pageNum' => 'page_num',
        'pageSize' => 'page_size',
        'serviceCode' => 'service_code',
        'serviceId' => 'service_id',
        'startRow' => 'start_row',
        'subServiceCode' => 'sub_service_code'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'applyType' => 'setApplyType',
        'auditStatusList' => 'setAuditStatusList',
        'categoryCode' => 'setCategoryCode',
        'name' => 'setName',
        'pageNum' => 'setPageNum',
        'pageSize' => 'setPageSize',
        'serviceCode' => 'setServiceCode',
        'serviceId' => 'setServiceId',
        'startRow' => 'setStartRow',
        'subServiceCode' => 'setSubServiceCode'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'applyType' => 'getApplyType',
        'auditStatusList' => 'getAuditStatusList',
        'categoryCode' => 'getCategoryCode',
        'name' => 'getName',
        'pageNum' => 'getPageNum',
        'pageSize' => 'getPageSize',
        'serviceCode' => 'getServiceCode',
        'serviceId' => 'getServiceId',
        'startRow' => 'getStartRow',
        'subServiceCode' => 'getSubServiceCode'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['applyType'] = $data['applyType'] ?? null;
        $this->container['auditStatusList'] = $data['auditStatusList'] ?? null;
        $this->container['categoryCode'] = $data['categoryCode'] ?? null;
        $this->container['name'] = $data['name'] ?? null;
        $this->container['pageNum'] = $data['pageNum'] ?? null;
        $this->container['pageSize'] = $data['pageSize'] ?? null;
        $this->container['serviceCode'] = $data['serviceCode'] ?? null;
        $this->container['serviceId'] = $data['serviceId'] ?? null;
        $this->container['startRow'] = $data['startRow'] ?? null;
        $this->container['subServiceCode'] = $data['subServiceCode'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets applyType
     *
     * @return string|null
     */
    public function getApplyType()
    {
        return $this->container['applyType'];
    }

    /**
     * Sets applyType
     *
     * @param string|null $applyType 申请类型 BASE：基础信息， BRAND_BOX：品牌直达，SERVICE_BOX服务直达
     *
     * @return self
     */
    public function setApplyType($applyType)
    {
        $this->container['applyType'] = $applyType;

        return $this;
    }

    /**
     * Gets auditStatusList
     *
     * @return string[]|null
     */
    public function getAuditStatusList()
    {
        return $this->container['auditStatusList'];
    }

    /**
     * Sets auditStatusList
     *
     * @param string[]|null $auditStatusList 筛选状态数组 AUDIT AGREE REJECT CANCEL EDIT
     *
     * @return self
     */
    public function setAuditStatusList($auditStatusList)
    {
        $this->container['auditStatusList'] = $auditStatusList;

        return $this;
    }

    /**
     * Gets categoryCode
     *
     * @return string|null
     */
    public function getCategoryCode()
    {
        return $this->container['categoryCode'];
    }

    /**
     * Sets categoryCode
     *
     * @param string|null $categoryCode 类目编码
     *
     * @return self
     */
    public function setCategoryCode($categoryCode)
    {
        $this->container['categoryCode'] = $categoryCode;

        return $this;
    }

    /**
     * Gets name
     *
     * @return string|null
     */
    public function getName()
    {
        return $this->container['name'];
    }

    /**
     * Sets name
     *
     * @param string|null $name 服务名称
     *
     * @return self
     */
    public function setName($name)
    {
        $this->container['name'] = $name;

        return $this;
    }

    /**
     * Gets pageNum
     *
     * @return string|null
     */
    public function getPageNum()
    {
        return $this->container['pageNum'];
    }

    /**
     * Sets pageNum
     *
     * @param string|null $pageNum 当前页码
     *
     * @return self
     */
    public function setPageNum($pageNum)
    {
        $this->container['pageNum'] = $pageNum;

        return $this;
    }

    /**
     * Gets pageSize
     *
     * @return string|null
     */
    public function getPageSize()
    {
        return $this->container['pageSize'];
    }

    /**
     * Sets pageSize
     *
     * @param string|null $pageSize 页面的显示记录条数
     *
     * @return self
     */
    public function setPageSize($pageSize)
    {
        $this->container['pageSize'] = $pageSize;

        return $this;
    }

    /**
     * Gets serviceCode
     *
     * @return string|null
     */
    public function getServiceCode()
    {
        return $this->container['serviceCode'];
    }

    /**
     * Sets serviceCode
     *
     * @param string|null $serviceCode 服务code
     *
     * @return self
     */
    public function setServiceCode($serviceCode)
    {
        $this->container['serviceCode'] = $serviceCode;

        return $this;
    }

    /**
     * Gets serviceId
     *
     * @return string|null
     */
    public function getServiceId()
    {
        return $this->container['serviceId'];
    }

    /**
     * Sets serviceId
     *
     * @param string|null $serviceId 小程序appid
     *
     * @return self
     */
    public function setServiceId($serviceId)
    {
        $this->container['serviceId'] = $serviceId;

        return $this;
    }

    /**
     * Gets startRow
     *
     * @return string|null
     */
    public function getStartRow()
    {
        return $this->container['startRow'];
    }

    /**
     * Sets startRow
     *
     * @param string|null $startRow 起始记录，起始：0
     *
     * @return self
     */
    public function setStartRow($startRow)
    {
        $this->container['startRow'] = $startRow;

        return $this;
    }

    /**
     * Gets subServiceCode
     *
     * @return string|null
     */
    public function getSubServiceCode()
    {
        return $this->container['subServiceCode'];
    }

    /**
     * Sets subServiceCode
     *
     * @param string|null $subServiceCode 二级服务code
     *
     * @return self
     */
    public function setSubServiceCode($subServiceCode)
    {
        $this->container['subServiceCode'] = $subServiceCode;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


