<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>积分下载器 - 管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.8rem;
            font-weight: 300;
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .card-icon {
            font-size: 2rem;
            margin-right: 1rem;
        }
        
        .card-title {
            font-size: 1.1rem;
            color: #666;
            margin: 0;
        }
        
        .card-value {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
        }
        
        .menu {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .menu-item {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            text-decoration: none;
            color: #333;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.2s;
        }
        
        .menu-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            text-decoration: none;
            color: #667eea;
        }
        
        .menu-icon {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            display: block;
        }
        
        .menu-title {
            font-size: 1.1rem;
            font-weight: 500;
        }
        
        .menu-desc {
            font-size: 0.9rem;
            color: #666;
            margin-top: 0.5rem;
        }
        
        .footer {
            text-align: center;
            padding: 2rem;
            color: #666;
            border-top: 1px solid #eee;
            margin-top: 3rem;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 0.5rem;
            }
            
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .menu {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>积分下载器管理后台</h1>
    </div>
    
    <div class="container">
        <!-- 数据统计 -->
        <div class="dashboard">
            <div class="card">
                <div class="card-header">
                    <span class="card-icon">👥</span>
                    <h3 class="card-title">总用户数</h3>
                </div>
                <div class="card-value" id="total-users">-</div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <span class="card-icon">💰</span>
                    <h3 class="card-title">总积分</h3>
                </div>
                <div class="card-value" id="total-points">-</div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <span class="card-icon">📦</span>
                    <h3 class="card-title">总资源数</h3>
                </div>
                <div class="card-value" id="total-resources">-</div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <span class="card-icon">⬇️</span>
                    <h3 class="card-title">总下载量</h3>
                </div>
                <div class="card-value" id="total-downloads">-</div>
            </div>
        </div>
        
        <!-- 功能菜单 -->
        <div class="menu">
            <a href="users.php" class="menu-item">
                <span class="menu-icon">👥</span>
                <div class="menu-title">用户管理</div>
                <div class="menu-desc">管理用户账户和权限</div>
            </a>
            
            <a href="points.php" class="menu-item">
                <span class="menu-icon">💰</span>
                <div class="menu-title">积分管理</div>
                <div class="menu-desc">查看和管理用户积分</div>
            </a>
            
            <a href="orders.php" class="menu-item">
                <span class="menu-icon">🛒</span>
                <div class="menu-title">订单管理</div>
                <div class="menu-desc">管理充值订单</div>
            </a>
            
            <a href="resources.php" class="menu-item">
                <span class="menu-icon">📦</span>
                <div class="menu-title">资源管理</div>
                <div class="menu-desc">管理下载资源</div>
            </a>
            
            <a href="downloads.php" class="menu-item">
                <span class="menu-icon">⬇️</span>
                <div class="menu-title">下载记录</div>
                <div class="menu-desc">查看下载历史</div>
            </a>
            
            <a href="settings.php" class="menu-item">
                <span class="menu-icon">⚙️</span>
                <div class="menu-title">系统设置</div>
                <div class="menu-desc">配置系统参数</div>
            </a>
        </div>
    </div>
    
    <div class="footer">
        <p>&copy; 2024 积分下载器管理系统 v1.0.0</p>
    </div>
    
    <script>
        // 加载统计数据
        async function loadStatistics() {
            try {
                const response = await fetch('../api/admin/statistics.php');
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('total-users').textContent = data.data.total_users || 0;
                    document.getElementById('total-points').textContent = data.data.total_points || 0;
                    document.getElementById('total-resources').textContent = data.data.total_resources || 0;
                    document.getElementById('total-downloads').textContent = data.data.total_downloads || 0;
                }
            } catch (error) {
                console.error('加载统计数据失败:', error);
            }
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
        });
    </script>
</body>
</html>
