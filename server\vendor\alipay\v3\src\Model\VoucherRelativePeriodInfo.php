<?php
/**
 * VoucherRelativePeriodInfo
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * VoucherRelativePeriodInfo Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class VoucherRelativePeriodInfo implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'VoucherRelativePeriodInfo';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'timeRestrictInfo' => '\Alipay\OpenAPISDK\Model\TimeRestrictInfo',
        'validDaysAfterReceive' => 'int',
        'waitDaysAfterReceive' => 'int'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'timeRestrictInfo' => null,
        'validDaysAfterReceive' => null,
        'waitDaysAfterReceive' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'timeRestrictInfo' => 'time_restrict_info',
        'validDaysAfterReceive' => 'valid_days_after_receive',
        'waitDaysAfterReceive' => 'wait_days_after_receive'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'timeRestrictInfo' => 'setTimeRestrictInfo',
        'validDaysAfterReceive' => 'setValidDaysAfterReceive',
        'waitDaysAfterReceive' => 'setWaitDaysAfterReceive'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'timeRestrictInfo' => 'getTimeRestrictInfo',
        'validDaysAfterReceive' => 'getValidDaysAfterReceive',
        'waitDaysAfterReceive' => 'getWaitDaysAfterReceive'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['timeRestrictInfo'] = $data['timeRestrictInfo'] ?? null;
        $this->container['validDaysAfterReceive'] = $data['validDaysAfterReceive'] ?? null;
        $this->container['waitDaysAfterReceive'] = $data['waitDaysAfterReceive'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets timeRestrictInfo
     *
     * @return \Alipay\OpenAPISDK\Model\TimeRestrictInfo|null
     */
    public function getTimeRestrictInfo()
    {
        return $this->container['timeRestrictInfo'];
    }

    /**
     * Sets timeRestrictInfo
     *
     * @param \Alipay\OpenAPISDK\Model\TimeRestrictInfo|null $timeRestrictInfo timeRestrictInfo
     *
     * @return self
     */
    public function setTimeRestrictInfo($timeRestrictInfo)
    {
        $this->container['timeRestrictInfo'] = $timeRestrictInfo;

        return $this;
    }

    /**
     * Gets validDaysAfterReceive
     *
     * @return int|null
     */
    public function getValidDaysAfterReceive()
    {
        return $this->container['validDaysAfterReceive'];
    }

    /**
     * Sets validDaysAfterReceive
     *
     * @param int|null $validDaysAfterReceive 券生效后 N 天内可以使用。 可以配合wait_days_after_receive 字段使用。
     *
     * @return self
     */
    public function setValidDaysAfterReceive($validDaysAfterReceive)
    {
        $this->container['validDaysAfterReceive'] = $validDaysAfterReceive;

        return $this;
    }

    /**
     * Gets waitDaysAfterReceive
     *
     * @return int|null
     */
    public function getWaitDaysAfterReceive()
    {
        return $this->container['waitDaysAfterReceive'];
    }

    /**
     * Sets waitDaysAfterReceive
     *
     * @param int|null $waitDaysAfterReceive 用户领券后需要等待 N 天，券才可以生效。  字段值为 0 表示用户领券后立刻生效。
     *
     * @return self
     */
    public function setWaitDaysAfterReceive($waitDaysAfterReceive)
    {
        $this->container['waitDaysAfterReceive'] = $waitDaysAfterReceive;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


