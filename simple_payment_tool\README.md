# 简化收款工具

一个基于现有支付系统的简单收款工具，实现输入金额→生成收款二维码→监控支付状态的完整流程。

## 功能特点

- ✅ **简单易用**: 命令行界面，输入金额即可生成收款二维码
- ✅ **实时监控**: 自动监控支付状态，支付成功后立即提示
- ✅ **二维码显示**: 自动打开二维码图片，支持保存到本地
- ✅ **支付宝集成**: 基于支付宝当面付，安全可靠
- ✅ **错误处理**: 完善的错误处理和用户提示
- ✅ **跨平台**: 支持Windows、macOS、Linux

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置说明

### 1. 修改配置文件

编辑 `config.py` 文件，设置以下关键配置：

```python
# 服务器配置
API_BASE_URL = "https://mengmeng.kechengmao.top/api"

# 认证配置（重要！）
USER_TOKEN = "your_user_token_here"  # 请替换为实际的token
```

### 2. 获取用户Token

您需要一个有效的用户token来调用支付API。有以下几种方式：

#### 方式1: 从现有系统获取
如果您已经有用户账号，可以通过登录API获取token：

```bash
curl -X POST https://mengmeng.kechengmao.top/api/auth/login.php \
  -H "Content-Type: application/json" \
  -d '{"username":"your_username","password":"your_password"}'
```

#### 方式2: 使用环境变量
设置环境变量（推荐，更安全）：

```bash
# Windows
set PAYMENT_TOKEN=your_actual_token_here

# Linux/macOS
export PAYMENT_TOKEN=your_actual_token_here
```

#### 方式3: 创建专用API端点（可选）
如果您有服务器访问权限，可以创建一个不需要认证的专用收款API端点。

## 使用方法

### 基本使用

```bash
python main.py
```

然后按照提示输入金额即可。

### 使用示例

```
$ python main.py

🎯 简化收款工具 v1.0.0
================================

请输入收款金额: 100
正在创建支付订单，金额: ¥100.00...
✓ 订单创建成功!
  订单号: PD20241219143025001
  金额: ¥100.00
  积分: 100
  过期时间: 2024-12-19 15:00:25

✓ 二维码已显示
📱 请使用支付宝扫描二维码完成支付

等待支付完成...
订单号: PD20241219143025001
超时时间: 300秒
检查间隔: 3秒

⏳ 等待支付中... (15s/300s, 剩余285s)
🎉 支付成功!
  订单号: PD20241219143025001
  支付宝交易号: 2024121922001234567890
  支付时间: 2024-12-19 14:30:45

是否继续收款？(y/n): n
感谢使用！
```

## 文件结构

```
simple_payment_tool/
├── main.py              # 主程序
├── payment_client.py    # 支付API客户端
├── qr_generator.py      # 二维码生成器
├── config.py           # 配置文件
├── requirements.txt    # 依赖包
├── README.md          # 说明文档
├── qr_codes/          # 二维码保存目录（自动创建）
└── temp/              # 临时文件目录（自动创建）
```

## 配置选项

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `API_BASE_URL` | 支付API基础URL | `https://mengmeng.kechengmao.top/api` |
| `USER_TOKEN` | 用户认证token | `your_user_token_here` |
| `QR_CODE_SIZE` | 二维码图片大小 | `300` |
| `PAYMENT_TIMEOUT` | 支付超时时间（秒） | `300` |
| `CHECK_INTERVAL` | 状态检查间隔（秒） | `3` |
| `SAVE_QR_CODES` | 是否保存二维码 | `True` |
| `AUTO_OPEN_QR` | 是否自动打开二维码 | `True` |
| `MIN_AMOUNT` | 最小金额 | `0.01` |
| `MAX_AMOUNT` | 最大金额 | `10000.0` |

## 故障排除

### 1. 连接失败
- 检查网络连接
- 确认API_BASE_URL配置正确
- 确认服务器正常运行

### 2. 认证失败
- 检查USER_TOKEN是否正确
- 确认token未过期
- 尝试重新获取token

### 3. 二维码无法显示
- 确认已安装PIL/Pillow库
- 检查系统是否支持图片查看器
- 可以手动打开保存的二维码图片

### 4. 支付状态检查失败
- 检查网络连接稳定性
- 确认订单号正确
- 查看错误日志信息

## 注意事项

1. **安全性**: 请妥善保管您的用户token，不要泄露给他人
2. **网络**: 确保网络连接稳定，避免支付过程中断网
3. **金额**: 支持的金额范围为0.01-10000.00元
4. **超时**: 默认支付超时时间为5分钟，可在配置中调整
5. **保存**: 二维码图片默认保存在qr_codes目录中

## 技术支持

如果遇到问题，请检查：
1. 配置文件是否正确
2. 依赖包是否完整安装
3. 网络连接是否正常
4. 服务器API是否可访问

## 版本信息

- 版本: 1.0.0
- 创建时间: 2024-12-19
- Python版本要求: 3.7+
