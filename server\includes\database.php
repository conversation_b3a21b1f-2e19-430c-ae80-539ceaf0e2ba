<?php
/**
 * 数据库连接和操作类
 * 版本: 1.0.0
 * 创建时间: 2024-12-19
 */

require_once 'config.php';

class Database {
    private static $instance = null;
    private $connection;
    private $host;
    private $dbname;
    private $username;
    private $password;
    private $charset;

    private function __construct() {
        $this->host = DB_HOST;
        $this->dbname = DB_NAME;
        $this->username = DB_USER;
        $this->password = DB_PASS;
        $this->charset = DB_CHARSET;
        
        $this->connect();
    }

    /**
     * 获取数据库实例（单例模式）
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 建立数据库连接
     */
    private function connect() {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->dbname};charset={$this->charset}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset}"
            ];
            
            $this->connection = new PDO($dsn, $this->username, $this->password, $options);
            
            writeLog("数据库连接成功", 'INFO');
        } catch (PDOException $e) {
            writeLog("数据库连接失败: " . $e->getMessage(), 'ERROR');
            throw new Exception("数据库连接失败");
        }
    }

    /**
     * 获取PDO连接对象
     */
    public function getConnection() {
        return $this->connection;
    }

    /**
     * 执行查询语句
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            writeLog("SQL查询错误: " . $e->getMessage() . " SQL: $sql", 'ERROR');
            throw new Exception("数据库查询失败");
        }
    }

    /**
     * 查询单行数据
     */
    public function fetchOne($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }

    /**
     * 查询多行数据
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }

    /**
     * 执行插入语句并返回插入ID
     */
    public function insert($sql, $params = []) {
        $this->query($sql, $params);
        return $this->connection->lastInsertId();
    }

    /**
     * 执行更新或删除语句并返回影响行数
     */
    public function execute($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }

    /**
     * 开始事务
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }

    /**
     * 提交事务
     */
    public function commit() {
        return $this->connection->commit();
    }

    /**
     * 回滚事务
     */
    public function rollback() {
        return $this->connection->rollback();
    }

    /**
     * 检查是否在事务中
     */
    public function inTransaction() {
        return $this->connection->inTransaction();
    }

    /**
     * 检查表是否存在
     */
    public function tableExists($tableName) {
        $sql = "SHOW TABLES LIKE ?";
        $result = $this->fetchOne($sql, [$tableName]);
        return !empty($result);
    }

    /**
     * 获取表的字段信息
     */
    public function getTableColumns($tableName) {
        $sql = "DESCRIBE `$tableName`";
        return $this->fetchAll($sql);
    }

    /**
     * 构建WHERE条件
     */
    public function buildWhere($conditions) {
        if (empty($conditions)) {
            return ['', []];
        }

        $whereParts = [];
        $params = [];

        foreach ($conditions as $field => $value) {
            if (is_array($value)) {
                // IN 查询
                $placeholders = str_repeat('?,', count($value) - 1) . '?';
                $whereParts[] = "`$field` IN ($placeholders)";
                $params = array_merge($params, $value);
            } elseif (strpos($field, ' ') !== false) {
                // 包含操作符的字段，如 'age >' => 18
                $whereParts[] = "`" . trim(explode(' ', $field)[0]) . "` " . 
                               trim(explode(' ', $field, 2)[1]) . " ?";
                $params[] = $value;
            } else {
                // 普通等值查询
                $whereParts[] = "`$field` = ?";
                $params[] = $value;
            }
        }

        $whereClause = 'WHERE ' . implode(' AND ', $whereParts);
        return [$whereClause, $params];
    }

    /**
     * 构建INSERT语句
     */
    public function buildInsert($table, $data) {
        $fields = array_keys($data);
        $values = array_values($data);
        
        $fieldList = '`' . implode('`, `', $fields) . '`';
        $placeholders = str_repeat('?,', count($fields) - 1) . '?';
        
        $sql = "INSERT INTO `$table` ($fieldList) VALUES ($placeholders)";
        
        return [$sql, $values];
    }

    /**
     * 构建UPDATE语句
     */
    public function buildUpdate($table, $data, $conditions) {
        $setParts = [];
        $params = [];

        foreach ($data as $field => $value) {
            $setParts[] = "`$field` = ?";
            $params[] = $value;
        }

        $setClause = implode(', ', $setParts);
        list($whereClause, $whereParams) = $this->buildWhere($conditions);
        
        $sql = "UPDATE `$table` SET $setClause $whereClause";
        $params = array_merge($params, $whereParams);

        return [$sql, $params];
    }

    /**
     * 简化的插入方法
     */
    public function insertData($table, $data) {
        list($sql, $params) = $this->buildInsert($table, $data);
        return $this->insert($sql, $params);
    }

    /**
     * 简化的更新方法
     */
    public function updateData($table, $data, $conditions) {
        list($sql, $params) = $this->buildUpdate($table, $data, $conditions);
        return $this->execute($sql, $params);
    }

    /**
     * 简化的删除方法
     */
    public function deleteData($table, $conditions) {
        list($whereClause, $params) = $this->buildWhere($conditions);
        $sql = "DELETE FROM `$table` $whereClause";
        return $this->execute($sql, $params);
    }

    /**
     * 简化的查询方法
     */
    public function selectData($table, $conditions = [], $fields = '*', $orderBy = '', $limit = '') {
        list($whereClause, $params) = $this->buildWhere($conditions);
        
        $sql = "SELECT $fields FROM `$table` $whereClause";
        
        if ($orderBy) {
            $sql .= " ORDER BY $orderBy";
        }
        
        if ($limit) {
            $sql .= " LIMIT $limit";
        }

        return $this->fetchAll($sql, $params);
    }

    /**
     * 获取单条记录
     */
    public function selectOne($table, $conditions = [], $fields = '*') {
        $data = $this->selectData($table, $conditions, $fields, '', '1');
        return empty($data) ? null : $data[0];
    }

    /**
     * 检查记录是否存在
     */
    public function exists($table, $conditions) {
        $result = $this->selectOne($table, $conditions, 'COUNT(*) as count');
        return $result && $result['count'] > 0;
    }

    /**
     * 获取记录总数
     */
    public function count($table, $conditions = []) {
        $result = $this->selectOne($table, $conditions, 'COUNT(*) as count');
        return $result ? (int)$result['count'] : 0;
    }

    /**
     * 防止克隆
     */
    private function __clone() {}

    /**
     * 防止反序列化
     */
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

// 全局数据库实例获取函数
function getDB() {
    return Database::getInstance();
}

?>
