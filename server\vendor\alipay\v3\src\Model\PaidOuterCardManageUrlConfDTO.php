<?php
/**
 * PaidOuterCardManageUrlConfDTO
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * PaidOuterCardManageUrlConfDTO Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class PaidOuterCardManageUrlConfDTO implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'PaidOuterCardManageUrlConfDTO';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'cycleManageUrl' => 'string',
        'downgradeUrl' => 'string',
        'refundUrl' => 'string',
        'renewUrl' => 'string',
        'upgradeUrl' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'cycleManageUrl' => null,
        'downgradeUrl' => null,
        'refundUrl' => null,
        'renewUrl' => null,
        'upgradeUrl' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'cycleManageUrl' => 'cycle_manage_url',
        'downgradeUrl' => 'downgrade_url',
        'refundUrl' => 'refund_url',
        'renewUrl' => 'renew_url',
        'upgradeUrl' => 'upgrade_url'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'cycleManageUrl' => 'setCycleManageUrl',
        'downgradeUrl' => 'setDowngradeUrl',
        'refundUrl' => 'setRefundUrl',
        'renewUrl' => 'setRenewUrl',
        'upgradeUrl' => 'setUpgradeUrl'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'cycleManageUrl' => 'getCycleManageUrl',
        'downgradeUrl' => 'getDowngradeUrl',
        'refundUrl' => 'getRefundUrl',
        'renewUrl' => 'getRenewUrl',
        'upgradeUrl' => 'getUpgradeUrl'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['cycleManageUrl'] = $data['cycleManageUrl'] ?? null;
        $this->container['downgradeUrl'] = $data['downgradeUrl'] ?? null;
        $this->container['refundUrl'] = $data['refundUrl'] ?? null;
        $this->container['renewUrl'] = $data['renewUrl'] ?? null;
        $this->container['upgradeUrl'] = $data['upgradeUrl'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets cycleManageUrl
     *
     * @return string|null
     */
    public function getCycleManageUrl()
    {
        return $this->container['cycleManageUrl'];
    }

    /**
     * Sets cycleManageUrl
     *
     * @param string|null $cycleManageUrl 连续购买管理地址。包括查看当前用户连续购买详情，关闭连续购买等功能
     *
     * @return self
     */
    public function setCycleManageUrl($cycleManageUrl)
    {
        $this->container['cycleManageUrl'] = $cycleManageUrl;

        return $this;
    }

    /**
     * Gets downgradeUrl
     *
     * @return string|null
     */
    public function getDowngradeUrl()
    {
        return $this->container['downgradeUrl'];
    }

    /**
     * Sets downgradeUrl
     *
     * @param string|null $downgradeUrl 付费外卡降级地址
     *
     * @return self
     */
    public function setDowngradeUrl($downgradeUrl)
    {
        $this->container['downgradeUrl'] = $downgradeUrl;

        return $this;
    }

    /**
     * Gets refundUrl
     *
     * @return string|null
     */
    public function getRefundUrl()
    {
        return $this->container['refundUrl'];
    }

    /**
     * Sets refundUrl
     *
     * @param string|null $refundUrl 续费外卡退款地址
     *
     * @return self
     */
    public function setRefundUrl($refundUrl)
    {
        $this->container['refundUrl'] = $refundUrl;

        return $this;
    }

    /**
     * Gets renewUrl
     *
     * @return string|null
     */
    public function getRenewUrl()
    {
        return $this->container['renewUrl'];
    }

    /**
     * Sets renewUrl
     *
     * @param string|null $renewUrl 付费外卡续费地址
     *
     * @return self
     */
    public function setRenewUrl($renewUrl)
    {
        $this->container['renewUrl'] = $renewUrl;

        return $this;
    }

    /**
     * Gets upgradeUrl
     *
     * @return string|null
     */
    public function getUpgradeUrl()
    {
        return $this->container['upgradeUrl'];
    }

    /**
     * Sets upgradeUrl
     *
     * @param string|null $upgradeUrl 付费外卡升级地址
     *
     * @return self
     */
    public function setUpgradeUrl($upgradeUrl)
    {
        $this->container['upgradeUrl'] = $upgradeUrl;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


