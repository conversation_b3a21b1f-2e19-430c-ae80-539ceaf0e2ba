<?php
/**
 * OrderVoucherAvailableShopResult
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * OrderVoucherAvailableShopResult Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class OrderVoucherAvailableShopResult implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'OrderVoucherAvailableShopResult';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'orderVoucherMerchantAllShopResult' => '\Alipay\OpenAPISDK\Model\OrderVoucherMerchantAllShopResult',
        'realShopFailInfos' => '\Alipay\OpenAPISDK\Model\OrderVoucherRealShopFailInfo[]',
        'shopFailInfos' => '\Alipay\OpenAPISDK\Model\OrderVoucherShopFailInfo[]',
        'successRealShopIds' => 'string[]',
        'successShopIds' => 'string[]'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'orderVoucherMerchantAllShopResult' => null,
        'realShopFailInfos' => null,
        'shopFailInfos' => null,
        'successRealShopIds' => null,
        'successShopIds' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'orderVoucherMerchantAllShopResult' => 'order_voucher_merchant_all_shop_result',
        'realShopFailInfos' => 'real_shop_fail_infos',
        'shopFailInfos' => 'shop_fail_infos',
        'successRealShopIds' => 'success_real_shop_ids',
        'successShopIds' => 'success_shop_ids'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'orderVoucherMerchantAllShopResult' => 'setOrderVoucherMerchantAllShopResult',
        'realShopFailInfos' => 'setRealShopFailInfos',
        'shopFailInfos' => 'setShopFailInfos',
        'successRealShopIds' => 'setSuccessRealShopIds',
        'successShopIds' => 'setSuccessShopIds'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'orderVoucherMerchantAllShopResult' => 'getOrderVoucherMerchantAllShopResult',
        'realShopFailInfos' => 'getRealShopFailInfos',
        'shopFailInfos' => 'getShopFailInfos',
        'successRealShopIds' => 'getSuccessRealShopIds',
        'successShopIds' => 'getSuccessShopIds'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['orderVoucherMerchantAllShopResult'] = $data['orderVoucherMerchantAllShopResult'] ?? null;
        $this->container['realShopFailInfos'] = $data['realShopFailInfos'] ?? null;
        $this->container['shopFailInfos'] = $data['shopFailInfos'] ?? null;
        $this->container['successRealShopIds'] = $data['successRealShopIds'] ?? null;
        $this->container['successShopIds'] = $data['successShopIds'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets orderVoucherMerchantAllShopResult
     *
     * @return \Alipay\OpenAPISDK\Model\OrderVoucherMerchantAllShopResult|null
     */
    public function getOrderVoucherMerchantAllShopResult()
    {
        return $this->container['orderVoucherMerchantAllShopResult'];
    }

    /**
     * Sets orderVoucherMerchantAllShopResult
     *
     * @param \Alipay\OpenAPISDK\Model\OrderVoucherMerchantAllShopResult|null $orderVoucherMerchantAllShopResult orderVoucherMerchantAllShopResult
     *
     * @return self
     */
    public function setOrderVoucherMerchantAllShopResult($orderVoucherMerchantAllShopResult)
    {
        $this->container['orderVoucherMerchantAllShopResult'] = $orderVoucherMerchantAllShopResult;

        return $this;
    }

    /**
     * Gets realShopFailInfos
     *
     * @return \Alipay\OpenAPISDK\Model\OrderVoucherRealShopFailInfo[]|null
     */
    public function getRealShopFailInfos()
    {
        return $this->container['realShopFailInfos'];
    }

    /**
     * Sets realShopFailInfos
     *
     * @param \Alipay\OpenAPISDK\Model\OrderVoucherRealShopFailInfo[]|null $realShopFailInfos 请求失败的代运营商业关系门店详情
     *
     * @return self
     */
    public function setRealShopFailInfos($realShopFailInfos)
    {
        $this->container['realShopFailInfos'] = $realShopFailInfos;

        return $this;
    }

    /**
     * Gets shopFailInfos
     *
     * @return \Alipay\OpenAPISDK\Model\OrderVoucherShopFailInfo[]|null
     */
    public function getShopFailInfos()
    {
        return $this->container['shopFailInfos'];
    }

    /**
     * Sets shopFailInfos
     *
     * @param \Alipay\OpenAPISDK\Model\OrderVoucherShopFailInfo[]|null $shopFailInfos 请求失败的支付宝门店详情。
     *
     * @return self
     */
    public function setShopFailInfos($shopFailInfos)
    {
        $this->container['shopFailInfos'] = $shopFailInfos;

        return $this;
    }

    /**
     * Gets successRealShopIds
     *
     * @return string[]|null
     */
    public function getSuccessRealShopIds()
    {
        return $this->container['successRealShopIds'];
    }

    /**
     * Sets successRealShopIds
     *
     * @param string[]|null $successRealShopIds 请求成功的代运营商业关系门店
     *
     * @return self
     */
    public function setSuccessRealShopIds($successRealShopIds)
    {
        $this->container['successRealShopIds'] = $successRealShopIds;

        return $this;
    }

    /**
     * Gets successShopIds
     *
     * @return string[]|null
     */
    public function getSuccessShopIds()
    {
        return $this->container['successShopIds'];
    }

    /**
     * Sets successShopIds
     *
     * @param string[]|null $successShopIds 请求成功的支付宝门店。
     *
     * @return self
     */
    public function setSuccessShopIds($successShopIds)
    {
        $this->container['successShopIds'] = $successShopIds;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


