<?php
/**
 * 获取资源详情API
 * 版本: 1.0.0
 * 创建时间: 2024-12-19
 */

require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/User.php';

// 验证请求方法
validateRequestMethod(['GET']);

try {
    // 验证用户Token
    $token = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    if (empty($token)) {
        handleError('缺少授权令牌', API_AUTH_ERROR_CODE);
    }
    
    $user = new User();
    $userId = $user->validateToken($token);
    
    if (!$userId) {
        handleError('无效的授权令牌', API_AUTH_ERROR_CODE);
    }
    
    // 获取资源ID
    $resourceId = $_GET['resource_id'] ?? '';
    
    if (empty($resourceId)) {
        handleError('资源ID不能为空', API_ERROR_CODE);
    }
    
    $db = getDB();
    
    // 获取资源详情
    $resource = $db->selectOne('resources', [
        'id' => $resourceId,
        'status' => 'active'
    ]);
    
    if (!$resource) {
        handleError('资源不存在或已下架', API_NOT_FOUND_CODE);
    }
    
    // 格式化资源数据
    $resource['id'] = (int)$resource['id'];
    $resource['points_cost'] = (int)$resource['points_cost'];
    $resource['download_count'] = (int)$resource['download_count'];
    $resource['file_size'] = $resource['file_size'] ? (int)$resource['file_size'] : null;
    $resource['duration'] = $resource['duration'] ? (int)$resource['duration'] : null;
    $resource['tags'] = $resource['tags'] ? explode(',', $resource['tags']) : [];
    
    // 检查用户是否已下载过此资源
    $downloadHistory = $db->selectOne('downloads', [
        'user_id' => $userId,
        'resource_id' => $resourceId,
        'status' => 'success'
    ]);
    
    $resource['downloaded'] = !empty($downloadHistory);
    $resource['download_time'] = $downloadHistory ? $downloadHistory['download_time'] : null;
    
    // 获取相关资源推荐（同平台或同类型）
    $relatedResources = $db->fetchAll("
        SELECT id, name, platform, points_cost, download_count, preview_url
        FROM resources 
        WHERE status = 'active' 
        AND id != ? 
        AND (platform = ? OR resource_type = ?)
        ORDER BY download_count DESC 
        LIMIT 5
    ", [$resourceId, $resource['platform'], $resource['resource_type']]);
    
    foreach ($relatedResources as &$related) {
        $related['id'] = (int)$related['id'];
        $related['points_cost'] = (int)$related['points_cost'];
        $related['download_count'] = (int)$related['download_count'];
    }
    
    // 记录查看日志
    writeLog("资源详情查看: 用户ID $userId, 资源ID $resourceId", 'INFO');
    
    // 返回成功响应
    jsonResponse([
        'resource' => $resource,
        'related_resources' => $relatedResources
    ], API_SUCCESS_CODE, '获取资源详情成功');
    
} catch (Exception $e) {
    // 记录错误日志
    writeLog("获取资源详情失败: " . $e->getMessage(), 'ERROR');
    
    // 返回错误响应
    handleError($e->getMessage(), API_AUTH_ERROR_CODE);
}

?>
