<?php
/**
 * IsvMerchantSalesDetailRequest
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * IsvMerchantSalesDetailRequest Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class IsvMerchantSalesDetailRequest implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'IsvMerchantSalesDetailRequest';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'couponsQuantity' => 'string',
        'deviceDetail' => 'string',
        'merchantPid' => 'string',
        'miniAppid' => 'string',
        'operationPlace' => 'string',
        'outBizNo' => 'string',
        'promotorPid' => 'string',
        'salesAmount' => 'string',
        'salesQuantity' => 'string',
        'subPromotorPid' => 'string',
        'writeOffAmount' => 'string',
        'writeOffQuantity' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'couponsQuantity' => null,
        'deviceDetail' => null,
        'merchantPid' => null,
        'miniAppid' => null,
        'operationPlace' => null,
        'outBizNo' => null,
        'promotorPid' => null,
        'salesAmount' => null,
        'salesQuantity' => null,
        'subPromotorPid' => null,
        'writeOffAmount' => null,
        'writeOffQuantity' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'couponsQuantity' => 'coupons_quantity',
        'deviceDetail' => 'device_detail',
        'merchantPid' => 'merchant_pid',
        'miniAppid' => 'mini_appid',
        'operationPlace' => 'operation_place',
        'outBizNo' => 'out_biz_no',
        'promotorPid' => 'promotor_pid',
        'salesAmount' => 'sales_amount',
        'salesQuantity' => 'sales_quantity',
        'subPromotorPid' => 'sub_promotor_pid',
        'writeOffAmount' => 'write_off_amount',
        'writeOffQuantity' => 'write_off_quantity'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'couponsQuantity' => 'setCouponsQuantity',
        'deviceDetail' => 'setDeviceDetail',
        'merchantPid' => 'setMerchantPid',
        'miniAppid' => 'setMiniAppid',
        'operationPlace' => 'setOperationPlace',
        'outBizNo' => 'setOutBizNo',
        'promotorPid' => 'setPromotorPid',
        'salesAmount' => 'setSalesAmount',
        'salesQuantity' => 'setSalesQuantity',
        'subPromotorPid' => 'setSubPromotorPid',
        'writeOffAmount' => 'setWriteOffAmount',
        'writeOffQuantity' => 'setWriteOffQuantity'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'couponsQuantity' => 'getCouponsQuantity',
        'deviceDetail' => 'getDeviceDetail',
        'merchantPid' => 'getMerchantPid',
        'miniAppid' => 'getMiniAppid',
        'operationPlace' => 'getOperationPlace',
        'outBizNo' => 'getOutBizNo',
        'promotorPid' => 'getPromotorPid',
        'salesAmount' => 'getSalesAmount',
        'salesQuantity' => 'getSalesQuantity',
        'subPromotorPid' => 'getSubPromotorPid',
        'writeOffAmount' => 'getWriteOffAmount',
        'writeOffQuantity' => 'getWriteOffQuantity'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['couponsQuantity'] = $data['couponsQuantity'] ?? null;
        $this->container['deviceDetail'] = $data['deviceDetail'] ?? null;
        $this->container['merchantPid'] = $data['merchantPid'] ?? null;
        $this->container['miniAppid'] = $data['miniAppid'] ?? null;
        $this->container['operationPlace'] = $data['operationPlace'] ?? null;
        $this->container['outBizNo'] = $data['outBizNo'] ?? null;
        $this->container['promotorPid'] = $data['promotorPid'] ?? null;
        $this->container['salesAmount'] = $data['salesAmount'] ?? null;
        $this->container['salesQuantity'] = $data['salesQuantity'] ?? null;
        $this->container['subPromotorPid'] = $data['subPromotorPid'] ?? null;
        $this->container['writeOffAmount'] = $data['writeOffAmount'] ?? null;
        $this->container['writeOffQuantity'] = $data['writeOffQuantity'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets couponsQuantity
     *
     * @return string|null
     */
    public function getCouponsQuantity()
    {
        return $this->container['couponsQuantity'];
    }

    /**
     * Sets couponsQuantity
     *
     * @param string|null $couponsQuantity 配券数
     *
     * @return self
     */
    public function setCouponsQuantity($couponsQuantity)
    {
        $this->container['couponsQuantity'] = $couponsQuantity;

        return $this;
    }

    /**
     * Gets deviceDetail
     *
     * @return string|null
     */
    public function getDeviceDetail()
    {
        return $this->container['deviceDetail'];
    }

    /**
     * Sets deviceDetail
     *
     * @param string|null $deviceDetail 设备详情
     *
     * @return self
     */
    public function setDeviceDetail($deviceDetail)
    {
        $this->container['deviceDetail'] = $deviceDetail;

        return $this;
    }

    /**
     * Gets merchantPid
     *
     * @return string|null
     */
    public function getMerchantPid()
    {
        return $this->container['merchantPid'];
    }

    /**
     * Sets merchantPid
     *
     * @param string|null $merchantPid 商户pid
     *
     * @return self
     */
    public function setMerchantPid($merchantPid)
    {
        $this->container['merchantPid'] = $merchantPid;

        return $this;
    }

    /**
     * Gets miniAppid
     *
     * @return string|null
     */
    public function getMiniAppid()
    {
        return $this->container['miniAppid'];
    }

    /**
     * Sets miniAppid
     *
     * @param string|null $miniAppid 小程序appid，若推广的商品不为小程序，则不传此参数
     *
     * @return self
     */
    public function setMiniAppid($miniAppid)
    {
        $this->container['miniAppid'] = $miniAppid;

        return $this;
    }

    /**
     * Gets operationPlace
     *
     * @return string|null
     */
    public function getOperationPlace()
    {
        return $this->container['operationPlace'];
    }

    /**
     * Sets operationPlace
     *
     * @param string|null $operationPlace 作业地
     *
     * @return self
     */
    public function setOperationPlace($operationPlace)
    {
        $this->container['operationPlace'] = $operationPlace;

        return $this;
    }

    /**
     * Gets outBizNo
     *
     * @return string|null
     */
    public function getOutBizNo()
    {
        return $this->container['outBizNo'];
    }

    /**
     * Sets outBizNo
     *
     * @param string|null $outBizNo 外部业务号，传isv系统生成的账单号，需要保证唯一
     *
     * @return self
     */
    public function setOutBizNo($outBizNo)
    {
        $this->container['outBizNo'] = $outBizNo;

        return $this;
    }

    /**
     * Gets promotorPid
     *
     * @return string|null
     */
    public function getPromotorPid()
    {
        return $this->container['promotorPid'];
    }

    /**
     * Sets promotorPid
     *
     * @param string|null $promotorPid 推广服务商(S2)pid
     *
     * @return self
     */
    public function setPromotorPid($promotorPid)
    {
        $this->container['promotorPid'] = $promotorPid;

        return $this;
    }

    /**
     * Gets salesAmount
     *
     * @return string|null
     */
    public function getSalesAmount()
    {
        return $this->container['salesAmount'];
    }

    /**
     * Sets salesAmount
     *
     * @param string|null $salesAmount 销售金额，这里填写的是整数，单位为分，比如1元，那么输入100
     *
     * @return self
     */
    public function setSalesAmount($salesAmount)
    {
        $this->container['salesAmount'] = $salesAmount;

        return $this;
    }

    /**
     * Gets salesQuantity
     *
     * @return string|null
     */
    public function getSalesQuantity()
    {
        return $this->container['salesQuantity'];
    }

    /**
     * Sets salesQuantity
     *
     * @param string|null $salesQuantity 销售笔数
     *
     * @return self
     */
    public function setSalesQuantity($salesQuantity)
    {
        $this->container['salesQuantity'] = $salesQuantity;

        return $this;
    }

    /**
     * Gets subPromotorPid
     *
     * @return string|null
     */
    public function getSubPromotorPid()
    {
        return $this->container['subPromotorPid'];
    }

    /**
     * Sets subPromotorPid
     *
     * @param string|null $subPromotorPid 推广服务商(S2)子账号pid
     *
     * @return self
     */
    public function setSubPromotorPid($subPromotorPid)
    {
        $this->container['subPromotorPid'] = $subPromotorPid;

        return $this;
    }

    /**
     * Gets writeOffAmount
     *
     * @return string|null
     */
    public function getWriteOffAmount()
    {
        return $this->container['writeOffAmount'];
    }

    /**
     * Sets writeOffAmount
     *
     * @param string|null $writeOffAmount 核销金额，这里填写的是整数，单位为分，比如1元，那么输入100
     *
     * @return self
     */
    public function setWriteOffAmount($writeOffAmount)
    {
        $this->container['writeOffAmount'] = $writeOffAmount;

        return $this;
    }

    /**
     * Gets writeOffQuantity
     *
     * @return string|null
     */
    public function getWriteOffQuantity()
    {
        return $this->container['writeOffQuantity'];
    }

    /**
     * Sets writeOffQuantity
     *
     * @param string|null $writeOffQuantity 核销数
     *
     * @return self
     */
    public function setWriteOffQuantity($writeOffQuantity)
    {
        $this->container['writeOffQuantity'] = $writeOffQuantity;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


