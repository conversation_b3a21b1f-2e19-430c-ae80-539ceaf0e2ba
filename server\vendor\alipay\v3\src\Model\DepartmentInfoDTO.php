<?php
/**
 * DepartmentInfoDTO
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * DepartmentInfoDTO Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class DepartmentInfoDTO implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'DepartmentInfoDTO';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'departmentCode' => 'string',
        'departmentId' => 'string',
        'departmentName' => 'string',
        'deptChargeEmployeeId' => 'string',
        'gmtCreate' => 'string',
        'gmtModified' => 'string',
        'parentDepartmentId' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'departmentCode' => null,
        'departmentId' => null,
        'departmentName' => null,
        'deptChargeEmployeeId' => null,
        'gmtCreate' => null,
        'gmtModified' => null,
        'parentDepartmentId' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'departmentCode' => 'department_code',
        'departmentId' => 'department_id',
        'departmentName' => 'department_name',
        'deptChargeEmployeeId' => 'dept_charge_employee_id',
        'gmtCreate' => 'gmt_create',
        'gmtModified' => 'gmt_modified',
        'parentDepartmentId' => 'parent_department_id'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'departmentCode' => 'setDepartmentCode',
        'departmentId' => 'setDepartmentId',
        'departmentName' => 'setDepartmentName',
        'deptChargeEmployeeId' => 'setDeptChargeEmployeeId',
        'gmtCreate' => 'setGmtCreate',
        'gmtModified' => 'setGmtModified',
        'parentDepartmentId' => 'setParentDepartmentId'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'departmentCode' => 'getDepartmentCode',
        'departmentId' => 'getDepartmentId',
        'departmentName' => 'getDepartmentName',
        'deptChargeEmployeeId' => 'getDeptChargeEmployeeId',
        'gmtCreate' => 'getGmtCreate',
        'gmtModified' => 'getGmtModified',
        'parentDepartmentId' => 'getParentDepartmentId'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['departmentCode'] = $data['departmentCode'] ?? null;
        $this->container['departmentId'] = $data['departmentId'] ?? null;
        $this->container['departmentName'] = $data['departmentName'] ?? null;
        $this->container['deptChargeEmployeeId'] = $data['deptChargeEmployeeId'] ?? null;
        $this->container['gmtCreate'] = $data['gmtCreate'] ?? null;
        $this->container['gmtModified'] = $data['gmtModified'] ?? null;
        $this->container['parentDepartmentId'] = $data['parentDepartmentId'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets departmentCode
     *
     * @return string|null
     */
    public function getDepartmentCode()
    {
        return $this->container['departmentCode'];
    }

    /**
     * Sets departmentCode
     *
     * @param string|null $departmentCode 部门编码
     *
     * @return self
     */
    public function setDepartmentCode($departmentCode)
    {
        $this->container['departmentCode'] = $departmentCode;

        return $this;
    }

    /**
     * Gets departmentId
     *
     * @return string|null
     */
    public function getDepartmentId()
    {
        return $this->container['departmentId'];
    }

    /**
     * Sets departmentId
     *
     * @param string|null $departmentId 部门id
     *
     * @return self
     */
    public function setDepartmentId($departmentId)
    {
        $this->container['departmentId'] = $departmentId;

        return $this;
    }

    /**
     * Gets departmentName
     *
     * @return string|null
     */
    public function getDepartmentName()
    {
        return $this->container['departmentName'];
    }

    /**
     * Sets departmentName
     *
     * @param string|null $departmentName 部门名称
     *
     * @return self
     */
    public function setDepartmentName($departmentName)
    {
        $this->container['departmentName'] = $departmentName;

        return $this;
    }

    /**
     * Gets deptChargeEmployeeId
     *
     * @return string|null
     */
    public function getDeptChargeEmployeeId()
    {
        return $this->container['deptChargeEmployeeId'];
    }

    /**
     * Sets deptChargeEmployeeId
     *
     * @param string|null $deptChargeEmployeeId 部门负责人员工ID
     *
     * @return self
     */
    public function setDeptChargeEmployeeId($deptChargeEmployeeId)
    {
        $this->container['deptChargeEmployeeId'] = $deptChargeEmployeeId;

        return $this;
    }

    /**
     * Gets gmtCreate
     *
     * @return string|null
     */
    public function getGmtCreate()
    {
        return $this->container['gmtCreate'];
    }

    /**
     * Sets gmtCreate
     *
     * @param string|null $gmtCreate 创建时间
     *
     * @return self
     */
    public function setGmtCreate($gmtCreate)
    {
        $this->container['gmtCreate'] = $gmtCreate;

        return $this;
    }

    /**
     * Gets gmtModified
     *
     * @return string|null
     */
    public function getGmtModified()
    {
        return $this->container['gmtModified'];
    }

    /**
     * Sets gmtModified
     *
     * @param string|null $gmtModified 修改时间
     *
     * @return self
     */
    public function setGmtModified($gmtModified)
    {
        $this->container['gmtModified'] = $gmtModified;

        return $this;
    }

    /**
     * Gets parentDepartmentId
     *
     * @return string|null
     */
    public function getParentDepartmentId()
    {
        return $this->container['parentDepartmentId'];
    }

    /**
     * Sets parentDepartmentId
     *
     * @param string|null $parentDepartmentId 上级部门id，特殊值-1表示根部门
     *
     * @return self
     */
    public function setParentDepartmentId($parentDepartmentId)
    {
        $this->container['parentDepartmentId'] = $parentDepartmentId;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


