<?php
/**
 * Topic
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * Topic Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class Topic implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'Topic';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'imgUrl' => 'string',
        'linkType' => 'string',
        'linkUrl' => 'string',
        'subTitle' => 'string',
        'title' => 'string',
        'topicId' => 'string',
        'topicItems' => '\Alipay\OpenAPISDK\Model\TopicItem[]'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'imgUrl' => null,
        'linkType' => null,
        'linkUrl' => null,
        'subTitle' => null,
        'title' => null,
        'topicId' => null,
        'topicItems' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'imgUrl' => 'img_url',
        'linkType' => 'link_type',
        'linkUrl' => 'link_url',
        'subTitle' => 'sub_title',
        'title' => 'title',
        'topicId' => 'topic_id',
        'topicItems' => 'topic_items'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'imgUrl' => 'setImgUrl',
        'linkType' => 'setLinkType',
        'linkUrl' => 'setLinkUrl',
        'subTitle' => 'setSubTitle',
        'title' => 'setTitle',
        'topicId' => 'setTopicId',
        'topicItems' => 'setTopicItems'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'imgUrl' => 'getImgUrl',
        'linkType' => 'getLinkType',
        'linkUrl' => 'getLinkUrl',
        'subTitle' => 'getSubTitle',
        'title' => 'getTitle',
        'topicId' => 'getTopicId',
        'topicItems' => 'getTopicItems'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['imgUrl'] = $data['imgUrl'] ?? null;
        $this->container['linkType'] = $data['linkType'] ?? null;
        $this->container['linkUrl'] = $data['linkUrl'] ?? null;
        $this->container['subTitle'] = $data['subTitle'] ?? null;
        $this->container['title'] = $data['title'] ?? null;
        $this->container['topicId'] = $data['topicId'] ?? null;
        $this->container['topicItems'] = $data['topicItems'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets imgUrl
     *
     * @return string|null
     */
    public function getImgUrl()
    {
        return $this->container['imgUrl'];
    }

    /**
     * Sets imgUrl
     *
     * @param string|null $imgUrl 营销位图片url
     *
     * @return self
     */
    public function setImgUrl($imgUrl)
    {
        $this->container['imgUrl'] = $imgUrl;

        return $this;
    }

    /**
     * Gets linkType
     *
     * @return string|null
     */
    public function getLinkType()
    {
        return $this->container['linkType'];
    }

    /**
     * Sets linkType
     *
     * @param string|null $linkType 跳转类型，网页:HTTP、小程序:APP
     *
     * @return self
     */
    public function setLinkType($linkType)
    {
        $this->container['linkType'] = $linkType;

        return $this;
    }

    /**
     * Gets linkUrl
     *
     * @return string|null
     */
    public function getLinkUrl()
    {
        return $this->container['linkUrl'];
    }

    /**
     * Sets linkUrl
     *
     * @param string|null $linkUrl 营销位跳转地址，点击营销位头图跳到的链接url。
     *
     * @return self
     */
    public function setLinkUrl($linkUrl)
    {
        $this->container['linkUrl'] = $linkUrl;

        return $this;
    }

    /**
     * Gets subTitle
     *
     * @return string|null
     */
    public function getSubTitle()
    {
        return $this->container['subTitle'];
    }

    /**
     * Sets subTitle
     *
     * @param string|null $subTitle 营销位描述
     *
     * @return self
     */
    public function setSubTitle($subTitle)
    {
        $this->container['subTitle'] = $subTitle;

        return $this;
    }

    /**
     * Gets title
     *
     * @return string|null
     */
    public function getTitle()
    {
        return $this->container['title'];
    }

    /**
     * Sets title
     *
     * @param string|null $title 营销位名称
     *
     * @return self
     */
    public function setTitle($title)
    {
        $this->container['title'] = $title;

        return $this;
    }

    /**
     * Gets topicId
     *
     * @return string|null
     */
    public function getTopicId()
    {
        return $this->container['topicId'];
    }

    /**
     * Sets topicId
     *
     * @param string|null $topicId 营销位id
     *
     * @return self
     */
    public function setTopicId($topicId)
    {
        $this->container['topicId'] = $topicId;

        return $this;
    }

    /**
     * Gets topicItems
     *
     * @return \Alipay\OpenAPISDK\Model\TopicItem[]|null
     */
    public function getTopicItems()
    {
        return $this->container['topicItems'];
    }

    /**
     * Sets topicItems
     *
     * @param \Alipay\OpenAPISDK\Model\TopicItem[]|null $topicItems 营销位内容列表
     *
     * @return self
     */
    public function setTopicItems($topicItems)
    {
        $this->container['topicItems'] = $topicItems;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


