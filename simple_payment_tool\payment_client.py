#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
支付API客户端
版本: 1.0.0
创建时间: 2024-12-19
"""

import requests
import json
import time
from typing import Dict, Optional, Any
from colorama import Fore, Style, init

# 初始化colorama
init(autoreset=True)

class PaymentClient:
    """支付API客户端"""
    
    def __init__(self, api_base_url: str, token: str = None):
        """
        初始化支付客户端
        
        Args:
            api_base_url: API基础URL
            token: 用户认证token（可选，用于需要认证的API）
        """
        self.api_base_url = api_base_url.rstrip('/')
        self.token = token
        self.session = requests.Session()
        
        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'SimplePaymentTool/1.0.0'
        })
        
        if self.token:
            self.session.headers.update({
                'Authorization': f'Bearer {self.token}'
            })
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None, 
                     params: Optional[Dict] = None) -> Dict[str, Any]:
        """
        发送API请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            data: 请求数据
            params: URL参数
            
        Returns:
            API响应数据
            
        Raises:
            Exception: 请求失败时抛出异常
        """
        url = f"{self.api_base_url}/{endpoint.lstrip('/')}"
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, params=params, timeout=30)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data, params=params, timeout=30)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            # 检查HTTP状态码
            response.raise_for_status()
            
            # 解析JSON响应
            try:
                result = response.json()
            except json.JSONDecodeError:
                raise Exception(f"API返回非JSON格式数据: {response.text}")
            
            # 检查业务状态
            if not result.get('success', False):
                error_msg = result.get('message', '未知错误')
                raise Exception(f"API调用失败: {error_msg}")
            
            return result
            
        except requests.exceptions.Timeout:
            raise Exception("请求超时，请检查网络连接")
        except requests.exceptions.ConnectionError:
            raise Exception("连接失败，请检查服务器地址和网络连接")
        except requests.exceptions.HTTPError as e:
            raise Exception(f"HTTP错误: {e}")
        except Exception as e:
            raise e
    
    def create_order(self, amount: float) -> Dict[str, Any]:
        """
        创建支付订单
        
        Args:
            amount: 支付金额
            
        Returns:
            订单信息字典，包含订单号、二维码等
            
        Raises:
            Exception: 创建订单失败时抛出异常
        """
        if amount <= 0:
            raise ValueError("支付金额必须大于0")
        
        if amount > 10000:
            raise ValueError("单次充值金额不能超过10000元")
        
        # 验证金额精度（最多2位小数）
        if round(amount, 2) != amount:
            raise ValueError("金额最多支持2位小数")
        
        print(f"{Fore.BLUE}正在创建支付订单，金额: ¥{amount:.2f}...")
        
        try:
            data = {'amount': amount}
            result = self._make_request('POST', 'payment/create.php', data)
            
            order_info = result['data']
            print(f"{Fore.GREEN}✓ 订单创建成功!")
            print(f"  订单号: {order_info['order_number']}")
            print(f"  金额: ¥{order_info['amount']:.2f}")
            print(f"  积分: {order_info['points']}")
            print(f"  过期时间: {order_info['expires_at']}")
            
            return order_info
            
        except Exception as e:
            print(f"{Fore.RED}✗ 创建订单失败: {e}")
            raise e
    
    def query_order_status(self, order_number: str) -> Dict[str, Any]:
        """
        查询订单状态
        
        Args:
            order_number: 订单号
            
        Returns:
            订单状态信息
            
        Raises:
            Exception: 查询失败时抛出异常
        """
        if not order_number:
            raise ValueError("订单号不能为空")
        
        try:
            params = {'order_number': order_number}
            result = self._make_request('GET', 'payment/query.php', params=params)
            
            return result['data']
            
        except Exception as e:
            print(f"{Fore.RED}✗ 查询订单状态失败: {e}")
            raise e
    
    def wait_for_payment(self, order_number: str, timeout: int = 300, 
                        check_interval: int = 3) -> Dict[str, Any]:
        """
        等待支付完成
        
        Args:
            order_number: 订单号
            timeout: 超时时间（秒）
            check_interval: 检查间隔（秒）
            
        Returns:
            支付结果信息
        """
        start_time = time.time()
        print(f"{Fore.YELLOW}等待支付完成...")
        print(f"订单号: {order_number}")
        print(f"超时时间: {timeout}秒")
        print(f"检查间隔: {check_interval}秒")
        print()
        
        while time.time() - start_time < timeout:
            try:
                order_info = self.query_order_status(order_number)
                status = order_info['status']
                
                if status == 'completed':
                    print(f"{Fore.GREEN}🎉 支付成功!")
                    print(f"  订单号: {order_number}")
                    print(f"  支付宝交易号: {order_info.get('trade_no', 'N/A')}")
                    print(f"  支付时间: {order_info.get('paid_at', 'N/A')}")
                    return {
                        'success': True,
                        'status': 'completed',
                        'order_info': order_info,
                        'message': '支付成功'
                    }
                elif status in ['failed', 'cancelled', 'expired']:
                    status_map = {
                        'failed': '失败',
                        'cancelled': '已取消', 
                        'expired': '已过期'
                    }
                    print(f"{Fore.RED}❌ 支付{status_map.get(status, status)}")
                    return {
                        'success': False,
                        'status': status,
                        'order_info': order_info,
                        'message': f"支付{status_map.get(status, status)}"
                    }
                
                # 显示等待状态
                elapsed = int(time.time() - start_time)
                remaining = timeout - elapsed
                print(f"\r{Fore.CYAN}⏳ 等待支付中... ({elapsed}s/{timeout}s, 剩余{remaining}s)", end='', flush=True)
                
                # 等待下次检查
                time.sleep(check_interval)
                
            except Exception as e:
                print(f"\r{Fore.YELLOW}⚠ 检查支付状态时出错: {e}", flush=True)
                time.sleep(check_interval)
        
        # 超时
        print(f"\n{Fore.RED}⏰ 支付等待超时")
        return {
            'success': False,
            'status': 'timeout',
            'order_info': None,
            'message': '支付等待超时'
        }
    
    def format_amount(self, amount: float) -> str:
        """格式化金额显示"""
        return f"¥{amount:.2f}"
    
    def validate_amount(self, amount_str: str) -> float:
        """
        验证并转换金额
        
        Args:
            amount_str: 金额字符串
            
        Returns:
            转换后的金额
            
        Raises:
            ValueError: 金额格式错误时抛出异常
        """
        try:
            amount = float(amount_str)
        except ValueError:
            raise ValueError("金额格式错误，请输入有效数字")
        
        if amount <= 0:
            raise ValueError("金额必须大于0")
        
        if amount > 10000:
            raise ValueError("单次充值金额不能超过10000元")
        
        # 检查小数位数
        if round(amount, 2) != amount:
            raise ValueError("金额最多支持2位小数")
        
        return amount

__all__ = ['PaymentClient']
