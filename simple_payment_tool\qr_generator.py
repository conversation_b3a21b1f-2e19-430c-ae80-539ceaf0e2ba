#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
二维码生成器
版本: 1.0.0
创建时间: 2024-12-19
"""

import os
import sys
import qrcode
import platform
import subprocess
from io import BytesIO
from PIL import Image, ImageDraw, ImageFont
from typing import Optional
from colorama import Fore, Style, init

# 初始化colorama
init(autoreset=True)

class QRGenerator:
    """二维码生成器"""
    
    def __init__(self, size: int = 300, border: int = 4):
        """
        初始化二维码生成器
        
        Args:
            size: 二维码图片大小（像素）
            border: 二维码边框大小
        """
        self.size = size
        self.border = border
        
    def generate_qr_code(self, content: str, title: Optional[str] = None) -> Image.Image:
        """
        生成二维码图片
        
        Args:
            content: 二维码内容
            title: 可选的标题文字
            
        Returns:
            PIL Image对象
        """
        try:
            # 创建二维码对象
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=self.border,
            )
            
            # 添加数据
            qr.add_data(content)
            qr.make(fit=True)
            
            # 生成二维码图片
            qr_img = qr.make_image(fill_color="black", back_color="white")
            
            # 调整大小
            qr_img = qr_img.resize((self.size, self.size), Image.Resampling.LANCZOS)
            
            # 如果有标题，添加标题
            if title:
                qr_img = self._add_title(qr_img, title)
            
            return qr_img
            
        except Exception as e:
            raise Exception(f"生成二维码失败: {e}")
    
    def _add_title(self, qr_img: Image.Image, title: str) -> Image.Image:
        """
        为二维码添加标题
        
        Args:
            qr_img: 二维码图片
            title: 标题文字
            
        Returns:
            添加标题后的图片
        """
        try:
            # 计算新图片尺寸
            title_height = 60
            new_width = qr_img.width
            new_height = qr_img.height + title_height
            
            # 创建新图片
            new_img = Image.new('RGB', (new_width, new_height), 'white')
            
            # 粘贴二维码
            new_img.paste(qr_img, (0, title_height))
            
            # 添加标题文字
            draw = ImageDraw.Draw(new_img)
            
            # 尝试使用系统字体
            try:
                if platform.system() == "Windows":
                    font = ImageFont.truetype("msyh.ttc", 24)  # 微软雅黑
                elif platform.system() == "Darwin":  # macOS
                    font = ImageFont.truetype("/System/Library/Fonts/PingFang.ttc", 24)
                else:  # Linux
                    font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 24)
            except:
                # 如果找不到字体，使用默认字体
                font = ImageFont.load_default()
            
            # 计算文字位置（居中）
            bbox = draw.textbbox((0, 0), title, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            x = (new_width - text_width) // 2
            y = (title_height - text_height) // 2
            
            # 绘制文字
            draw.text((x, y), title, fill='black', font=font)
            
            return new_img
            
        except Exception as e:
            print(f"{Fore.YELLOW}⚠ 添加标题失败，使用原始二维码: {e}")
            return qr_img
    
    def save_qr_code(self, content: str, file_path: str, title: Optional[str] = None) -> bool:
        """
        保存二维码到文件
        
        Args:
            content: 二维码内容
            file_path: 保存路径
            title: 可选的标题文字
            
        Returns:
            是否保存成功
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 生成二维码
            qr_img = self.generate_qr_code(content, title)
            
            # 保存图片
            qr_img.save(file_path, 'PNG', quality=95)
            
            print(f"{Fore.GREEN}✓ 二维码已保存: {file_path}")
            return True
            
        except Exception as e:
            print(f"{Fore.RED}✗ 保存二维码失败: {e}")
            return False
    
    def display_qr_code(self, content: str, title: Optional[str] = None, 
                       save_path: Optional[str] = None) -> bool:
        """
        显示二维码
        
        Args:
            content: 二维码内容
            title: 可选的标题文字
            save_path: 可选的保存路径
            
        Returns:
            是否显示成功
        """
        try:
            # 生成二维码
            qr_img = self.generate_qr_code(content, title)
            
            # 如果指定了保存路径，先保存
            if save_path:
                self.save_qr_code(content, save_path, title)
                img_path = save_path
            else:
                # 保存到临时文件
                temp_dir = os.path.join(os.getcwd(), 'temp')
                os.makedirs(temp_dir, exist_ok=True)
                img_path = os.path.join(temp_dir, 'payment_qr.png')
                qr_img.save(img_path, 'PNG')
            
            # 尝试打开图片
            success = self._open_image(img_path)
            
            if success:
                print(f"{Fore.GREEN}✓ 二维码已显示")
                print(f"{Fore.CYAN}📱 请使用支付宝扫描二维码完成支付")
                return True
            else:
                print(f"{Fore.YELLOW}⚠ 无法自动打开图片，请手动打开: {img_path}")
                return False
                
        except Exception as e:
            print(f"{Fore.RED}✗ 显示二维码失败: {e}")
            return False
    
    def _open_image(self, image_path: str) -> bool:
        """
        使用系统默认程序打开图片
        
        Args:
            image_path: 图片路径
            
        Returns:
            是否成功打开
        """
        try:
            system = platform.system()
            
            if system == "Windows":
                os.startfile(image_path)
            elif system == "Darwin":  # macOS
                subprocess.run(["open", image_path], check=True)
            else:  # Linux
                subprocess.run(["xdg-open", image_path], check=True)
            
            return True
            
        except Exception as e:
            print(f"{Fore.YELLOW}⚠ 打开图片失败: {e}")
            return False
    
    def print_qr_code_ascii(self, content: str) -> bool:
        """
        在终端中打印ASCII二维码（备用方案）
        
        Args:
            content: 二维码内容
            
        Returns:
            是否打印成功
        """
        try:
            # 创建简单的二维码
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=1,
                border=1,
            )
            qr.add_data(content)
            qr.make(fit=True)
            
            # 打印ASCII二维码
            print(f"{Fore.CYAN}ASCII二维码:")
            qr.print_ascii(out=sys.stdout, tty=True, invert=True)
            print()
            
            return True
            
        except Exception as e:
            print(f"{Fore.RED}✗ 打印ASCII二维码失败: {e}")
            return False
    
    def get_qr_code_bytes(self, content: str, title: Optional[str] = None) -> bytes:
        """
        获取二维码图片的字节数据
        
        Args:
            content: 二维码内容
            title: 可选的标题文字
            
        Returns:
            图片字节数据
        """
        try:
            qr_img = self.generate_qr_code(content, title)
            
            # 转换为字节流
            img_buffer = BytesIO()
            qr_img.save(img_buffer, format='PNG')
            img_bytes = img_buffer.getvalue()
            
            return img_bytes
            
        except Exception as e:
            raise Exception(f"获取二维码字节数据失败: {e}")

__all__ = ['QRGenerator']
