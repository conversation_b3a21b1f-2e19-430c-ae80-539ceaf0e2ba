<?php
/**
 * PaymentVoucherUseRuleModify
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * PaymentVoucherUseRuleModify Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class PaymentVoucherUseRuleModify implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'PaymentVoucherUseRuleModify';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'availableAppIds' => 'string',
        'availableGoods' => '\Alipay\OpenAPISDK\Model\PaymentVoucherAvailableGoodsModify',
        'availableMerchant' => '\Alipay\OpenAPISDK\Model\PaymentVoucherAvailableMerchantModify',
        'availableStoreIds' => 'string',
        'voucherValidPeriod' => '\Alipay\OpenAPISDK\Model\PaymentVoucherValidPeriodModify'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'availableAppIds' => null,
        'availableGoods' => null,
        'availableMerchant' => null,
        'availableStoreIds' => null,
        'voucherValidPeriod' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'availableAppIds' => 'available_app_ids',
        'availableGoods' => 'available_goods',
        'availableMerchant' => 'available_merchant',
        'availableStoreIds' => 'available_store_ids',
        'voucherValidPeriod' => 'voucher_valid_period'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'availableAppIds' => 'setAvailableAppIds',
        'availableGoods' => 'setAvailableGoods',
        'availableMerchant' => 'setAvailableMerchant',
        'availableStoreIds' => 'setAvailableStoreIds',
        'voucherValidPeriod' => 'setVoucherValidPeriod'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'availableAppIds' => 'getAvailableAppIds',
        'availableGoods' => 'getAvailableGoods',
        'availableMerchant' => 'getAvailableMerchant',
        'availableStoreIds' => 'getAvailableStoreIds',
        'voucherValidPeriod' => 'getVoucherValidPeriod'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['availableAppIds'] = $data['availableAppIds'] ?? null;
        $this->container['availableGoods'] = $data['availableGoods'] ?? null;
        $this->container['availableMerchant'] = $data['availableMerchant'] ?? null;
        $this->container['availableStoreIds'] = $data['availableStoreIds'] ?? null;
        $this->container['voucherValidPeriod'] = $data['voucherValidPeriod'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets availableAppIds
     *
     * @return string|null
     */
    public function getAvailableAppIds()
    {
        return $this->container['availableAppIds'];
    }

    /**
     * Sets availableAppIds
     *
     * @param string|null $availableAppIds 可核销的支付宝小程序id   限制：  1、必须是支付宝小程序  2、如果包含重复的小程序id会自动进行去重操作。 3、可核销小程序范围只能扩大不能缩小
     *
     * @return self
     */
    public function setAvailableAppIds($availableAppIds)
    {
        $this->container['availableAppIds'] = $availableAppIds;

        return $this;
    }

    /**
     * Gets availableGoods
     *
     * @return \Alipay\OpenAPISDK\Model\PaymentVoucherAvailableGoodsModify|null
     */
    public function getAvailableGoods()
    {
        return $this->container['availableGoods'];
    }

    /**
     * Sets availableGoods
     *
     * @param \Alipay\OpenAPISDK\Model\PaymentVoucherAvailableGoodsModify|null $availableGoods availableGoods
     *
     * @return self
     */
    public function setAvailableGoods($availableGoods)
    {
        $this->container['availableGoods'] = $availableGoods;

        return $this;
    }

    /**
     * Gets availableMerchant
     *
     * @return \Alipay\OpenAPISDK\Model\PaymentVoucherAvailableMerchantModify|null
     */
    public function getAvailableMerchant()
    {
        return $this->container['availableMerchant'];
    }

    /**
     * Sets availableMerchant
     *
     * @param \Alipay\OpenAPISDK\Model\PaymentVoucherAvailableMerchantModify|null $availableMerchant availableMerchant
     *
     * @return self
     */
    public function setAvailableMerchant($availableMerchant)
    {
        $this->container['availableMerchant'] = $availableMerchant;

        return $this;
    }

    /**
     * Gets availableStoreIds
     *
     * @return string|null
     */
    public function getAvailableStoreIds()
    {
        return $this->container['availableStoreIds'];
    }

    /**
     * Sets availableStoreIds
     *
     * @param string|null $availableStoreIds 可核销支付门店id。   限制：  1、available_store_ids中的门店id必须是支付宝门店id。  2、available_store_ids如果包含重复的门店id会自动进行去重操作。 3、可核销门店范围只能扩大不能缩小
     *
     * @return self
     */
    public function setAvailableStoreIds($availableStoreIds)
    {
        $this->container['availableStoreIds'] = $availableStoreIds;

        return $this;
    }

    /**
     * Gets voucherValidPeriod
     *
     * @return \Alipay\OpenAPISDK\Model\PaymentVoucherValidPeriodModify|null
     */
    public function getVoucherValidPeriod()
    {
        return $this->container['voucherValidPeriod'];
    }

    /**
     * Sets voucherValidPeriod
     *
     * @param \Alipay\OpenAPISDK\Model\PaymentVoucherValidPeriodModify|null $voucherValidPeriod voucherValidPeriod
     *
     * @return self
     */
    public function setVoucherValidPeriod($voucherValidPeriod)
    {
        $this->container['voucherValidPeriod'] = $voucherValidPeriod;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


