<?php
/**
 * 消费积分API
 * 版本: 1.0.0
 * 创建时间: 2024-12-19
 */

require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/User.php';
require_once '../../includes/Points.php';

// 验证请求方法
validateRequestMethod(['POST']);

try {
    // 验证用户Token
    $token = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    if (empty($token)) {
        handleError('缺少授权令牌', API_AUTH_ERROR_CODE);
    }
    
    $user = new User();
    $userId = $user->validateToken($token);
    
    if (!$userId) {
        handleError('无效的授权令牌', API_AUTH_ERROR_CODE);
    }
    
    // 获取请求数据
    $data = getRequestData();
    
    // 验证必需参数
    if (!isset($data['points']) || !is_numeric($data['points'])) {
        handleError('积分数量必须是有效数字', API_ERROR_CODE);
    }
    
    $points = (int)$data['points'];
    if ($points <= 0) {
        handleError('积分数量必须大于0', API_ERROR_CODE);
    }
    
    $resourceId = $data['resource_id'] ?? null;
    $description = $data['description'] ?? '下载资源';
    
    // 执行积分消费
    $pointsManager = new Points($userId);
    $result = $pointsManager->consumePoints($points, $resourceId, $description);
    
    // 记录消费日志
    writeLog("积分消费成功: 用户ID $userId, 消费 $points 积分, 资源ID $resourceId", 'INFO');
    
    // 返回成功响应
    jsonResponse([
        'balance' => $result['balance'],
        'consumed' => $result['consumed'],
        'resource_id' => $resourceId
    ], API_SUCCESS_CODE, '积分消费成功');
    
} catch (Exception $e) {
    // 记录错误日志
    writeLog("积分消费失败: " . $e->getMessage(), 'ERROR');
    
    // 返回错误响应
    handleError($e->getMessage(), API_ERROR_CODE);
}

?>
