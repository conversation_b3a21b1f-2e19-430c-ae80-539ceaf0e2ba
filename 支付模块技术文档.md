# 积分支付系统技术文档

## 📋 目录
- [系统概述](#系统概述)
- [技术架构](#技术架构)
- [核心功能](#核心功能)
- [数据库设计](#数据库设计)
- [API接口](#api接口)
- [支付流程](#支付流程)
- [代码实现](#代码实现)
- [配置说明](#配置说明)
- [部署指南](#部署指南)
- [集成指南](#集成指南)
- [安全机制](#安全机制)
- [错误处理](#错误处理)
- [日志系统](#日志系统)
- [测试方案](#测试方案)
- [常见问题](#常见问题)

---

## 🎯 系统概述

### 功能特性
- ✅ **支付宝当面付集成** - 基于官方SDK的标准实现
- ✅ **积分充值系统** - 1元=1积分的充值机制
- ✅ **订单管理** - 完整的订单生命周期管理
- ✅ **积分管理** - 充值、消费、退还、统计
- ✅ **安全验签** - RSA2签名验证机制
- ✅ **事务保护** - 数据库事务确保一致性
- ✅ **异步回调** - 支付宝异步通知处理
- ✅ **二维码生成** - 支付二维码自动生成
- ✅ **订单超时** - 自动过期机制
- ✅ **日志记录** - 完整的操作日志

### 技术栈
- **后端**: PHP 7.4+ (支持PHP 8.x)
- **数据库**: MySQL 5.7+ / MariaDB 10.3+
- **支付**: 支付宝当面付官方SDK v2.0
- **客户端**: Python 3.8+ (可选)
- **加密**: RSA2签名算法
- **架构**: RESTful API + 单例数据库连接

---

## 🏗️ 技术架构

### 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   客户端应用     │    │   PHP后端API    │    │   支付宝服务     │
│                │    │                │    │                │
│ PaymentManager  │◄──►│ Payment.php     │◄──►│ 当面付API       │
│ PointsManager   │    │ Points.php      │    │ 异步回调        │
│                │    │ Database.php    │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   MySQL数据库    │
                       │                │
                       │ orders         │
                       │ points         │
                       │ point_transactions│
                       │ users          │
                       └─────────────────┘
```

### 核心组件

#### 1. 支付管理器 (Payment.php)
- 订单创建与管理
- 支付宝API集成
- 回调处理与验签
- 订单状态查询

#### 2. 积分管理器 (Points.php)
- 积分账户管理
- 充值/消费/退还
- 交易记录
- 统计分析

#### 3. 数据库管理器 (Database.php)
- 单例连接池
- 事务管理
- SQL构建器
- 安全查询

#### 4. 客户端SDK (Python)
- 支付接口封装
- 二维码生成
- 状态轮询
- 错误处理

---

## 💰 核心功能

### 1. 支付订单管理

#### 订单创建流程
1. **参数验证** - 金额范围检查 (0 < amount <= 10000)
2. **订单生成** - 生成唯一订单号 (PD + 时间戳 + 随机数)
3. **支付宝调用** - 创建当面付订单
4. **数据库保存** - 保存订单信息和二维码
5. **返回结果** - 返回订单详情和支付二维码

#### 订单状态管理
- `pending` - 待支付
- `completed` - 已完成
- `failed` - 支付失败
- `cancelled` - 已取消
- `expired` - 已过期

### 2. 积分系统

#### 积分规则
- **充值比例**: 1元 = 1积分
- **注册赠送**: 10积分
- **消费扣除**: 按资源定价扣除
- **失败退还**: 下载失败自动退还

#### 交易类型
- `recharge` - 充值
- `consume` - 消费
- `refund` - 退还
- `gift` - 赠送
- `admin` - 管理员调整

### 3. 支付宝集成

#### 当面付特性
- **扫码支付** - 生成支付二维码
- **异步通知** - 支付结果回调
- **订单查询** - 主动查询支付状态
- **签名验证** - RSA2安全验签

---

## 🗄️ 数据库设计

### 核心表结构

#### 1. 订单表 (orders)
```sql
CREATE TABLE `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_number` varchar(50) NOT NULL COMMENT '订单号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `points` int(11) NOT NULL COMMENT '获得积分',
  `status` enum('pending','completed','failed','cancelled','expired') NOT NULL DEFAULT 'pending',
  `payment_method` varchar(20) NOT NULL DEFAULT 'alipay',
  `trade_no` varchar(100) DEFAULT NULL COMMENT '支付宝交易号',
  `qr_code` text COMMENT '支付二维码',
  `notify_data` text COMMENT '支付回调数据',
  `ip_address` varchar(45) DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL COMMENT '订单过期时间',
  `paid_at` timestamp NULL DEFAULT NULL COMMENT '支付完成时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_number` (`order_number`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 2. 积分账户表 (points)
```sql
CREATE TABLE `points` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `balance` int(11) NOT NULL DEFAULT '0' COMMENT '积分余额',
  `total_recharged` int(11) NOT NULL DEFAULT '0' COMMENT '累计充值积分',
  `total_consumed` int(11) NOT NULL DEFAULT '0' COMMENT '累计消费积分',
  `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 3. 积分交易表 (point_transactions)
```sql
CREATE TABLE `point_transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `amount` int(11) NOT NULL COMMENT '积分数量(正数为增加,负数为减少)',
  `transaction_type` enum('recharge','consume','refund','gift','admin') NOT NULL,
  `resource_id` varchar(100) DEFAULT NULL,
  `order_id` varchar(100) DEFAULT NULL,
  `description` text,
  `balance_before` int(11) NOT NULL COMMENT '交易前余额',
  `balance_after` int(11) NOT NULL COMMENT '交易后余额',
  `ip_address` varchar(45) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_transaction_type` (`transaction_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 索引优化策略
- **订单查询**: order_number(唯一), user_id, status, created_at
- **积分查询**: user_id(唯一), transaction_type, created_at
- **用户查询**: username(唯一), email(唯一), token

---

## 🔌 API接口

### 支付相关接口

#### 1. 创建支付订单
```http
POST /api/payment/create.php
Authorization: Bearer {token}
Content-Type: application/json

{
    "amount": 10.00
}
```

**响应示例:**
```json
{
    "success": true,
    "code": 200,
    "message": "订单创建成功",
    "data": {
        "order_id": 123,
        "order_number": "PD20241219143025001",
        "amount": 10.00,
        "points": 10,
        "qr_code": "https://qr.alipay.com/bax08431...",
        "expires_at": "2024-12-19 15:00:25"
    }
}
```

#### 2. 查询订单状态
```http
GET /api/payment/query.php?order_number=PD20241219143025001
Authorization: Bearer {token}
```

**响应示例:**
```json
{
    "success": true,
    "data": {
        "order_number": "PD20241219143025001",
        "amount": 10.00,
        "points": 10,
        "status": "completed",
        "trade_no": "2024121922001234567890",
        "created_at": "2024-12-19 14:30:25",
        "expires_at": "2024-12-19 15:00:25"
    }
}
```

#### 3. 支付宝异步回调
```http
POST /api/payment/notify.php
Content-Type: application/x-www-form-urlencoded

out_trade_no=PD20241219143025001&trade_no=2024121922001234567890&trade_status=TRADE_SUCCESS&total_amount=10.00&sign=...
```

### 积分相关接口

#### 1. 获取积分余额
```http
GET /api/points/balance.php
Authorization: Bearer {token}
```

#### 2. 消费积分
```http
POST /api/points/consume.php
Authorization: Bearer {token}

{
    "points": 5,
    "resource_id": "resource_123",
    "description": "下载资源"
}
```

#### 3. 退还积分
```http
POST /api/points/refund.php
Authorization: Bearer {token}

{
    "points": 5,
    "resource_id": "resource_123",
    "description": "下载失败退还"
}
```

#### 4. 积分交易历史
```http
GET /api/points/history.php?page=1&limit=20&type=recharge
Authorization: Bearer {token}
```

---

## 🔄 支付流程

### 完整支付时序图
```mermaid
sequenceDiagram
    participant C as 客户端
    participant A as API服务
    participant D as 数据库
    participant P as 支付宝

    C->>A: 1. 创建支付订单
    A->>D: 2. 生成订单记录
    A->>P: 3. 调用当面付API
    P-->>A: 4. 返回支付二维码
    A-->>C: 5. 返回订单信息
    
    C->>C: 6. 显示二维码
    Note over C: 用户扫码支付
    
    P->>A: 7. 异步支付通知
    A->>A: 8. 验证签名
    A->>D: 9. 更新订单状态
    A->>D: 10. 充值积分
    A-->>P: 11. 返回success
    
    loop 轮询查询
        C->>A: 12. 查询订单状态
        A->>D: 13. 查询数据库
        A-->>C: 14. 返回状态
    end
```

### 详细流程说明

#### 阶段1: 订单创建
1. **客户端请求** - 发送充值金额到API
2. **参数验证** - 验证金额范围和用户权限
3. **订单生成** - 生成唯一订单号
4. **支付宝调用** - 调用当面付预下单接口
5. **数据保存** - 保存订单信息到数据库
6. **返回结果** - 返回二维码和订单信息

#### 阶段2: 支付处理
1. **用户扫码** - 用户使用支付宝扫描二维码
2. **支付确认** - 用户在支付宝中确认支付
3. **异步通知** - 支付宝发送支付结果到回调地址
4. **签名验证** - 验证支付宝回调签名
5. **订单更新** - 更新订单状态为已完成
6. **积分充值** - 为用户账户充值对应积分

#### 阶段3: 状态同步
1. **客户端轮询** - 定期查询订单状态
2. **状态检查** - 检查订单是否已完成或过期
3. **结果返回** - 返回最新的订单状态
4. **流程结束** - 支付完成或超时结束

---

## 💻 代码实现

### 核心类详解

#### 1. Payment类 - 支付管理核心
```php
class Payment {
    private $db;
    private $userId;
    private $aopClient; // 支付宝SDK客户端

    public function __construct($userId = null) {
        $this->db = getDB();
        $this->userId = $userId;
        $this->initAlipayClient(); // 初始化支付宝客户端
    }

    /**
     * 初始化支付宝客户端 - 严格按照官方SDK规范
     */
    private function initAlipayClient() {
        $this->aopClient = new AopClient();
        $this->aopClient->gatewayUrl = ALIPAY_GATEWAY;
        $this->aopClient->appId = ALIPAY_APP_ID;
        $this->aopClient->rsaPrivateKey = ALIPAY_PRIVATE_KEY;
        $this->aopClient->alipayrsaPublicKey = ALIPAY_PUBLIC_KEY;
        $this->aopClient->signType = ALIPAY_SIGN_TYPE;
        $this->aopClient->postCharset = ALIPAY_CHARSET;
        $this->aopClient->format = ALIPAY_FORMAT;
        $this->aopClient->apiVersion = '1.0';
    }

    /**
     * 创建支付订单
     */
    public function createOrder($amount, $points = null) {
        if ($amount <= 0) {
            throw new Exception('支付金额必须大于0');
        }

        if (!$this->userId) {
            throw new Exception('用户未登录');
        }

        // 计算积分数量
        if ($points === null) {
            $pointsPerYuan = $this->getSystemConfig('points_per_yuan', DEFAULT_POINTS_PER_YUAN);
            $points = $amount * $pointsPerYuan;
        }

        try {
            $this->db->beginTransaction();

            // 生成订单号
            $orderNumber = $this->generateOrderNumber();

            // 创建支付宝订单
            $alipayResult = $this->createAlipayOrder($orderNumber, $amount, $points);

            // 保存订单到数据库
            $orderData = [
                'order_number' => $orderNumber,
                'user_id' => $this->userId,
                'amount' => $amount,
                'points' => $points,
                'status' => 'pending',
                'payment_method' => 'alipay',
                'qr_code' => $alipayResult['qr_code'],
                'ip_address' => getClientIP(),
                'expires_at' => date('Y-m-d H:i:s', time() + DEFAULT_ORDER_EXPIRE_MINUTES * 60)
            ];

            $orderId = $this->db->insertData('orders', $orderData);
            $this->db->commit();

            return [
                'success' => true,
                'order_id' => $orderId,
                'order_number' => $orderNumber,
                'amount' => $amount,
                'points' => $points,
                'qr_code' => $alipayResult['qr_code'],
                'expires_at' => $orderData['expires_at'],
                'message' => '订单创建成功'
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    /**
     * 创建支付宝当面付订单 - 官方SDK标准实现
     */
    private function createAlipayOrder($orderNumber, $amount, $points) {
        $subject = "积分充值";
        $body = "充值 {$amount} 元获得 {$points} 积分";

        // 创建请求对象
        $request = new AlipayTradePrecreateRequest();

        // 设置异步通知地址
        $request->setNotifyUrl(SITE_URL . 'api/payment/notify.php');

        // 设置业务参数
        $bizContent = [
            'out_trade_no' => $orderNumber,                    // 商户订单号
            'total_amount' => number_format($amount, 2, '.', ''), // 订单总金额
            'subject' => $subject,                             // 订单标题
            'body' => $body,                                   // 订单描述
            'timeout_express' => '30m',                        // 订单超时时间
            'store_id' => 'POINTS_STORE_001'                   // 商户门店编号
        ];

        $request->setBizContent(json_encode($bizContent, JSON_UNESCAPED_UNICODE));

        // 执行请求
        $response = $this->aopClient->execute($request);

        // 解析响应
        $responseNode = str_replace(".", "_", $request->getApiMethodName()) . "_response";
        $result = $response->$responseNode;
        $resultCode = $result->code;

        if ($resultCode == '10000') {
            // 成功创建订单
            return [
                'success' => true,
                'qr_code' => $result->qr_code,
                'order_number' => $orderNumber
            ];
        } else {
            // 创建订单失败
            $errorMsg = $result->msg ?? '未知错误';
            $subErrorMsg = $result->sub_msg ?? '';
            $fullErrorMsg = $errorMsg . ($subErrorMsg ? ' (' . $subErrorMsg . ')' : '');
            throw new Exception("支付宝订单创建失败: $fullErrorMsg (错误码: $resultCode)");
        }
    }

    /**
     * 处理支付宝回调通知
     */
    public function handleAlipayNotify($notifyData) {
        try {
            // 验证支付宝签名
            if (!$this->verifyAlipaySign($notifyData)) {
                throw new Exception('签名验证失败');
            }

            $orderNumber = $notifyData['out_trade_no'];
            $tradeNo = $notifyData['trade_no'];
            $tradeStatus = $notifyData['trade_status'];

            // 查找订单
            $order = $this->db->selectOne('orders', ['order_number' => $orderNumber]);

            if (!$order) {
                throw new Exception('订单不存在');
            }

            // 验证金额
            if (abs($order['amount'] - floatval($notifyData['total_amount'])) > 0.01) {
                throw new Exception('订单金额不匹配');
            }

            // 处理支付成功
            if ($tradeStatus === 'TRADE_SUCCESS' || $tradeStatus === 'TRADE_FINISHED') {
                if ($order['status'] === 'pending') {
                    $this->processOrderPayment($orderNumber, $tradeNo, json_encode($notifyData));
                }
            }

            return ['success' => true];

        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * 验证支付宝签名 - 官方SDK标准实现
     */
    private function verifyAlipaySign($data) {
        // 检查必需的签名参数
        if (!isset($data['sign']) || !isset($data['sign_type'])) {
            return false;
        }

        $sign = $data['sign'];
        $signType = $data['sign_type'];

        // 验证签名类型
        if ($signType !== 'RSA2') {
            return false;
        }

        // 移除签名相关参数
        $dataToVerify = $data;
        unset($dataToVerify['sign'], $dataToVerify['sign_type']);

        // 按照支付宝规范构建待验证字符串
        ksort($dataToVerify);
        $stringToBeVerified = '';
        foreach ($dataToVerify as $key => $value) {
            if ($value !== '' && $value !== null) {
                $stringToBeVerified .= $key . '=' . $value . '&';
            }
        }
        $stringToBeVerified = rtrim($stringToBeVerified, '&');

        // 构建支付宝公钥
        $publicKey = "-----BEGIN PUBLIC KEY-----\n" .
                    chunk_split(ALIPAY_PUBLIC_KEY, 64, "\n") .
                    "-----END PUBLIC KEY-----";

        // 执行RSA2验签
        $result = openssl_verify($stringToBeVerified, base64_decode($sign), $publicKey, OPENSSL_ALGO_SHA256);

        return $result === 1;
    }

    /**
     * 生成订单号
     */
    private function generateOrderNumber() {
        return 'PD' . date('YmdHis') . rand(1000, 9999);
    }
}
```

#### 2. Points类 - 积分管理核心
```php
class Points {
    private $db;
    private $userId;

    public function __construct($userId) {
        $this->db = getDB();
        $this->userId = $userId;
        $this->ensurePointsAccount(); // 确保用户有积分账户
    }

    /**
     * 充值积分
     */
    public function rechargePoints($amount, $orderId, $description = '积分充值') {
        if ($amount <= 0) {
            throw new Exception('充值积分必须大于0');
        }

        try {
            // 检查是否已经在事务中
            $needTransaction = !$this->db->inTransaction();

            if ($needTransaction) {
                $this->db->beginTransaction();
            }

            // 获取当前余额
            $currentBalance = $this->getBalance();
            $newBalance = $currentBalance + $amount;

            // 更新积分余额
            $this->db->updateData('points', [
                'balance' => $newBalance,
                'total_recharged' => $this->db->fetchOne(
                    'SELECT total_recharged FROM points WHERE user_id = ?',
                    [$this->userId]
                )['total_recharged'] + $amount
            ], ['user_id' => $this->userId]);

            // 记录交易
            $this->recordTransaction($amount, 'recharge', null, $orderId, $description, $currentBalance, $newBalance);

            if ($needTransaction) {
                $this->db->commit();
            }

            return [
                'success' => true,
                'balance' => $newBalance,
                'recharged' => $amount,
                'message' => '积分充值成功'
            ];

        } catch (Exception $e) {
            if ($needTransaction && $this->db->inTransaction()) {
                $this->db->rollback();
            }
            throw $e;
        }
    }

    /**
     * 消费积分
     */
    public function consumePoints($amount, $resourceId = null, $description = '下载资源') {
        if ($amount <= 0) {
            throw new Exception('消费积分必须大于0');
        }

        try {
            $this->db->beginTransaction();

            // 获取当前余额
            $currentBalance = $this->getBalance();

            if ($currentBalance < $amount) {
                throw new Exception('积分余额不足');
            }

            $newBalance = $currentBalance - $amount;

            // 更新积分余额
            $this->db->updateData('points', [
                'balance' => $newBalance,
                'total_consumed' => $this->db->fetchOne(
                    'SELECT total_consumed FROM points WHERE user_id = ?',
                    [$this->userId]
                )['total_consumed'] + $amount
            ], ['user_id' => $this->userId]);

            // 记录交易
            $this->recordTransaction(-$amount, 'consume', $resourceId, null, $description, $currentBalance, $newBalance);

            $this->db->commit();

            return [
                'success' => true,
                'balance' => $newBalance,
                'consumed' => $amount,
                'message' => '积分消费成功'
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    /**
     * 记录积分交易
     */
    private function recordTransaction($amount, $type, $resourceId = null, $orderId = null, $description = '', $balanceBefore = null, $balanceAfter = null) {
        $transactionData = [
            'user_id' => $this->userId,
            'amount' => $amount,
            'transaction_type' => $type,
            'resource_id' => $resourceId,
            'order_id' => $orderId,
            'description' => $description,
            'balance_before' => $balanceBefore,
            'balance_after' => $balanceAfter,
            'ip_address' => getClientIP()
        ];

        return $this->db->insertData('point_transactions', $transactionData);
    }
}
```

#### 3. Database类 - 数据库管理核心
```php
class Database {
    private static $instance = null;
    private $connection;

    /**
     * 获取数据库实例（单例模式）
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 简化的插入方法
     */
    public function insertData($table, $data) {
        list($sql, $params) = $this->buildInsert($table, $data);
        return $this->insert($sql, $params);
    }

    /**
     * 简化的更新方法
     */
    public function updateData($table, $data, $conditions) {
        list($sql, $params) = $this->buildUpdate($table, $data, $conditions);
        return $this->execute($sql, $params);
    }

    /**
     * 事务管理
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }

    public function commit() {
        return $this->connection->commit();
    }

    public function rollback() {
        return $this->connection->rollback();
    }

    public function inTransaction() {
        return $this->connection->inTransaction();
    }
}
```

---

## ⚙️ 配置说明

### 1. 支付宝配置
```php
// 支付宝配置 - 官方SDK标准配置
define('ALIPAY_APP_ID', '2021005150683259');                    // 应用ID
define('ALIPAY_GATEWAY', 'https://openapi.alipay.com/gateway.do'); // 网关地址
define('ALIPAY_CHARSET', 'utf-8');                              // 字符集
define('ALIPAY_SIGN_TYPE', 'RSA2');                             // 签名类型
define('ALIPAY_FORMAT', 'json');                                // 数据格式

// 支付宝公钥 (用于验证支付宝返回的数据)
define('ALIPAY_PUBLIC_KEY', 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAj+1qBI3J9UImYn...');

// 应用私钥 (RSA格式，用于签名请求数据)
define('ALIPAY_PRIVATE_KEY', 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDTSuol4ahkH...');
```

### 2. 数据库配置
```php
// 数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'your_database');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
define('DB_CHARSET', 'utf8mb4');
```

### 3. 系统配置
```php
// 积分系统配置
define('DEFAULT_POINTS_PER_YUAN', 1);           // 每元对应积分数
define('DEFAULT_REGISTER_GIFT_POINTS', 10);     // 注册赠送积分
define('DEFAULT_ORDER_EXPIRE_MINUTES', 30);     // 订单过期时间(分钟)

// 安全配置
define('JWT_SECRET', 'your_jwt_secret_key');    // JWT密钥
define('TOKEN_EXPIRE_HOURS', 24);               // Token过期时间
```

### 4. 客户端配置 (config.json)
```json
{
    "server": {
        "base_url": "https://your-domain.com/",
        "api_base": "https://your-domain.com/api/"
    },
    "download": {
        "default_path": "downloads/",
        "max_concurrent": 3
    },
    "user": {
        "auto_login": true,
        "token_file": "keys/user_token.txt"
    },
    "security": {
        "encrypt_token": true
    }
}
```

---

## 🚀 部署指南

### 1. 环境要求
- **PHP**: 7.4+ (推荐8.0+)
- **MySQL**: 5.7+ 或 MariaDB 10.3+
- **Web服务器**: Apache 2.4+ 或 Nginx 1.18+
- **PHP扩展**: PDO, PDO_MySQL, OpenSSL, cURL, JSON

### 2. 安装步骤

#### 步骤1: 下载支付宝SDK
```bash
# 下载支付宝官方SDK
wget https://docs.open.alipay.com/54/103419
unzip alipay-sdk-php.zip
mv alipay-sdk-php server/vendor/alipay/
```

#### 步骤2: 配置数据库
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE your_database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入数据库结构
mysql -u your_user -p your_database < server/database.sql
```

#### 步骤3: 配置文件
```bash
# 复制配置文件
cp server/includes/config.php.example server/includes/config.php

# 编辑配置文件
vim server/includes/config.php
```

#### 步骤4: 设置目录权限
```bash
# 设置日志目录权限
chmod 755 server/logs/
chmod 666 server/logs/*.log

# 设置上传目录权限
chmod 755 server/uploads/
```

---

## 🔧 集成指南

### 1. 快速集成步骤

#### 步骤1: 复制核心文件
```
your_project/
├── includes/
│   ├── config.php          # 配置文件
│   ├── database.php        # 数据库类
│   ├── Payment.php         # 支付类
│   └── Points.php          # 积分类
├── api/
│   ├── payment/
│   │   ├── create.php      # 创建订单
│   │   ├── query.php       # 查询订单
│   │   └── notify.php      # 支付回调
│   └── points/
│       ├── balance.php     # 积分余额
│       ├── consume.php     # 消费积分
│       └── history.php     # 交易历史
└── vendor/
    └── alipay/             # 支付宝SDK
```

#### 步骤2: 修改配置
```php
// 修改 includes/config.php
define('DB_HOST', 'your_host');
define('DB_NAME', 'your_database');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');

define('ALIPAY_APP_ID', 'your_app_id');
define('ALIPAY_PRIVATE_KEY', 'your_private_key');
define('ALIPAY_PUBLIC_KEY', 'alipay_public_key');

define('SITE_URL', 'https://your-domain.com/');
```

#### 步骤3: 导入数据库
```sql
-- 导入必要的表结构
SOURCE database.sql;

-- 或者只导入支付相关表
CREATE TABLE orders (...);
CREATE TABLE points (...);
CREATE TABLE point_transactions (...);
```

#### 步骤4: 测试集成
```php
// 测试支付功能
$payment = new Payment($userId);
$result = $payment->createOrder(10.00);

// 测试积分功能
$points = new Points($userId);
$balance = $points->getBalance();
```

### 2. 自定义集成

#### 集成到现有用户系统
```php
// 如果你已有用户系统，只需要确保用户ID一致
class YourUserSystem {
    public function getCurrentUserId() {
        // 返回当前登录用户的ID
        return $_SESSION['user_id'];
    }

    public function integratePayment() {
        $userId = $this->getCurrentUserId();
        $payment = new Payment($userId);
        $points = new Points($userId);

        // 现在可以使用支付和积分功能
        return ['payment' => $payment, 'points' => $points];
    }
}
```

#### 自定义积分规则
```php
// 修改积分计算规则
class CustomPoints extends Points {
    public function calculatePoints($amount) {
        // 自定义积分计算逻辑
        if ($amount >= 100) {
            return $amount * 1.2; // 大额充值有奖励
        }
        return $amount;
    }
}
```

---

## 🔒 安全机制

### 1. 签名验证
- **RSA2算法**: 使用2048位RSA密钥
- **签名验证**: 严格验证支付宝回调签名
- **防篡改**: 确保数据传输完整性

### 2. 数据安全
- **SQL注入防护**: 使用PDO预处理语句
- **XSS防护**: 输出数据HTML转义
- **CSRF防护**: Token验证机制

### 3. 业务安全
- **金额验证**: 严格验证订单金额匹配
- **重复处理**: 防止重复处理同一订单
- **权限控制**: 用户只能操作自己的订单
- **IP记录**: 记录所有操作的IP地址

### 4. 系统安全
- **HTTPS强制**: 生产环境必须使用HTTPS
- **文件权限**: 敏感文件设置适当权限
- **错误隐藏**: 生产环境隐藏详细错误信息

---

## ❌ 错误处理

### 1. 错误分类

#### 业务错误
- `INSUFFICIENT_BALANCE`: 积分余额不足
- `ORDER_NOT_FOUND`: 订单不存在
- `ORDER_EXPIRED`: 订单已过期
- `INVALID_AMOUNT`: 无效金额

#### 系统错误
- `DATABASE_ERROR`: 数据库连接错误
- `ALIPAY_API_ERROR`: 支付宝API错误
- `NETWORK_ERROR`: 网络连接错误
- `CONFIG_ERROR`: 配置错误

#### 安全错误
- `INVALID_TOKEN`: 无效令牌
- `SIGNATURE_ERROR`: 签名验证失败
- `PERMISSION_DENIED`: 权限不足
- `RATE_LIMIT_EXCEEDED`: 请求频率超限

### 2. 错误响应格式
```json
{
    "success": false,
    "code": 400,
    "message": "积分余额不足",
    "data": null,
    "timestamp": 1703001234,
    "error_code": "INSUFFICIENT_BALANCE",
    "trace_id": "abc123def456"
}
```

---

## 📝 日志系统

### 1. 日志级别
- **DEBUG**: 调试信息
- **INFO**: 一般信息
- **WARNING**: 警告信息
- **ERROR**: 错误信息

### 2. 日志格式
```
[2024-12-19 14:30:25] [INFO] [*************] 支付订单创建成功: 订单号 PD20241219143025001, 金额 10.00 元
[2024-12-19 14:30:26] [ERROR] [*************] 支付宝API调用失败: 网络超时
```

### 3. 关键日志记录点
```php
// 订单创建
writeLog("支付订单创建: 用户ID $userId, 金额 $amount 元", 'INFO');

// 支付成功
writeLog("支付成功: 订单号 $orderNumber, 支付宝交易号 $tradeNo", 'INFO');

// 积分变动
writeLog("积分充值: 用户ID $userId, 充值 $amount 积分", 'INFO');

// 错误记录
writeLog("支付宝回调验签失败: " . json_encode($notifyData), 'ERROR');
```

---

## ❓ 常见问题

### 1. 支付相关问题

#### Q: 支付宝回调验签失败？
**A**: 检查以下几点：
1. 确认支付宝公钥配置正确
2. 检查回调URL是否可访问
3. 验证签名算法是否为RSA2
4. 确保字符编码为UTF-8

#### Q: 订单创建失败？
**A**: 常见原因：
1. 支付宝应用配置错误
2. 网络连接问题
3. 金额格式不正确
4. 应用私钥配置错误

### 2. 积分相关问题

#### Q: 积分余额不准确？
**A**: 可能原因：
1. 并发操作导致的数据不一致
2. 事务未正确提交
3. 数据库连接中断

### 3. 性能相关问题

#### Q: 查询速度慢？
**A**: 优化建议：
1. 添加适当的数据库索引
2. 使用分页查询
3. 缓存常用数据

---

## 📚 附录

### 1. 支付宝官方文档链接
- [当面付API文档](https://opendocs.alipay.com/apis/api_1/alipay.trade.precreate)
- [异步通知说明](https://opendocs.alipay.com/open/270/105902)
- [签名验证说明](https://opendocs.alipay.com/open/291/105974)

### 2. 相关技术文档
- [PHP PDO文档](https://www.php.net/manual/zh/book.pdo.php)
- [MySQL索引优化](https://dev.mysql.com/doc/refman/8.0/en/optimization-indexes.html)
- [RESTful API设计规范](https://restfulapi.net/)

---

**文档版本**: v1.0.0
**最后更新**: 2024-12-19
**维护者**: 开发团队
**联系方式**: <EMAIL>

> 💡 **提示**: 本文档会持续更新，建议定期查看最新版本。如有问题或建议，欢迎提交Issue或Pull Request。
