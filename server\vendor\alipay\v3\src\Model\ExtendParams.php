<?php
/**
 * ExtendParams
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * ExtendParams Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class ExtendParams implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'ExtendParams';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'cardType' => 'string',
        'creditExtInfo' => 'string',
        'hbFqNum' => 'string',
        'hbFqSellerPercent' => 'string',
        'industryRefluxInfo' => 'string',
        'royaltyFreeze' => 'string',
        'specifiedSellerName' => 'string',
        'sysServiceProviderId' => 'string',
        'tcInstallmentOrderId' => 'string',
        'tradeComponentOrderId' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'cardType' => null,
        'creditExtInfo' => null,
        'hbFqNum' => null,
        'hbFqSellerPercent' => null,
        'industryRefluxInfo' => null,
        'royaltyFreeze' => null,
        'specifiedSellerName' => null,
        'sysServiceProviderId' => null,
        'tcInstallmentOrderId' => null,
        'tradeComponentOrderId' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'cardType' => 'card_type',
        'creditExtInfo' => 'credit_ext_info',
        'hbFqNum' => 'hb_fq_num',
        'hbFqSellerPercent' => 'hb_fq_seller_percent',
        'industryRefluxInfo' => 'industry_reflux_info',
        'royaltyFreeze' => 'royalty_freeze',
        'specifiedSellerName' => 'specified_seller_name',
        'sysServiceProviderId' => 'sys_service_provider_id',
        'tcInstallmentOrderId' => 'tc_installment_order_id',
        'tradeComponentOrderId' => 'trade_component_order_id'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'cardType' => 'setCardType',
        'creditExtInfo' => 'setCreditExtInfo',
        'hbFqNum' => 'setHbFqNum',
        'hbFqSellerPercent' => 'setHbFqSellerPercent',
        'industryRefluxInfo' => 'setIndustryRefluxInfo',
        'royaltyFreeze' => 'setRoyaltyFreeze',
        'specifiedSellerName' => 'setSpecifiedSellerName',
        'sysServiceProviderId' => 'setSysServiceProviderId',
        'tcInstallmentOrderId' => 'setTcInstallmentOrderId',
        'tradeComponentOrderId' => 'setTradeComponentOrderId'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'cardType' => 'getCardType',
        'creditExtInfo' => 'getCreditExtInfo',
        'hbFqNum' => 'getHbFqNum',
        'hbFqSellerPercent' => 'getHbFqSellerPercent',
        'industryRefluxInfo' => 'getIndustryRefluxInfo',
        'royaltyFreeze' => 'getRoyaltyFreeze',
        'specifiedSellerName' => 'getSpecifiedSellerName',
        'sysServiceProviderId' => 'getSysServiceProviderId',
        'tcInstallmentOrderId' => 'getTcInstallmentOrderId',
        'tradeComponentOrderId' => 'getTradeComponentOrderId'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['cardType'] = $data['cardType'] ?? null;
        $this->container['creditExtInfo'] = $data['creditExtInfo'] ?? null;
        $this->container['hbFqNum'] = $data['hbFqNum'] ?? null;
        $this->container['hbFqSellerPercent'] = $data['hbFqSellerPercent'] ?? null;
        $this->container['industryRefluxInfo'] = $data['industryRefluxInfo'] ?? null;
        $this->container['royaltyFreeze'] = $data['royaltyFreeze'] ?? null;
        $this->container['specifiedSellerName'] = $data['specifiedSellerName'] ?? null;
        $this->container['sysServiceProviderId'] = $data['sysServiceProviderId'] ?? null;
        $this->container['tcInstallmentOrderId'] = $data['tcInstallmentOrderId'] ?? null;
        $this->container['tradeComponentOrderId'] = $data['tradeComponentOrderId'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets cardType
     *
     * @return string|null
     */
    public function getCardType()
    {
        return $this->container['cardType'];
    }

    /**
     * Sets cardType
     *
     * @param string|null $cardType 卡类型
     *
     * @return self
     */
    public function setCardType($cardType)
    {
        $this->container['cardType'] = $cardType;

        return $this;
    }

    /**
     * Gets creditExtInfo
     *
     * @return string|null
     */
    public function getCreditExtInfo()
    {
        return $this->container['creditExtInfo'];
    }

    /**
     * Sets creditExtInfo
     *
     * @param string|null $creditExtInfo 信用参数，可选，如有需要请与芝麻约定后传入，信用服务说明见
     *
     * @return self
     */
    public function setCreditExtInfo($creditExtInfo)
    {
        $this->container['creditExtInfo'] = $creditExtInfo;

        return $this;
    }

    /**
     * Gets hbFqNum
     *
     * @return string|null
     */
    public function getHbFqNum()
    {
        return $this->container['hbFqNum'];
    }

    /**
     * Sets hbFqNum
     *
     * @param string|null $hbFqNum 使用花呗分期要进行的分期数
     *
     * @return self
     */
    public function setHbFqNum($hbFqNum)
    {
        $this->container['hbFqNum'] = $hbFqNum;

        return $this;
    }

    /**
     * Gets hbFqSellerPercent
     *
     * @return string|null
     */
    public function getHbFqSellerPercent()
    {
        return $this->container['hbFqSellerPercent'];
    }

    /**
     * Sets hbFqSellerPercent
     *
     * @param string|null $hbFqSellerPercent 使用花呗分期需要卖家承担的手续费比例的百分值，传入100代表100%
     *
     * @return self
     */
    public function setHbFqSellerPercent($hbFqSellerPercent)
    {
        $this->container['hbFqSellerPercent'] = $hbFqSellerPercent;

        return $this;
    }

    /**
     * Gets industryRefluxInfo
     *
     * @return string|null
     */
    public function getIndustryRefluxInfo()
    {
        return $this->container['industryRefluxInfo'];
    }

    /**
     * Sets industryRefluxInfo
     *
     * @param string|null $industryRefluxInfo 行业数据回流信息, 详见：地铁支付接口参数补充说明
     *
     * @return self
     */
    public function setIndustryRefluxInfo($industryRefluxInfo)
    {
        $this->container['industryRefluxInfo'] = $industryRefluxInfo;

        return $this;
    }

    /**
     * Gets royaltyFreeze
     *
     * @return string|null
     */
    public function getRoyaltyFreeze()
    {
        return $this->container['royaltyFreeze'];
    }

    /**
     * Sets royaltyFreeze
     *
     * @param string|null $royaltyFreeze 是否进行资金冻结，用于后续分账，true表示资金冻结，false或不传表示资金不冻结
     *
     * @return self
     */
    public function setRoyaltyFreeze($royaltyFreeze)
    {
        $this->container['royaltyFreeze'] = $royaltyFreeze;

        return $this;
    }

    /**
     * Gets specifiedSellerName
     *
     * @return string|null
     */
    public function getSpecifiedSellerName()
    {
        return $this->container['specifiedSellerName'];
    }

    /**
     * Sets specifiedSellerName
     *
     * @param string|null $specifiedSellerName 特殊场景下，允许商户指定交易展示的卖家名称
     *
     * @return self
     */
    public function setSpecifiedSellerName($specifiedSellerName)
    {
        $this->container['specifiedSellerName'] = $specifiedSellerName;

        return $this;
    }

    /**
     * Gets sysServiceProviderId
     *
     * @return string|null
     */
    public function getSysServiceProviderId()
    {
        return $this->container['sysServiceProviderId'];
    }

    /**
     * Sets sysServiceProviderId
     *
     * @param string|null $sysServiceProviderId 系统商编号  该参数作为系统商返佣数据提取的依据，请填写系统商签约协议的PID
     *
     * @return self
     */
    public function setSysServiceProviderId($sysServiceProviderId)
    {
        $this->container['sysServiceProviderId'] = $sysServiceProviderId;

        return $this;
    }

    /**
     * Gets tcInstallmentOrderId
     *
     * @return string|null
     */
    public function getTcInstallmentOrderId()
    {
        return $this->container['tcInstallmentOrderId'];
    }

    /**
     * Sets tcInstallmentOrderId
     *
     * @param string|null $tcInstallmentOrderId 公域商品交易分期单ID，小程序交易组件订单特殊场景使用，请传入 订单分期接口(alipay.open.mini.order.installment.create)中返回的installment_order_id
     *
     * @return self
     */
    public function setTcInstallmentOrderId($tcInstallmentOrderId)
    {
        $this->container['tcInstallmentOrderId'] = $tcInstallmentOrderId;

        return $this;
    }

    /**
     * Gets tradeComponentOrderId
     *
     * @return string|null
     */
    public function getTradeComponentOrderId()
    {
        return $this->container['tradeComponentOrderId'];
    }

    /**
     * Sets tradeComponentOrderId
     *
     * @param string|null $tradeComponentOrderId 公域商品交易业务订单ID
     *
     * @return self
     */
    public function setTradeComponentOrderId($tradeComponentOrderId)
    {
        $this->container['tradeComponentOrderId'] = $tradeComponentOrderId;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


