<?php
/**
 * 获取积分交易历史API
 * 版本: 1.0.0
 * 创建时间: 2024-12-19
 */

require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/User.php';
require_once '../../includes/Points.php';

// 验证请求方法
validateRequestMethod(['GET']);

try {
    // 验证用户Token
    $token = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    if (empty($token)) {
        handleError('缺少授权令牌', API_AUTH_ERROR_CODE);
    }
    
    $user = new User();
    $userId = $user->validateToken($token);
    
    if (!$userId) {
        handleError('无效的授权令牌', API_AUTH_ERROR_CODE);
    }
    
    // 获取查询参数
    $page = max(1, (int)($_GET['page'] ?? 1));
    $limit = min(100, max(1, (int)($_GET['limit'] ?? 20)));
    $type = $_GET['type'] ?? null;
    
    // 验证交易类型
    if ($type && !in_array($type, ['recharge', 'consume', 'refund', 'gift', 'admin'])) {
        handleError('无效的交易类型', API_ERROR_CODE);
    }
    
    // 获取积分交易历史
    $pointsManager = new Points($userId);
    $history = $pointsManager->getTransactionHistory($page, $limit, $type);
    
    // 记录查询日志
    writeLog("积分历史查询: 用户ID $userId, 页码 $page, 类型 " . ($type ?: '全部'), 'INFO');
    
    // 返回成功响应
    jsonResponse($history, API_SUCCESS_CODE, '获取积分历史成功');
    
} catch (Exception $e) {
    // 记录错误日志
    writeLog("获取积分历史失败: " . $e->getMessage(), 'ERROR');
    
    // 返回错误响应
    handleError($e->getMessage(), API_AUTH_ERROR_CODE);
}

?>
