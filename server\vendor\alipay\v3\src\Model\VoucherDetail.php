<?php
/**
 * VoucherDetail
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * VoucherDetail Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class VoucherDetail implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'VoucherDetail';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'amount' => 'string',
        'id' => 'string',
        'memo' => 'string',
        'merchantContribute' => 'string',
        'name' => 'string',
        'otherContribute' => 'string',
        'otherContributeDetail' => '\Alipay\OpenAPISDK\Model\ContributeDetail[]',
        'purchaseAntContribute' => 'string',
        'purchaseBuyerContribute' => 'string',
        'purchaseMerchantContribute' => 'string',
        'templateId' => 'string',
        'type' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'amount' => null,
        'id' => null,
        'memo' => null,
        'merchantContribute' => null,
        'name' => null,
        'otherContribute' => null,
        'otherContributeDetail' => null,
        'purchaseAntContribute' => null,
        'purchaseBuyerContribute' => null,
        'purchaseMerchantContribute' => null,
        'templateId' => null,
        'type' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'amount' => 'amount',
        'id' => 'id',
        'memo' => 'memo',
        'merchantContribute' => 'merchant_contribute',
        'name' => 'name',
        'otherContribute' => 'other_contribute',
        'otherContributeDetail' => 'other_contribute_detail',
        'purchaseAntContribute' => 'purchase_ant_contribute',
        'purchaseBuyerContribute' => 'purchase_buyer_contribute',
        'purchaseMerchantContribute' => 'purchase_merchant_contribute',
        'templateId' => 'template_id',
        'type' => 'type'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'amount' => 'setAmount',
        'id' => 'setId',
        'memo' => 'setMemo',
        'merchantContribute' => 'setMerchantContribute',
        'name' => 'setName',
        'otherContribute' => 'setOtherContribute',
        'otherContributeDetail' => 'setOtherContributeDetail',
        'purchaseAntContribute' => 'setPurchaseAntContribute',
        'purchaseBuyerContribute' => 'setPurchaseBuyerContribute',
        'purchaseMerchantContribute' => 'setPurchaseMerchantContribute',
        'templateId' => 'setTemplateId',
        'type' => 'setType'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'amount' => 'getAmount',
        'id' => 'getId',
        'memo' => 'getMemo',
        'merchantContribute' => 'getMerchantContribute',
        'name' => 'getName',
        'otherContribute' => 'getOtherContribute',
        'otherContributeDetail' => 'getOtherContributeDetail',
        'purchaseAntContribute' => 'getPurchaseAntContribute',
        'purchaseBuyerContribute' => 'getPurchaseBuyerContribute',
        'purchaseMerchantContribute' => 'getPurchaseMerchantContribute',
        'templateId' => 'getTemplateId',
        'type' => 'getType'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['amount'] = $data['amount'] ?? null;
        $this->container['id'] = $data['id'] ?? null;
        $this->container['memo'] = $data['memo'] ?? null;
        $this->container['merchantContribute'] = $data['merchantContribute'] ?? null;
        $this->container['name'] = $data['name'] ?? null;
        $this->container['otherContribute'] = $data['otherContribute'] ?? null;
        $this->container['otherContributeDetail'] = $data['otherContributeDetail'] ?? null;
        $this->container['purchaseAntContribute'] = $data['purchaseAntContribute'] ?? null;
        $this->container['purchaseBuyerContribute'] = $data['purchaseBuyerContribute'] ?? null;
        $this->container['purchaseMerchantContribute'] = $data['purchaseMerchantContribute'] ?? null;
        $this->container['templateId'] = $data['templateId'] ?? null;
        $this->container['type'] = $data['type'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets amount
     *
     * @return string|null
     */
    public function getAmount()
    {
        return $this->container['amount'];
    }

    /**
     * Sets amount
     *
     * @param string|null $amount 优惠券面额，它应该会等于商家出资加上其他出资方出资
     *
     * @return self
     */
    public function setAmount($amount)
    {
        $this->container['amount'] = $amount;

        return $this;
    }

    /**
     * Gets id
     *
     * @return string|null
     */
    public function getId()
    {
        return $this->container['id'];
    }

    /**
     * Sets id
     *
     * @param string|null $id 券id
     *
     * @return self
     */
    public function setId($id)
    {
        $this->container['id'] = $id;

        return $this;
    }

    /**
     * Gets memo
     *
     * @return string|null
     */
    public function getMemo()
    {
        return $this->container['memo'];
    }

    /**
     * Sets memo
     *
     * @param string|null $memo 优惠券备注信息
     *
     * @return self
     */
    public function setMemo($memo)
    {
        $this->container['memo'] = $memo;

        return $this;
    }

    /**
     * Gets merchantContribute
     *
     * @return string|null
     */
    public function getMerchantContribute()
    {
        return $this->container['merchantContribute'];
    }

    /**
     * Sets merchantContribute
     *
     * @param string|null $merchantContribute 商家出资（特指发起交易的商家出资金额）
     *
     * @return self
     */
    public function setMerchantContribute($merchantContribute)
    {
        $this->container['merchantContribute'] = $merchantContribute;

        return $this;
    }

    /**
     * Gets name
     *
     * @return string|null
     */
    public function getName()
    {
        return $this->container['name'];
    }

    /**
     * Sets name
     *
     * @param string|null $name 券名称
     *
     * @return self
     */
    public function setName($name)
    {
        $this->container['name'] = $name;

        return $this;
    }

    /**
     * Gets otherContribute
     *
     * @return string|null
     */
    public function getOtherContribute()
    {
        return $this->container['otherContribute'];
    }

    /**
     * Sets otherContribute
     *
     * @param string|null $otherContribute 其他出资方出资金额，可能是支付宝，可能是品牌商，或者其他方，也可能是他们的一起出资
     *
     * @return self
     */
    public function setOtherContribute($otherContribute)
    {
        $this->container['otherContribute'] = $otherContribute;

        return $this;
    }

    /**
     * Gets otherContributeDetail
     *
     * @return \Alipay\OpenAPISDK\Model\ContributeDetail[]|null
     */
    public function getOtherContributeDetail()
    {
        return $this->container['otherContributeDetail'];
    }

    /**
     * Sets otherContributeDetail
     *
     * @param \Alipay\OpenAPISDK\Model\ContributeDetail[]|null $otherContributeDetail 优惠券的其他出资方明细
     *
     * @return self
     */
    public function setOtherContributeDetail($otherContributeDetail)
    {
        $this->container['otherContributeDetail'] = $otherContributeDetail;

        return $this;
    }

    /**
     * Gets purchaseAntContribute
     *
     * @return string|null
     */
    public function getPurchaseAntContribute()
    {
        return $this->container['purchaseAntContribute'];
    }

    /**
     * Sets purchaseAntContribute
     *
     * @param string|null $purchaseAntContribute 如果使用的这张券是用户购买的，则该字段代表用户在购买这张券时平台优惠的金额
     *
     * @return self
     */
    public function setPurchaseAntContribute($purchaseAntContribute)
    {
        $this->container['purchaseAntContribute'] = $purchaseAntContribute;

        return $this;
    }

    /**
     * Gets purchaseBuyerContribute
     *
     * @return string|null
     */
    public function getPurchaseBuyerContribute()
    {
        return $this->container['purchaseBuyerContribute'];
    }

    /**
     * Sets purchaseBuyerContribute
     *
     * @param string|null $purchaseBuyerContribute 如果使用的这张券是用户购买的，则该字段代表用户在购买这张券时用户实际付款的金额
     *
     * @return self
     */
    public function setPurchaseBuyerContribute($purchaseBuyerContribute)
    {
        $this->container['purchaseBuyerContribute'] = $purchaseBuyerContribute;

        return $this;
    }

    /**
     * Gets purchaseMerchantContribute
     *
     * @return string|null
     */
    public function getPurchaseMerchantContribute()
    {
        return $this->container['purchaseMerchantContribute'];
    }

    /**
     * Sets purchaseMerchantContribute
     *
     * @param string|null $purchaseMerchantContribute 如果使用的这张券是用户购买的，则该字段代表用户在购买这张券时商户优惠的金额
     *
     * @return self
     */
    public function setPurchaseMerchantContribute($purchaseMerchantContribute)
    {
        $this->container['purchaseMerchantContribute'] = $purchaseMerchantContribute;

        return $this;
    }

    /**
     * Gets templateId
     *
     * @return string|null
     */
    public function getTemplateId()
    {
        return $this->container['templateId'];
    }

    /**
     * Sets templateId
     *
     * @param string|null $templateId 券模板id
     *
     * @return self
     */
    public function setTemplateId($templateId)
    {
        $this->container['templateId'] = $templateId;

        return $this;
    }

    /**
     * Gets type
     *
     * @return string|null
     */
    public function getType()
    {
        return $this->container['type'];
    }

    /**
     * Sets type
     *
     * @param string|null $type 券类型
     *
     * @return self
     */
    public function setType($type)
    {
        $this->container['type'] = $type;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


