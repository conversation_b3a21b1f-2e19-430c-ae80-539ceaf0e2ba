<?php
/**
 * IsvSpiDefinition
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * IsvSpiDefinition Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class IsvSpiDefinition implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'IsvSpiDefinition';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'bizCode' => 'string',
        'description' => 'string',
        'icon' => 'string',
        'spiEndpoint' => 'string',
        'spiExtProperty' => 'string',
        'spiKey' => 'string',
        'spiName' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'bizCode' => null,
        'description' => null,
        'icon' => null,
        'spiEndpoint' => null,
        'spiExtProperty' => null,
        'spiKey' => null,
        'spiName' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'bizCode' => 'biz_code',
        'description' => 'description',
        'icon' => 'icon',
        'spiEndpoint' => 'spi_endpoint',
        'spiExtProperty' => 'spi_ext_property',
        'spiKey' => 'spi_key',
        'spiName' => 'spi_name'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'bizCode' => 'setBizCode',
        'description' => 'setDescription',
        'icon' => 'setIcon',
        'spiEndpoint' => 'setSpiEndpoint',
        'spiExtProperty' => 'setSpiExtProperty',
        'spiKey' => 'setSpiKey',
        'spiName' => 'setSpiName'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'bizCode' => 'getBizCode',
        'description' => 'getDescription',
        'icon' => 'getIcon',
        'spiEndpoint' => 'getSpiEndpoint',
        'spiExtProperty' => 'getSpiExtProperty',
        'spiKey' => 'getSpiKey',
        'spiName' => 'getSpiName'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['bizCode'] = $data['bizCode'] ?? null;
        $this->container['description'] = $data['description'] ?? null;
        $this->container['icon'] = $data['icon'] ?? null;
        $this->container['spiEndpoint'] = $data['spiEndpoint'] ?? null;
        $this->container['spiExtProperty'] = $data['spiExtProperty'] ?? null;
        $this->container['spiKey'] = $data['spiKey'] ?? null;
        $this->container['spiName'] = $data['spiName'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets bizCode
     *
     * @return string|null
     */
    public function getBizCode()
    {
        return $this->container['bizCode'];
    }

    /**
     * Sets bizCode
     *
     * @param string|null $bizCode ISV自定义的标识功能的业务代码，不可重复
     *
     * @return self
     */
    public function setBizCode($bizCode)
    {
        $this->container['bizCode'] = $bizCode;

        return $this;
    }

    /**
     * Gets description
     *
     * @return string|null
     */
    public function getDescription()
    {
        return $this->container['description'];
    }

    /**
     * Sets description
     *
     * @param string|null $description spi功能描述
     *
     * @return self
     */
    public function setDescription($description)
    {
        $this->container['description'] = $description;

        return $this;
    }

    /**
     * Gets icon
     *
     * @return string|null
     */
    public function getIcon()
    {
        return $this->container['icon'];
    }

    /**
     * Sets icon
     *
     * @param string|null $icon 功能图标文件url
     *
     * @return self
     */
    public function setIcon($icon)
    {
        $this->container['icon'] = $icon;

        return $this;
    }

    /**
     * Gets spiEndpoint
     *
     * @return string|null
     */
    public function getSpiEndpoint()
    {
        return $this->container['spiEndpoint'];
    }

    /**
     * Sets spiEndpoint
     *
     * @param string|null $spiEndpoint spi接口服务地址
     *
     * @return self
     */
    public function setSpiEndpoint($spiEndpoint)
    {
        $this->container['spiEndpoint'] = $spiEndpoint;

        return $this;
    }

    /**
     * Gets spiExtProperty
     *
     * @return string|null
     */
    public function getSpiExtProperty()
    {
        return $this->container['spiExtProperty'];
    }

    /**
     * Sets spiExtProperty
     *
     * @param string|null $spiExtProperty spi接口扩展参数，json格式字符串
     *
     * @return self
     */
    public function setSpiExtProperty($spiExtProperty)
    {
        $this->container['spiExtProperty'] = $spiExtProperty;

        return $this;
    }

    /**
     * Gets spiKey
     *
     * @return string|null
     */
    public function getSpiKey()
    {
        return $this->container['spiKey'];
    }

    /**
     * Sets spiKey
     *
     * @param string|null $spiKey CCM预先定义的spi key，与插件位置有关
     *
     * @return self
     */
    public function setSpiKey($spiKey)
    {
        $this->container['spiKey'] = $spiKey;

        return $this;
    }

    /**
     * Gets spiName
     *
     * @return string|null
     */
    public function getSpiName()
    {
        return $this->container['spiName'];
    }

    /**
     * Sets spiName
     *
     * @param string|null $spiName SPI 名称
     *
     * @return self
     */
    public function setSpiName($spiName)
    {
        $this->container['spiName'] = $spiName;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


