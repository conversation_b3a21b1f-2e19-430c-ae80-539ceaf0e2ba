<?php
/**
 * SettleCardInfo
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * SettleCardInfo Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class SettleCardInfo implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'SettleCardInfo';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'accountBranchName' => 'string',
        'accountHolderName' => 'string',
        'accountInstCity' => 'string',
        'accountInstId' => 'string',
        'accountInstName' => 'string',
        'accountInstProvince' => 'string',
        'accountNo' => 'string',
        'accountType' => 'string',
        'bankCode' => 'string',
        'usageType' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'accountBranchName' => null,
        'accountHolderName' => null,
        'accountInstCity' => null,
        'accountInstId' => null,
        'accountInstName' => null,
        'accountInstProvince' => null,
        'accountNo' => null,
        'accountType' => null,
        'bankCode' => null,
        'usageType' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'accountBranchName' => 'account_branch_name',
        'accountHolderName' => 'account_holder_name',
        'accountInstCity' => 'account_inst_city',
        'accountInstId' => 'account_inst_id',
        'accountInstName' => 'account_inst_name',
        'accountInstProvince' => 'account_inst_province',
        'accountNo' => 'account_no',
        'accountType' => 'account_type',
        'bankCode' => 'bank_code',
        'usageType' => 'usage_type'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'accountBranchName' => 'setAccountBranchName',
        'accountHolderName' => 'setAccountHolderName',
        'accountInstCity' => 'setAccountInstCity',
        'accountInstId' => 'setAccountInstId',
        'accountInstName' => 'setAccountInstName',
        'accountInstProvince' => 'setAccountInstProvince',
        'accountNo' => 'setAccountNo',
        'accountType' => 'setAccountType',
        'bankCode' => 'setBankCode',
        'usageType' => 'setUsageType'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'accountBranchName' => 'getAccountBranchName',
        'accountHolderName' => 'getAccountHolderName',
        'accountInstCity' => 'getAccountInstCity',
        'accountInstId' => 'getAccountInstId',
        'accountInstName' => 'getAccountInstName',
        'accountInstProvince' => 'getAccountInstProvince',
        'accountNo' => 'getAccountNo',
        'accountType' => 'getAccountType',
        'bankCode' => 'getBankCode',
        'usageType' => 'getUsageType'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['accountBranchName'] = $data['accountBranchName'] ?? null;
        $this->container['accountHolderName'] = $data['accountHolderName'] ?? null;
        $this->container['accountInstCity'] = $data['accountInstCity'] ?? null;
        $this->container['accountInstId'] = $data['accountInstId'] ?? null;
        $this->container['accountInstName'] = $data['accountInstName'] ?? null;
        $this->container['accountInstProvince'] = $data['accountInstProvince'] ?? null;
        $this->container['accountNo'] = $data['accountNo'] ?? null;
        $this->container['accountType'] = $data['accountType'] ?? null;
        $this->container['bankCode'] = $data['bankCode'] ?? null;
        $this->container['usageType'] = $data['usageType'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets accountBranchName
     *
     * @return string|null
     */
    public function getAccountBranchName()
    {
        return $this->container['accountBranchName'];
    }

    /**
     * Sets accountBranchName
     *
     * @param string|null $accountBranchName 开户支行名
     *
     * @return self
     */
    public function setAccountBranchName($accountBranchName)
    {
        $this->container['accountBranchName'] = $accountBranchName;

        return $this;
    }

    /**
     * Gets accountHolderName
     *
     * @return string|null
     */
    public function getAccountHolderName()
    {
        return $this->container['accountHolderName'];
    }

    /**
     * Sets accountHolderName
     *
     * @param string|null $accountHolderName 卡户名
     *
     * @return self
     */
    public function setAccountHolderName($accountHolderName)
    {
        $this->container['accountHolderName'] = $accountHolderName;

        return $this;
    }

    /**
     * Gets accountInstCity
     *
     * @return string|null
     */
    public function getAccountInstCity()
    {
        return $this->container['accountInstCity'];
    }

    /**
     * Sets accountInstCity
     *
     * @param string|null $accountInstCity 开户行所在地-市
     *
     * @return self
     */
    public function setAccountInstCity($accountInstCity)
    {
        $this->container['accountInstCity'] = $accountInstCity;

        return $this;
    }

    /**
     * Gets accountInstId
     *
     * @return string|null
     */
    public function getAccountInstId()
    {
        return $this->container['accountInstId'];
    }

    /**
     * Sets accountInstId
     *
     * @param string|null $accountInstId 开户行简称缩写
     *
     * @return self
     */
    public function setAccountInstId($accountInstId)
    {
        $this->container['accountInstId'] = $accountInstId;

        return $this;
    }

    /**
     * Gets accountInstName
     *
     * @return string|null
     */
    public function getAccountInstName()
    {
        return $this->container['accountInstName'];
    }

    /**
     * Sets accountInstName
     *
     * @param string|null $accountInstName 银行名称
     *
     * @return self
     */
    public function setAccountInstName($accountInstName)
    {
        $this->container['accountInstName'] = $accountInstName;

        return $this;
    }

    /**
     * Gets accountInstProvince
     *
     * @return string|null
     */
    public function getAccountInstProvince()
    {
        return $this->container['accountInstProvince'];
    }

    /**
     * Sets accountInstProvince
     *
     * @param string|null $accountInstProvince 开户行所在地-省
     *
     * @return self
     */
    public function setAccountInstProvince($accountInstProvince)
    {
        $this->container['accountInstProvince'] = $accountInstProvince;

        return $this;
    }

    /**
     * Gets accountNo
     *
     * @return string|null
     */
    public function getAccountNo()
    {
        return $this->container['accountNo'];
    }

    /**
     * Sets accountNo
     *
     * @param string|null $accountNo 银行卡号
     *
     * @return self
     */
    public function setAccountNo($accountNo)
    {
        $this->container['accountNo'] = $accountNo;

        return $this;
    }

    /**
     * Gets accountType
     *
     * @return string|null
     */
    public function getAccountType()
    {
        return $this->container['accountType'];
    }

    /**
     * Sets accountType
     *
     * @param string|null $accountType 卡类型  借记卡-DC  信用卡-CC
     *
     * @return self
     */
    public function setAccountType($accountType)
    {
        $this->container['accountType'] = $accountType;

        return $this;
    }

    /**
     * Gets bankCode
     *
     * @return string|null
     */
    public function getBankCode()
    {
        return $this->container['bankCode'];
    }

    /**
     * Sets bankCode
     *
     * @param string|null $bankCode 联行号
     *
     * @return self
     */
    public function setBankCode($bankCode)
    {
        $this->container['bankCode'] = $bankCode;

        return $this;
    }

    /**
     * Gets usageType
     *
     * @return string|null
     */
    public function getUsageType()
    {
        return $this->container['usageType'];
    }

    /**
     * Sets usageType
     *
     * @param string|null $usageType 账号使用类型  对公-01  对私-02
     *
     * @return self
     */
    public function setUsageType($usageType)
    {
        $this->container['usageType'] = $usageType;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


