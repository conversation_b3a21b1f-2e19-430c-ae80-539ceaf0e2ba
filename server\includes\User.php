<?php
/**
 * 用户管理类
 * 版本: 1.0.0
 * 创建时间: 2024-12-19
 */

require_once 'database.php';

class User {
    private $db;
    private $userId;
    private $userData;

    public function __construct($userId = null) {
        $this->db = getDB();
        $this->userId = $userId;
        
        if ($userId) {
            $this->loadUserData();
        }
    }

    /**
     * 用户注册
     */
    public function register($username, $password, $email = null) {
        try {
            // 检查用户名是否已存在
            if ($this->db->exists('users', ['username' => $username])) {
                throw new Exception('用户名已存在');
            }

            // 检查邮箱是否已存在
            if ($email && $this->db->exists('users', ['email' => $email])) {
                throw new Exception('邮箱已被使用');
            }

            // 密码加密
            $hashedPassword = $this->hashPassword($password);

            // 开始事务
            $this->db->beginTransaction();

            // 插入用户数据
            $userData = [
                'username' => $username,
                'password' => $hashedPassword,
                'email' => $email,
                'status' => 'active'
            ];

            $userId = $this->db->insertData('users', $userData);

            // 创建积分账户
            $pointsData = [
                'user_id' => $userId,
                'balance' => DEFAULT_REGISTER_GIFT_POINTS
            ];
            $this->db->insertData('points', $pointsData);

            // 记录赠送积分交易
            if (DEFAULT_REGISTER_GIFT_POINTS > 0) {
                $transactionData = [
                    'user_id' => $userId,
                    'amount' => DEFAULT_REGISTER_GIFT_POINTS,
                    'transaction_type' => 'gift',
                    'description' => '注册赠送积分',
                    'balance_before' => 0,
                    'balance_after' => DEFAULT_REGISTER_GIFT_POINTS,
                    'ip_address' => getClientIP()
                ];
                $this->db->insertData('point_transactions', $transactionData);
            }

            $this->db->commit();

            writeLog("用户注册成功: $username (ID: $userId)", 'INFO');

            return [
                'success' => true,
                'user_id' => $userId,
                'message' => '注册成功'
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            writeLog("用户注册失败: " . $e->getMessage(), 'ERROR');
            throw $e;
        }
    }

    /**
     * 用户登录
     */
    public function login($username, $password) {
        try {
            // 查找用户
            $user = $this->db->selectOne('users', ['username' => $username]);
            
            if (!$user) {
                throw new Exception('用户名或密码错误');
            }

            // 检查用户状态
            if ($user['status'] !== 'active') {
                throw new Exception('账户已被禁用');
            }

            // 验证密码
            if (!$this->verifyPassword($password, $user['password'])) {
                throw new Exception('用户名或密码错误');
            }

            // 生成登录令牌
            $token = $this->generateToken($user['id']);
            $tokenExpires = date('Y-m-d H:i:s', time() + TOKEN_EXPIRE_HOURS * 3600);

            // 更新用户登录信息
            $updateData = [
                'token' => $token,
                'token_expires' => $tokenExpires,
                'last_login' => date('Y-m-d H:i:s'),
                'last_login_ip' => getClientIP()
            ];

            $this->db->updateData('users', $updateData, ['id' => $user['id']]);

            writeLog("用户登录成功: $username (ID: {$user['id']})", 'INFO');

            return [
                'success' => true,
                'token' => $token,
                'user_id' => $user['id'],
                'username' => $user['username'],
                'email' => $user['email'],
                'expires_at' => $tokenExpires,
                'message' => '登录成功'
            ];

        } catch (Exception $e) {
            writeLog("用户登录失败: " . $e->getMessage(), 'ERROR');
            throw $e;
        }
    }

    /**
     * 验证用户令牌
     */
    public function validateToken($token) {
        if (empty($token)) {
            return false;
        }

        // 移除Bearer前缀
        if (strpos($token, 'Bearer ') === 0) {
            $token = substr($token, 7);
        }

        $user = $this->db->selectOne('users', [
            'token' => $token,
            'status' => 'active'
        ]);

        if (!$user) {
            return false;
        }

        // 检查令牌是否过期
        if (strtotime($user['token_expires']) < time()) {
            return false;
        }

        return $user['id'];
    }

    /**
     * 获取用户信息
     */
    public function getUserInfo($userId = null) {
        $userId = $userId ?: $this->userId;
        
        if (!$userId) {
            throw new Exception('用户ID不能为空');
        }

        $user = $this->db->selectOne('users', ['id' => $userId]);
        
        if (!$user) {
            throw new Exception('用户不存在');
        }

        // 移除敏感信息
        unset($user['password'], $user['token']);

        return $user;
    }

    /**
     * 更新用户信息
     */
    public function updateUserInfo($data, $userId = null) {
        $userId = $userId ?: $this->userId;
        
        if (!$userId) {
            throw new Exception('用户ID不能为空');
        }

        // 过滤允许更新的字段
        $allowedFields = ['email'];
        $updateData = [];

        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = $data[$field];
            }
        }

        if (empty($updateData)) {
            throw new Exception('没有可更新的数据');
        }

        // 检查邮箱唯一性
        if (isset($updateData['email'])) {
            $existingUser = $this->db->selectOne('users', [
                'email' => $updateData['email'],
                'id !=' => $userId
            ]);
            
            if ($existingUser) {
                throw new Exception('邮箱已被其他用户使用');
            }
        }

        $affected = $this->db->updateData('users', $updateData, ['id' => $userId]);

        if ($affected > 0) {
            writeLog("用户信息更新成功: 用户ID $userId", 'INFO');
            return true;
        }

        return false;
    }

    /**
     * 修改密码
     */
    public function changePassword($oldPassword, $newPassword, $userId = null) {
        $userId = $userId ?: $this->userId;
        
        if (!$userId) {
            throw new Exception('用户ID不能为空');
        }

        $user = $this->db->selectOne('users', ['id' => $userId]);
        
        if (!$user) {
            throw new Exception('用户不存在');
        }

        // 验证旧密码
        if (!$this->verifyPassword($oldPassword, $user['password'])) {
            throw new Exception('原密码错误');
        }

        // 更新密码
        $hashedPassword = $this->hashPassword($newPassword);
        $affected = $this->db->updateData('users', 
            ['password' => $hashedPassword], 
            ['id' => $userId]
        );

        if ($affected > 0) {
            writeLog("用户密码修改成功: 用户ID $userId", 'INFO');
            return true;
        }

        return false;
    }

    /**
     * 用户登出
     */
    public function logout($userId = null) {
        $userId = $userId ?: $this->userId;
        
        if (!$userId) {
            return false;
        }

        // 清除令牌
        $this->db->updateData('users', 
            ['token' => null, 'token_expires' => null], 
            ['id' => $userId]
        );

        writeLog("用户登出: 用户ID $userId", 'INFO');
        return true;
    }

    /**
     * 加载用户数据
     */
    private function loadUserData() {
        if ($this->userId) {
            $this->userData = $this->getUserInfo($this->userId);
        }
    }

    /**
     * 密码加密
     */
    private function hashPassword($password) {
        return password_hash($password . PASSWORD_SALT, PASSWORD_BCRYPT);
    }

    /**
     * 密码验证
     */
    private function verifyPassword($password, $hash) {
        return password_verify($password . PASSWORD_SALT, $hash);
    }

    /**
     * 生成登录令牌
     */
    private function generateToken($userId) {
        $payload = [
            'user_id' => $userId,
            'timestamp' => time(),
            'random' => generateRandomString(16)
        ];
        
        return base64_encode(json_encode($payload)) . '.' . 
               hash_hmac('sha256', json_encode($payload), JWT_SECRET);
    }

    /**
     * 获取用户ID
     */
    public function getUserId() {
        return $this->userId;
    }

    /**
     * 获取用户数据
     */
    public function getUserData() {
        return $this->userData;
    }
}

?>
