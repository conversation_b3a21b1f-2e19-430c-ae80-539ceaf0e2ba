#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化收款工具主程序
版本: 1.0.0
创建时间: 2024-12-19
"""

import os
import sys
import time
import signal
from typing import Optional
from colorama import Fore, Style, init

# 初始化colorama
init(autoreset=True)

# 导入本地模块
from config import config, CONFIG_HELP
from payment_client import PaymentClient
from qr_generator import QRGenerator

class SimplePaymentTool:
    """简化收款工具主类"""
    
    def __init__(self):
        """初始化收款工具"""
        self.payment_client = None
        self.qr_generator = None
        self.running = True
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        
        # 初始化组件
        self.init_components()
    
    def signal_handler(self, signum, frame):
        """信号处理器（Ctrl+C）"""
        print(f"\n{Fore.YELLOW}正在退出程序...")
        self.running = False
        sys.exit(0)
    
    def init_components(self):
        """初始化组件"""
        try:
            # 检查配置
            if not self.check_config():
                return False
            
            # 初始化支付客户端
            api_url = config.get_api_url()
            token = config.get_token()
            
            self.payment_client = PaymentClient(api_url, token)
            
            # 初始化二维码生成器
            self.qr_generator = QRGenerator(
                size=config.QR_CODE_SIZE,
                border=config.QR_CODE_BORDER
            )
            
            print(f"{Fore.GREEN}✓ 组件初始化成功")
            return True
            
        except Exception as e:
            print(f"{Fore.RED}✗ 组件初始化失败: {e}")
            return False
    
    def check_config(self) -> bool:
        """检查配置"""
        print(f"{Fore.BLUE}检查配置...")
        
        # 检查API URL
        if not config.API_BASE_URL or config.API_BASE_URL == "":
            print(f"{Fore.RED}✗ API_BASE_URL 未配置")
            return False
        
        # 检查Token
        token = config.get_token()
        if not token or token == "your_user_token_here":
            print(f"{Fore.RED}✗ USER_TOKEN 未配置或使用默认值")
            print(f"{Fore.YELLOW}请在 config.py 中设置正确的 USER_TOKEN")
            print(f"{Fore.YELLOW}或设置环境变量: export PAYMENT_TOKEN=your_token")
            print()
            print(CONFIG_HELP)
            return False
        
        print(f"{Fore.GREEN}✓ 配置检查通过")
        return True
    
    def show_banner(self):
        """显示程序横幅"""
        print(f"{Fore.CYAN}{Style.BRIGHT}")
        print("🎯 简化收款工具 v1.0.0")
        print("=" * 32)
        print(f"{Style.RESET_ALL}")
    
    def get_amount_input(self) -> Optional[float]:
        """获取用户输入的金额"""
        while True:
            try:
                amount_str = input(f"{Fore.CYAN}请输入收款金额: {Style.RESET_ALL}")
                
                # 检查是否退出
                if amount_str.lower() in ['q', 'quit', 'exit', '退出']:
                    return None
                
                # 验证并转换金额
                amount = self.payment_client.validate_amount(amount_str)
                
                # 确认金额
                print(f"{Fore.YELLOW}确认收款金额: {self.payment_client.format_amount(amount)}")
                confirm = input(f"{Fore.CYAN}确认吗？(y/n): {Style.RESET_ALL}")
                
                if confirm.lower() in ['y', 'yes', '是', '确认']:
                    return amount
                else:
                    print(f"{Fore.YELLOW}请重新输入金额")
                    continue
                    
            except ValueError as e:
                print(f"{Fore.RED}✗ {e}")
                print(f"{Fore.YELLOW}请重新输入（输入 q 退出）")
                continue
            except KeyboardInterrupt:
                return None
    
    def process_payment(self, amount: float) -> bool:
        """处理支付流程"""
        try:
            # 1. 创建订单
            print(f"\n{Fore.BLUE}{'='*50}")
            print(f"{Fore.BLUE}开始处理收款: {self.payment_client.format_amount(amount)}")
            print(f"{Fore.BLUE}{'='*50}")
            
            order_info = self.payment_client.create_order(amount)
            order_number = order_info['order_number']
            qr_code = order_info['qr_code']
            
            # 2. 生成并显示二维码
            print(f"\n{Fore.BLUE}生成支付二维码...")
            
            # 准备二维码标题
            title = f"收款 {self.payment_client.format_amount(amount)}"
            
            # 获取保存路径
            save_path = config.get_qr_save_path(order_number) if config.SAVE_QR_CODES else None
            
            # 显示二维码
            if config.AUTO_OPEN_QR:
                success = self.qr_generator.display_qr_code(qr_code, title, save_path)
                if not success and config.SHOW_ASCII_QR:
                    print(f"{Fore.YELLOW}尝试显示ASCII二维码...")
                    self.qr_generator.print_qr_code_ascii(qr_code)
            else:
                # 只保存不显示
                if save_path:
                    self.qr_generator.save_qr_code(qr_code, save_path, title)
                    print(f"{Fore.GREEN}✓ 二维码已保存: {save_path}")
                
                if config.SHOW_ASCII_QR:
                    self.qr_generator.print_qr_code_ascii(qr_code)
            
            # 3. 等待支付完成
            print(f"\n{Fore.BLUE}{'='*50}")
            result = self.payment_client.wait_for_payment(
                order_number,
                timeout=config.PAYMENT_TIMEOUT,
                check_interval=config.CHECK_INTERVAL
            )
            print(f"{Fore.BLUE}{'='*50}")
            
            # 4. 处理支付结果
            if result['success']:
                print(f"\n{Fore.GREEN}🎉 收款成功！")
                order_info = result['order_info']
                print(f"{Fore.GREEN}  金额: {self.payment_client.format_amount(order_info['amount'])}")
                print(f"{Fore.GREEN}  积分: {order_info['points']}")
                if order_info.get('trade_no'):
                    print(f"{Fore.GREEN}  支付宝交易号: {order_info['trade_no']}")
                return True
            else:
                print(f"\n{Fore.RED}❌ 收款失败: {result['message']}")
                return False
                
        except Exception as e:
            print(f"\n{Fore.RED}✗ 处理支付时出错: {e}")
            return False
    
    def ask_continue(self) -> bool:
        """询问是否继续"""
        try:
            print()
            answer = input(f"{Fore.CYAN}是否继续收款？(y/n): {Style.RESET_ALL}")
            return answer.lower() in ['y', 'yes', '是', '继续']
        except KeyboardInterrupt:
            return False
    
    def run(self):
        """运行主程序"""
        try:
            # 显示横幅
            self.show_banner()
            
            # 检查初始化
            if not self.payment_client or not self.qr_generator:
                print(f"{Fore.RED}程序初始化失败，无法继续")
                return
            
            print(f"{Fore.GREEN}程序启动成功！")
            print(f"{Fore.YELLOW}提示: 输入 'q' 或按 Ctrl+C 退出程序")
            print()
            
            # 主循环
            while self.running:
                try:
                    # 获取金额输入
                    amount = self.get_amount_input()
                    if amount is None:
                        break
                    
                    # 处理支付
                    success = self.process_payment(amount)
                    
                    # 询问是否继续
                    if not self.ask_continue():
                        break
                        
                except KeyboardInterrupt:
                    break
                except Exception as e:
                    print(f"{Fore.RED}✗ 程序运行出错: {e}")
                    if not self.ask_continue():
                        break
            
            # 退出提示
            print(f"\n{Fore.CYAN}感谢使用简化收款工具！")
            
        except Exception as e:
            print(f"{Fore.RED}✗ 程序运行失败: {e}")

def main():
    """主函数"""
    try:
        # 创建并运行收款工具
        tool = SimplePaymentTool()
        tool.run()
        
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}程序被用户中断")
    except Exception as e:
        print(f"{Fore.RED}程序启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
