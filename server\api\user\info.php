<?php
/**
 * 获取用户信息API
 * 版本: 1.0.0
 * 创建时间: 2024-12-19
 */

require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/User.php';
require_once '../../includes/Points.php';

// 验证请求方法
validateRequestMethod(['GET', 'POST']);

try {
    // 验证用户Token
    $token = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    if (empty($token)) {
        handleError('缺少授权令牌', API_AUTH_ERROR_CODE);
    }
    
    $user = new User();
    $userId = $user->validateToken($token);
    
    if (!$userId) {
        handleError('无效的授权令牌', API_AUTH_ERROR_CODE);
    }
    
    // 获取用户信息
    $userInfo = $user->getUserInfo($userId);
    
    // 获取积分信息
    $points = new Points($userId);
    $pointsInfo = $points->getAccountInfo();
    
    // 获取积分统计
    $statistics = $points->getStatistics(30);
    
    // 记录访问日志
    writeLog("用户信息查询: 用户ID $userId", 'INFO');
    
    // 返回成功响应
    jsonResponse([
        'user_info' => $userInfo,
        'points' => $pointsInfo,
        'statistics' => $statistics
    ], API_SUCCESS_CODE, '获取用户信息成功');
    
} catch (Exception $e) {
    // 记录错误日志
    writeLog("获取用户信息失败: " . $e->getMessage(), 'ERROR');
    
    // 返回错误响应
    handleError($e->getMessage(), API_AUTH_ERROR_CODE);
}

?>
