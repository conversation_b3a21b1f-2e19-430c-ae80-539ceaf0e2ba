<?php
/**
 * IndirectSupportCredentials
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * IndirectSupportCredentials Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class IndirectSupportCredentials implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'IndirectSupportCredentials';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'city' => 'string',
        'cityCode' => 'string',
        'district' => 'string',
        'districtCode' => 'string',
        'merchantType' => 'string',
        'province' => 'string',
        'provinceCode' => 'string',
        'storeAddress' => 'string',
        'storeDoorImg' => 'string',
        'storeInnerImg' => 'string',
        'storeName' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'city' => null,
        'cityCode' => null,
        'district' => null,
        'districtCode' => null,
        'merchantType' => null,
        'province' => null,
        'provinceCode' => null,
        'storeAddress' => null,
        'storeDoorImg' => null,
        'storeInnerImg' => null,
        'storeName' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'city' => 'city',
        'cityCode' => 'city_code',
        'district' => 'district',
        'districtCode' => 'district_code',
        'merchantType' => 'merchant_type',
        'province' => 'province',
        'provinceCode' => 'province_code',
        'storeAddress' => 'store_address',
        'storeDoorImg' => 'store_door_img',
        'storeInnerImg' => 'store_inner_img',
        'storeName' => 'store_name'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'city' => 'setCity',
        'cityCode' => 'setCityCode',
        'district' => 'setDistrict',
        'districtCode' => 'setDistrictCode',
        'merchantType' => 'setMerchantType',
        'province' => 'setProvince',
        'provinceCode' => 'setProvinceCode',
        'storeAddress' => 'setStoreAddress',
        'storeDoorImg' => 'setStoreDoorImg',
        'storeInnerImg' => 'setStoreInnerImg',
        'storeName' => 'setStoreName'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'city' => 'getCity',
        'cityCode' => 'getCityCode',
        'district' => 'getDistrict',
        'districtCode' => 'getDistrictCode',
        'merchantType' => 'getMerchantType',
        'province' => 'getProvince',
        'provinceCode' => 'getProvinceCode',
        'storeAddress' => 'getStoreAddress',
        'storeDoorImg' => 'getStoreDoorImg',
        'storeInnerImg' => 'getStoreInnerImg',
        'storeName' => 'getStoreName'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['city'] = $data['city'] ?? null;
        $this->container['cityCode'] = $data['cityCode'] ?? null;
        $this->container['district'] = $data['district'] ?? null;
        $this->container['districtCode'] = $data['districtCode'] ?? null;
        $this->container['merchantType'] = $data['merchantType'] ?? null;
        $this->container['province'] = $data['province'] ?? null;
        $this->container['provinceCode'] = $data['provinceCode'] ?? null;
        $this->container['storeAddress'] = $data['storeAddress'] ?? null;
        $this->container['storeDoorImg'] = $data['storeDoorImg'] ?? null;
        $this->container['storeInnerImg'] = $data['storeInnerImg'] ?? null;
        $this->container['storeName'] = $data['storeName'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets city
     *
     * @return string|null
     * @deprecated
     */
    public function getCity()
    {
        return $this->container['city'];
    }

    /**
     * Sets city
     *
     * @param string|null $city 门店城市(该值已废弃，以city_code字段映射的城市名称为准)
     *
     * @return self
     * @deprecated
     */
    public function setCity($city)
    {
        $this->container['city'] = $city;

        return $this;
    }

    /**
     * Gets cityCode
     *
     * @return string|null
     */
    public function getCityCode()
    {
        return $this->container['cityCode'];
    }

    /**
     * Sets cityCode
     *
     * @param string|null $cityCode 门店市行政区号（数字）
     *
     * @return self
     */
    public function setCityCode($cityCode)
    {
        $this->container['cityCode'] = $cityCode;

        return $this;
    }

    /**
     * Gets district
     *
     * @return string|null
     * @deprecated
     */
    public function getDistrict()
    {
        return $this->container['district'];
    }

    /**
     * Sets district
     *
     * @param string|null $district 门店所在区县。(该值已废弃，以district_code字段映射的区县名称为准)
     *
     * @return self
     * @deprecated
     */
    public function setDistrict($district)
    {
        $this->container['district'] = $district;

        return $this;
    }

    /**
     * Gets districtCode
     *
     * @return string|null
     */
    public function getDistrictCode()
    {
        return $this->container['districtCode'];
    }

    /**
     * Sets districtCode
     *
     * @param string|null $districtCode 门店街道区号（数字）
     *
     * @return self
     */
    public function setDistrictCode($districtCode)
    {
        $this->container['districtCode'] = $districtCode;

        return $this;
    }

    /**
     * Gets merchantType
     *
     * @return string|null
     */
    public function getMerchantType()
    {
        return $this->container['merchantType'];
    }

    /**
     * Sets merchantType
     *
     * @param string|null $merchantType 小微商户经营类型，枚举值：门店场所（STORE）、流动经营（STALL）
     *
     * @return self
     */
    public function setMerchantType($merchantType)
    {
        $this->container['merchantType'] = $merchantType;

        return $this;
    }

    /**
     * Gets province
     *
     * @return string|null
     * @deprecated
     */
    public function getProvince()
    {
        return $this->container['province'];
    }

    /**
     * Sets province
     *
     * @param string|null $province 门店省份(该值已废弃，以province_code字段映射的省份名称为准)
     *
     * @return self
     * @deprecated
     */
    public function setProvince($province)
    {
        $this->container['province'] = $province;

        return $this;
    }

    /**
     * Gets provinceCode
     *
     * @return string|null
     */
    public function getProvinceCode()
    {
        return $this->container['provinceCode'];
    }

    /**
     * Sets provinceCode
     *
     * @param string|null $provinceCode 门店省行政区号（数字
     *
     * @return self
     */
    public function setProvinceCode($provinceCode)
    {
        $this->container['provinceCode'] = $provinceCode;

        return $this;
    }

    /**
     * Gets storeAddress
     *
     * @return string|null
     */
    public function getStoreAddress()
    {
        return $this->container['storeAddress'];
    }

    /**
     * Sets storeAddress
     *
     * @param string|null $storeAddress 门店场所填写门店详细地址，流动经营类型填“无”
     *
     * @return self
     */
    public function setStoreAddress($storeAddress)
    {
        $this->container['storeAddress'] = $storeAddress;

        return $this;
    }

    /**
     * Gets storeDoorImg
     *
     * @return string|null
     */
    public function getStoreDoorImg()
    {
        return $this->container['storeDoorImg'];
    }

    /**
     * Sets storeDoorImg
     *
     * @param string|null $storeDoorImg 门店门头照信息或摊位照（使用图片上传接口）
     *
     * @return self
     */
    public function setStoreDoorImg($storeDoorImg)
    {
        $this->container['storeDoorImg'] = $storeDoorImg;

        return $this;
    }

    /**
     * Gets storeInnerImg
     *
     * @return string|null
     */
    public function getStoreInnerImg()
    {
        return $this->container['storeInnerImg'];
    }

    /**
     * Sets storeInnerImg
     *
     * @param string|null $storeInnerImg 门店店内照片或者摊位照侧面（使用图片上传接口）
     *
     * @return self
     */
    public function setStoreInnerImg($storeInnerImg)
    {
        $this->container['storeInnerImg'] = $storeInnerImg;

        return $this;
    }

    /**
     * Gets storeName
     *
     * @return string|null
     */
    public function getStoreName()
    {
        return $this->container['storeName'];
    }

    /**
     * Sets storeName
     *
     * @param string|null $storeName 门店名称
     *
     * @return self
     */
    public function setStoreName($storeName)
    {
        $this->container['storeName'] = $storeName;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


