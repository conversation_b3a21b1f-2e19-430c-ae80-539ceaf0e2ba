<?php
/**
 * 获取资源列表API
 * 版本: 1.0.0
 * 创建时间: 2024-12-19
 */

require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/User.php';

// 验证请求方法
validateRequestMethod(['GET']);

try {
    // 验证用户Token
    $token = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    if (empty($token)) {
        handleError('缺少授权令牌', API_AUTH_ERROR_CODE);
    }
    
    $user = new User();
    $userId = $user->validateToken($token);
    
    if (!$userId) {
        handleError('无效的授权令牌', API_AUTH_ERROR_CODE);
    }
    
    // 获取查询参数
    $page = max(1, (int)($_GET['page'] ?? 1));
    $limit = min(100, max(1, (int)($_GET['limit'] ?? 20)));
    $platform = $_GET['platform'] ?? null;
    $resourceType = $_GET['resource_type'] ?? null;
    $keyword = $_GET['keyword'] ?? null;
    $sortBy = $_GET['sort_by'] ?? 'created_at';
    $sortOrder = $_GET['sort_order'] ?? 'DESC';
    
    // 验证排序字段
    $allowedSortFields = ['created_at', 'download_count', 'points_cost', 'name'];
    if (!in_array($sortBy, $allowedSortFields)) {
        $sortBy = 'created_at';
    }
    
    // 验证排序方向
    $sortOrder = strtoupper($sortOrder) === 'ASC' ? 'ASC' : 'DESC';
    
    $db = getDB();
    $offset = ($page - 1) * $limit;
    
    // 构建查询条件
    $conditions = ['status' => 'active'];
    $params = [];
    
    if ($platform) {
        $conditions['platform'] = $platform;
    }
    
    if ($resourceType) {
        $conditions['resource_type'] = $resourceType;
    }
    
    // 构建SQL查询
    $whereClause = 'WHERE status = ?';
    $params[] = 'active';
    
    if ($platform) {
        $whereClause .= ' AND platform = ?';
        $params[] = $platform;
    }
    
    if ($resourceType) {
        $whereClause .= ' AND resource_type = ?';
        $params[] = $resourceType;
    }
    
    if ($keyword) {
        $whereClause .= ' AND (name LIKE ? OR description LIKE ? OR tags LIKE ?)';
        $keywordParam = '%' . $keyword . '%';
        $params[] = $keywordParam;
        $params[] = $keywordParam;
        $params[] = $keywordParam;
    }
    
    // 获取总数
    $countSql = "SELECT COUNT(*) as total FROM resources $whereClause";
    $totalResult = $db->fetchOne($countSql, $params);
    $total = $totalResult['total'];
    
    // 获取资源列表
    $sql = "SELECT id, name, platform, resource_id, points_cost, download_count, 
                   resource_type, file_size, duration, preview_url, description, 
                   tags, created_at 
            FROM resources 
            $whereClause 
            ORDER BY $sortBy $sortOrder 
            LIMIT $limit OFFSET $offset";
    
    $resources = $db->fetchAll($sql, $params);
    
    // 格式化资源数据
    foreach ($resources as &$resource) {
        $resource['id'] = (int)$resource['id'];
        $resource['points_cost'] = (int)$resource['points_cost'];
        $resource['download_count'] = (int)$resource['download_count'];
        $resource['file_size'] = $resource['file_size'] ? (int)$resource['file_size'] : null;
        $resource['duration'] = $resource['duration'] ? (int)$resource['duration'] : null;
        $resource['tags'] = $resource['tags'] ? explode(',', $resource['tags']) : [];
    }
    
    // 获取平台统计
    $platformStats = $db->fetchAll("
        SELECT platform, COUNT(*) as count 
        FROM resources 
        WHERE status = 'active' 
        GROUP BY platform 
        ORDER BY count DESC
    ");
    
    // 记录查询日志
    writeLog("资源列表查询: 用户ID $userId, 页码 $page, 平台 " . ($platform ?: '全部'), 'INFO');
    
    // 返回成功响应
    jsonResponse([
        'total' => (int)$total,
        'page' => $page,
        'limit' => $limit,
        'pages' => ceil($total / $limit),
        'platform_stats' => $platformStats,
        'data' => $resources
    ], API_SUCCESS_CODE, '获取资源列表成功');
    
} catch (Exception $e) {
    // 记录错误日志
    writeLog("获取资源列表失败: " . $e->getMessage(), 'ERROR');
    
    // 返回错误响应
    handleError($e->getMessage(), API_AUTH_ERROR_CODE);
}

?>
