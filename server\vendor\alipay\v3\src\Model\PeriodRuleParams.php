<?php
/**
 * PeriodRuleParams
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * PeriodRuleParams Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class PeriodRuleParams implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'PeriodRuleParams';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'executeTime' => 'string',
        'period' => 'int',
        'periodType' => 'string',
        'sceneRuleParams' => '\Alipay\OpenAPISDK\Model\SceneRuleParams',
        'singleAmount' => 'string',
        'totalAmount' => 'string',
        'totalPayments' => 'int'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'executeTime' => null,
        'period' => null,
        'periodType' => null,
        'sceneRuleParams' => null,
        'singleAmount' => null,
        'totalAmount' => null,
        'totalPayments' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'executeTime' => 'execute_time',
        'period' => 'period',
        'periodType' => 'period_type',
        'sceneRuleParams' => 'scene_rule_params',
        'singleAmount' => 'single_amount',
        'totalAmount' => 'total_amount',
        'totalPayments' => 'total_payments'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'executeTime' => 'setExecuteTime',
        'period' => 'setPeriod',
        'periodType' => 'setPeriodType',
        'sceneRuleParams' => 'setSceneRuleParams',
        'singleAmount' => 'setSingleAmount',
        'totalAmount' => 'setTotalAmount',
        'totalPayments' => 'setTotalPayments'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'executeTime' => 'getExecuteTime',
        'period' => 'getPeriod',
        'periodType' => 'getPeriodType',
        'sceneRuleParams' => 'getSceneRuleParams',
        'singleAmount' => 'getSingleAmount',
        'totalAmount' => 'getTotalAmount',
        'totalPayments' => 'getTotalPayments'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['executeTime'] = $data['executeTime'] ?? null;
        $this->container['period'] = $data['period'] ?? null;
        $this->container['periodType'] = $data['periodType'] ?? null;
        $this->container['sceneRuleParams'] = $data['sceneRuleParams'] ?? null;
        $this->container['singleAmount'] = $data['singleAmount'] ?? null;
        $this->container['totalAmount'] = $data['totalAmount'] ?? null;
        $this->container['totalPayments'] = $data['totalPayments'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets executeTime
     *
     * @return string|null
     */
    public function getExecuteTime()
    {
        return $this->container['executeTime'];
    }

    /**
     * Sets executeTime
     *
     * @param string|null $executeTime 首次执行时间execute_time是周期扣款产品必填，即商户发起首次扣款的时间。精确到日，格式为yyyy-MM-dd 结合其他必填的扣款周期参数，会确定商户以后的扣款计划。发起扣款的时间需符合这里的扣款计划。
     *
     * @return self
     */
    public function setExecuteTime($executeTime)
    {
        $this->container['executeTime'] = $executeTime;

        return $this;
    }

    /**
     * Gets period
     *
     * @return int|null
     */
    public function getPeriod()
    {
        return $this->container['period'];
    }

    /**
     * Sets period
     *
     * @param int|null $period 周期数period是周期扣款产品必填。与另一参数period_type组合使用确定扣款周期，例如period_type为DAY，period=90，则扣款周期为90天。
     *
     * @return self
     */
    public function setPeriod($period)
    {
        $this->container['period'] = $period;

        return $this;
    }

    /**
     * Gets periodType
     *
     * @return string|null
     */
    public function getPeriodType()
    {
        return $this->container['periodType'];
    }

    /**
     * Sets periodType
     *
     * @param string|null $periodType 周期类型period_type是周期扣款产品必填，枚举值为DAY和MONTH。 DAY即扣款周期按天计，MONTH代表扣款周期按自然月。 与另一参数period组合使用确定扣款周期，例如period_type为DAY，period=30，则扣款周期为30天；period_type为MONTH，period=3，则扣款周期为3个自然月。 自然月是指，不论这个月有多少天，周期都计算到月份中的同一日期。例如1月3日到2月3日为一个自然月，1月3日到4月3日为三个自然月。注意周期类型使用MONTH的时候，计划扣款时间execute_time不允许传28日之后的日期（可以传28日），以此避免有些月份可能不存在对应日期的情况。
     *
     * @return self
     */
    public function setPeriodType($periodType)
    {
        $this->container['periodType'] = $periodType;

        return $this;
    }

    /**
     * Gets sceneRuleParams
     *
     * @return \Alipay\OpenAPISDK\Model\SceneRuleParams|null
     */
    public function getSceneRuleParams()
    {
        return $this->container['sceneRuleParams'];
    }

    /**
     * Sets sceneRuleParams
     *
     * @param \Alipay\OpenAPISDK\Model\SceneRuleParams|null $sceneRuleParams sceneRuleParams
     *
     * @return self
     */
    public function setSceneRuleParams($sceneRuleParams)
    {
        $this->container['sceneRuleParams'] = $sceneRuleParams;

        return $this;
    }

    /**
     * Gets singleAmount
     *
     * @return string|null
     */
    public function getSingleAmount()
    {
        return $this->container['singleAmount'];
    }

    /**
     * Sets singleAmount
     *
     * @param string|null $singleAmount 单次扣款最大金额single_amount是周期扣款产品必填，即每次发起扣款时限制的最大金额，单位为元。商户每次发起扣款都不允许大于此金额。
     *
     * @return self
     */
    public function setSingleAmount($singleAmount)
    {
        $this->container['singleAmount'] = $singleAmount;

        return $this;
    }

    /**
     * Gets totalAmount
     *
     * @return string|null
     */
    public function getTotalAmount()
    {
        return $this->container['totalAmount'];
    }

    /**
     * Sets totalAmount
     *
     * @param string|null $totalAmount 总金额限制，单位为元。如果传入此参数，商户多次扣款的累计金额不允许超过此金额。
     *
     * @return self
     */
    public function setTotalAmount($totalAmount)
    {
        $this->container['totalAmount'] = $totalAmount;

        return $this;
    }

    /**
     * Gets totalPayments
     *
     * @return int|null
     */
    public function getTotalPayments()
    {
        return $this->container['totalPayments'];
    }

    /**
     * Sets totalPayments
     *
     * @param int|null $totalPayments 总扣款次数。如果传入此参数，则商户成功扣款的次数不能超过此次数限制（扣款失败不计入）。
     *
     * @return self
     */
    public function setTotalPayments($totalPayments)
    {
        $this->container['totalPayments'] = $totalPayments;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


