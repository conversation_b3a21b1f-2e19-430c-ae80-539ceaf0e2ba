# -*- coding: utf-8 -*-
"""
工具函数模块
版本: 1.0.0
创建时间: 2024-12-19
"""

import os
import json
import hashlib
import base64
import time
import logging
import requests
from pathlib import Path
from typing import Dict, Any, Optional, Union
from cryptography.fernet import Fernet
from datetime import datetime, timedelta

class Config:
    """配置管理类"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config_data = {}
        self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
            else:
                self.config_data = self.get_default_config()
                self.save_config()
            return self.config_data
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self.config_data = self.get_default_config()
            return self.config_data
    
    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        value = self.config_data
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> bool:
        """设置配置值"""
        keys = key.split('.')
        config = self.config_data
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
        return self.save_config()
    
    def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "app": {
                "name": "积分下载器",
                "version": "1.0.0"
            },
            "server": {
                "base_url": "https://mengmeng.kechengmao.top/",
                "api_base": "https://mengmeng.kechengmao.top/api/",
                "timeout": 30
            },
            "download": {
                "default_path": "downloads/",
                "max_concurrent": 3
            }
        }

class Logger:
    """日志管理类"""
    
    def __init__(self, name: str = "PointsDownloader", config: Optional[Config] = None):
        self.name = name
        self.config = config or Config()
        self.logger = None
        self.setup_logger()
    
    def setup_logger(self):
        """设置日志器"""
        self.logger = logging.getLogger(self.name)
        
        if self.logger.handlers:
            return
        
        # 设置日志级别
        level = self.config.get('logging.level', 'INFO')
        self.logger.setLevel(getattr(logging, level))
        
        # 创建日志目录
        log_file = self.config.get('logging.file', 'logs/app.log')
        log_dir = os.path.dirname(log_file)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)
        
        # 文件处理器
        if self.config.get('logging.enabled', True):
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_formatter = logging.Formatter(
                self.config.get('logging.format', 
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            )
            file_handler.setFormatter(file_formatter)
            self.logger.addHandler(file_handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter('%(levelname)s - %(message)s')
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
    
    def debug(self, message: str):
        """调试日志"""
        self.logger.debug(message)
    
    def info(self, message: str):
        """信息日志"""
        self.logger.info(message)
    
    def warning(self, message: str):
        """警告日志"""
        self.logger.warning(message)
    
    def error(self, message: str):
        """错误日志"""
        self.logger.error(message)
    
    def critical(self, message: str):
        """严重错误日志"""
        self.logger.critical(message)

class HttpClient:
    """HTTP客户端类"""
    
    def __init__(self, config: Optional[Config] = None):
        self.config = config or Config()
        self.session = requests.Session()
        self.setup_session()
    
    def setup_session(self):
        """设置会话"""
        # 设置超时
        self.timeout = self.config.get('server.timeout', 30)
        
        # 设置请求头
        headers = self.config.get('advanced.headers', {})
        user_agent = self.config.get('advanced.user_agent', 'PointsDownloader/1.0.0')
        headers['User-Agent'] = user_agent
        self.session.headers.update(headers)
        
        # 设置代理
        proxy_config = self.config.get('advanced.proxy', {})
        if proxy_config.get('enabled', False):
            proxy_url = f"{proxy_config['type']}://"
            if proxy_config.get('username'):
                proxy_url += f"{proxy_config['username']}:{proxy_config['password']}@"
            proxy_url += f"{proxy_config['host']}:{proxy_config['port']}"
            
            self.session.proxies = {
                'http': proxy_url,
                'https': proxy_url
            }
    
    def request(self, method: str, url: str, **kwargs) -> requests.Response:
        """发送HTTP请求"""
        kwargs.setdefault('timeout', self.timeout)
        kwargs.setdefault('verify', self.config.get('server.verify_ssl', True))
        
        retry_times = self.config.get('server.retry_times', 3)
        
        for attempt in range(retry_times):
            try:
                response = self.session.request(method, url, **kwargs)
                response.raise_for_status()
                return response
            except requests.exceptions.RequestException as e:
                if attempt == retry_times - 1:
                    raise e
                time.sleep(2 ** attempt)  # 指数退避
    
    def get(self, url: str, **kwargs) -> requests.Response:
        """GET请求"""
        return self.request('GET', url, **kwargs)
    
    def post(self, url: str, **kwargs) -> requests.Response:
        """POST请求"""
        return self.request('POST', url, **kwargs)
    
    def put(self, url: str, **kwargs) -> requests.Response:
        """PUT请求"""
        return self.request('PUT', url, **kwargs)
    
    def delete(self, url: str, **kwargs) -> requests.Response:
        """DELETE请求"""
        return self.request('DELETE', url, **kwargs)

class FileUtils:
    """文件工具类"""
    
    @staticmethod
    def ensure_dir(path: Union[str, Path]) -> bool:
        """确保目录存在"""
        try:
            Path(path).mkdir(parents=True, exist_ok=True)
            return True
        except Exception:
            return False
    
    @staticmethod
    def get_file_size(file_path: Union[str, Path]) -> int:
        """获取文件大小"""
        try:
            return Path(file_path).stat().st_size
        except Exception:
            return 0
    
    @staticmethod
    def get_file_hash(file_path: Union[str, Path], algorithm: str = 'md5') -> str:
        """获取文件哈希值"""
        try:
            hash_obj = hashlib.new(algorithm)
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_obj.update(chunk)
            return hash_obj.hexdigest()
        except Exception:
            return ""
    
    @staticmethod
    def format_size(size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"
    
    @staticmethod
    def safe_filename(filename: str) -> str:
        """生成安全的文件名"""
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        return filename.strip()

class Crypto:
    """加密工具类"""
    
    def __init__(self, key: Optional[bytes] = None):
        if key is None:
            key = Fernet.generate_key()
        self.cipher = Fernet(key)
        self.key = key
    
    def encrypt(self, data: str) -> str:
        """加密字符串"""
        encrypted = self.cipher.encrypt(data.encode('utf-8'))
        return base64.b64encode(encrypted).decode('utf-8')
    
    def decrypt(self, encrypted_data: str) -> str:
        """解密字符串"""
        encrypted = base64.b64decode(encrypted_data.encode('utf-8'))
        decrypted = self.cipher.decrypt(encrypted)
        return decrypted.decode('utf-8')
    
    @staticmethod
    def generate_key() -> bytes:
        """生成加密密钥"""
        return Fernet.generate_key()

class TimeUtils:
    """时间工具类"""
    
    @staticmethod
    def format_duration(seconds: int) -> str:
        """格式化时长"""
        if seconds < 60:
            return f"{seconds}秒"
        elif seconds < 3600:
            return f"{seconds // 60}分{seconds % 60}秒"
        else:
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            return f"{hours}小时{minutes}分"
    
    @staticmethod
    def format_datetime(dt: datetime) -> str:
        """格式化日期时间"""
        return dt.strftime('%Y-%m-%d %H:%M:%S')
    
    @staticmethod
    def parse_datetime(dt_str: str) -> datetime:
        """解析日期时间字符串"""
        try:
            return datetime.strptime(dt_str, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            try:
                return datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
            except ValueError:
                return datetime.now()

# 全局实例
config = Config()
logger = Logger(config=config)
http_client = HttpClient(config=config)

__all__ = [
    'Config', 'Logger', 'HttpClient', 'FileUtils', 
    'Crypto', 'TimeUtils', 'config', 'logger', 'http_client'
]
