# -*- coding: utf-8 -*-
"""
下载器基类模块
版本: 1.0.0
创建时间: 2024-12-19
"""

import os
import time
import threading
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Callable
from pathlib import Path
from .utils import config, logger, http_client, FileUtils
from .points import PointsManager

class DownloadProgress:
    """下载进度信息"""
    
    def __init__(self):
        self.total_size = 0
        self.downloaded_size = 0
        self.speed = 0
        self.eta = 0
        self.percentage = 0
        self.status = "准备中"
        self.start_time = time.time()
        self.last_update_time = time.time()
    
    def update(self, downloaded_size: int, total_size: int = None):
        """更新进度信息"""
        current_time = time.time()
        
        if total_size is not None:
            self.total_size = total_size
        
        # 计算下载速度
        time_diff = current_time - self.last_update_time
        if time_diff > 0:
            size_diff = downloaded_size - self.downloaded_size
            self.speed = size_diff / time_diff
        
        self.downloaded_size = downloaded_size
        self.last_update_time = current_time
        
        # 计算百分比
        if self.total_size > 0:
            self.percentage = (self.downloaded_size / self.total_size) * 100
        
        # 计算预计剩余时间
        if self.speed > 0 and self.total_size > 0:
            remaining_size = self.total_size - self.downloaded_size
            self.eta = remaining_size / self.speed
    
    def get_speed_str(self) -> str:
        """获取速度字符串"""
        return FileUtils.format_size(int(self.speed)) + "/s"
    
    def get_eta_str(self) -> str:
        """获取预计时间字符串"""
        if self.eta <= 0:
            return "未知"
        
        if self.eta < 60:
            return f"{int(self.eta)}秒"
        elif self.eta < 3600:
            return f"{int(self.eta // 60)}分{int(self.eta % 60)}秒"
        else:
            hours = int(self.eta // 3600)
            minutes = int((self.eta % 3600) // 60)
            return f"{hours}小时{minutes}分"

class BaseDownloader(ABC):
    """下载器基类"""
    
    def __init__(self, token: Optional[str] = None):
        self.token = token
        self.api_base = config.get('server.api_base')
        self.download_path = config.get('download.default_path', 'downloads/')
        self.chunk_size = config.get('download.chunk_size', 1024 * 1024)  # 1MB
        self.max_retries = config.get('download.retry_times', 3)
        self.retry_delay = config.get('download.retry_delay', 5)
        
        # 确保下载目录存在
        FileUtils.ensure_dir(self.download_path)
        
        # 积分管理器
        self.points_manager = PointsManager(token)
        
        # 下载状态
        self.is_downloading = False
        self.download_thread = None
        self.progress_callback = None
        self.cancel_flag = False
    
    def set_token(self, token: str):
        """设置用户令牌"""
        self.token = token
        self.points_manager.set_token(token)
    
    def set_progress_callback(self, callback: Callable[[DownloadProgress], None]):
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        headers = {'Content-Type': 'application/json'}
        if self.token:
            headers['Authorization'] = f'Bearer {self.token}'
        return headers
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """发送API请求"""
        if not self.token:
            raise Exception("用户未登录，请先登录")
        
        url = f"{self.api_base}{endpoint}"
        headers = self._get_headers()
        
        try:
            if method.upper() == 'GET':
                response = http_client.get(url, headers=headers, params=data)
            else:
                response = http_client.post(url, headers=headers, json=data)
            
            result = response.json()
            
            if not result.get('success', False):
                raise Exception(result.get('message', '请求失败'))
            
            return result
            
        except Exception as e:
            logger.error(f"下载器API请求失败: {e}")
            raise e
    
    def get_resources(self, platform: Optional[str] = None, page: int = 1, 
                     limit: int = 20, **kwargs) -> Dict[str, Any]:
        """获取资源列表"""
        try:
            params = {
                'page': page,
                'limit': limit
            }
            
            if platform:
                params['platform'] = platform
            
            # 添加其他查询参数
            params.update(kwargs)
            
            result = self._make_request('GET', 'resources/list.php', params)
            return result['data']
            
        except Exception as e:
            logger.error(f"获取资源列表失败: {e}")
            raise e
    
    def get_resource_info(self, resource_id: str) -> Dict[str, Any]:
        """获取资源详情"""
        try:
            params = {'resource_id': resource_id}
            result = self._make_request('GET', 'resources/info.php', params)
            return result['data']['resource']
            
        except Exception as e:
            logger.error(f"获取资源详情失败: {e}")
            raise e
    
    def request_download(self, resource_id: str) -> Dict[str, Any]:
        """请求下载链接"""
        try:
            data = {'resource_id': resource_id}
            result = self._make_request('POST', 'resources/download.php', data)
            return result['data']
            
        except Exception as e:
            logger.error(f"请求下载链接失败: {e}")
            raise e
    
    def download_file(self, download_url: str, file_path: str, 
                     total_size: Optional[int] = None) -> bool:
        """下载文件（支持断点续传）"""
        try:
            # 检查是否支持断点续传
            resume_support = config.get('download.resume_support', True)
            
            # 检查已下载的大小
            downloaded_size = 0
            if resume_support and os.path.exists(file_path):
                downloaded_size = os.path.getsize(file_path)
            
            # 设置请求头
            headers = {}
            if downloaded_size > 0:
                headers['Range'] = f'bytes={downloaded_size}-'
            
            # 创建进度对象
            progress = DownloadProgress()
            if total_size:
                progress.total_size = total_size
            progress.downloaded_size = downloaded_size
            progress.status = "下载中"
            
            # 发送下载请求
            response = http_client.get(download_url, headers=headers, stream=True)
            
            # 获取文件总大小
            if 'content-length' in response.headers:
                content_length = int(response.headers['content-length'])
                if downloaded_size == 0:
                    progress.total_size = content_length
                else:
                    progress.total_size = downloaded_size + content_length
            
            # 确保目录存在
            FileUtils.ensure_dir(os.path.dirname(file_path))
            
            # 打开文件（追加模式用于断点续传）
            mode = 'ab' if downloaded_size > 0 else 'wb'
            
            with open(file_path, mode) as f:
                for chunk in response.iter_content(chunk_size=self.chunk_size):
                    if self.cancel_flag:
                        progress.status = "已取消"
                        return False
                    
                    if chunk:
                        f.write(chunk)
                        progress.update(progress.downloaded_size + len(chunk))
                        
                        # 调用进度回调
                        if self.progress_callback:
                            self.progress_callback(progress)
            
            progress.status = "完成"
            if self.progress_callback:
                self.progress_callback(progress)
            
            logger.info(f"文件下载完成: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"文件下载失败: {e}")
            return False
    
    def download_resource(self, resource_id: str, save_path: Optional[str] = None) -> Dict[str, Any]:
        """下载资源"""
        try:
            # 获取资源信息
            resource_info = self.get_resource_info(resource_id)
            
            # 检查积分是否足够
            points_cost = resource_info.get('points_cost', 0)
            if points_cost > 0:
                current_balance = self.points_manager.get_current_balance()
                if current_balance < points_cost:
                    # 刷新余额再次检查
                    current_balance = self.points_manager.refresh_balance()
                    if current_balance < points_cost:
                        raise Exception(f"积分余额不足，当前余额: {current_balance}，需要: {points_cost}")
            
            # 请求下载链接
            download_info = self.request_download(resource_id)
            
            # 确定保存路径
            if not save_path:
                filename = FileUtils.safe_filename(resource_info.get('name', f'resource_{resource_id}'))
                save_path = os.path.join(self.download_path, filename)
            
            # 开始下载
            self.is_downloading = True
            self.cancel_flag = False
            
            success = self.download_file(
                download_info['download_url'],
                save_path,
                resource_info.get('file_size')
            )
            
            self.is_downloading = False
            
            if success:
                return {
                    'success': True,
                    'file_path': save_path,
                    'resource_info': resource_info,
                    'points_cost': points_cost,
                    'message': '下载成功'
                }
            else:
                # 下载失败，退还积分
                if points_cost > 0:
                    try:
                        self.points_manager.refund_points(points_cost, resource_id, "下载失败退还")
                    except Exception as refund_error:
                        logger.error(f"积分退还失败: {refund_error}")
                
                return {
                    'success': False,
                    'message': '下载失败'
                }
                
        except Exception as e:
            self.is_downloading = False
            logger.error(f"下载资源失败: {e}")
            
            # 如果是积分消费后的错误，尝试退还积分
            try:
                resource_info = self.get_resource_info(resource_id)
                points_cost = resource_info.get('points_cost', 0)
                if points_cost > 0:
                    self.points_manager.refund_points(points_cost, resource_id, "下载异常退还")
            except Exception:
                pass
            
            raise e
    
    def cancel_download(self):
        """取消下载"""
        self.cancel_flag = True
        logger.info("下载已取消")
    
    @abstractmethod
    def get_platform_name(self) -> str:
        """获取平台名称"""
        pass
    
    @abstractmethod
    def search_resources(self, keyword: str, **kwargs) -> List[Dict[str, Any]]:
        """搜索资源"""
        pass

__all__ = ['BaseDownloader', 'DownloadProgress']
