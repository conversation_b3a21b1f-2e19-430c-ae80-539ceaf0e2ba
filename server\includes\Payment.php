<?php
/**
 * 支付处理类 - 集成支付宝官方SDK
 * 版本: 2.0.0
 * 创建时间: 2024-12-19
 * 更新时间: 2024-12-19 - 集成支付宝官方SDK
 */

require_once 'database.php';
require_once __DIR__ . '/../vendor/alipay/v2/aop/AopClient.php';
require_once __DIR__ . '/../vendor/alipay/v2/aop/request/AlipayTradePrecreateRequest.php';
require_once __DIR__ . '/../vendor/alipay/v2/aop/request/AlipayTradeQueryRequest.php';

class Payment {
    private $db;
    private $userId;
    private $aopClient;

    public function __construct($userId = null) {
        $this->db = getDB();
        $this->userId = $userId;
        $this->initAlipayClient();
    }

    /**
     * 初始化支付宝客户端 - 严格按照官方SDK规范
     */
    private function initAlipayClient() {
        $this->aopClient = new AopClient();

        // 设置网关地址
        $this->aopClient->gatewayUrl = ALIPAY_GATEWAY;

        // 设置应用ID
        $this->aopClient->appId = ALIPAY_APP_ID;

        // 设置应用私钥 (PKCS8格式)
        $this->aopClient->rsaPrivateKey = ALIPAY_PRIVATE_KEY;

        // 设置支付宝公钥 (用于验签)
        $this->aopClient->alipayrsaPublicKey = ALIPAY_PUBLIC_KEY;

        // 设置签名类型
        $this->aopClient->signType = ALIPAY_SIGN_TYPE;

        // 设置字符集
        $this->aopClient->postCharset = ALIPAY_CHARSET;

        // 设置数据格式
        $this->aopClient->format = ALIPAY_FORMAT;

        // 设置API版本
        $this->aopClient->apiVersion = '1.0';

        // writeLog("支付宝客户端初始化完成: AppID=" . ALIPAY_APP_ID, 'INFO');
    }

    /**
     * 创建支付订单
     */
    public function createOrder($amount, $points = null) {
        if ($amount <= 0) {
            throw new Exception('支付金额必须大于0');
        }

        if (!$this->userId) {
            throw new Exception('用户未登录');
        }

        // 计算积分数量
        if ($points === null) {
            $pointsPerYuan = $this->getSystemConfig('points_per_yuan', DEFAULT_POINTS_PER_YUAN);
            $points = $amount * $pointsPerYuan;
        }

        try {
            $this->db->beginTransaction();

            // 生成订单号
            $orderNumber = $this->generateOrderNumber();

            // 创建支付宝订单
            $alipayResult = $this->createAlipayOrder($orderNumber, $amount, $points);

            // 保存订单到数据库
            $orderData = [
                'order_number' => $orderNumber,
                'user_id' => $this->userId,
                'amount' => $amount,
                'points' => $points,
                'status' => 'pending',
                'payment_method' => 'alipay',
                'qr_code' => $alipayResult['qr_code'],
                'ip_address' => getClientIP(),
                'expires_at' => date('Y-m-d H:i:s', time() + DEFAULT_ORDER_EXPIRE_MINUTES * 60)
            ];

            $orderId = $this->db->insertData('orders', $orderData);

            $this->db->commit();

            writeLog("支付订单创建成功: 订单号 $orderNumber, 金额 $amount 元", 'INFO');

            return [
                'success' => true,
                'order_id' => $orderId,
                'order_number' => $orderNumber,
                'amount' => $amount,
                'points' => $points,
                'qr_code' => $alipayResult['qr_code'],
                'expires_at' => $orderData['expires_at'],
                'message' => '订单创建成功'
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            writeLog("支付订单创建失败: " . $e->getMessage(), 'ERROR');
            throw $e;
        }
    }

    /**
     * 查询订单状态
     */
    public function queryOrderStatus($orderNumber) {
        try {
            // 检查是否有活跃事务，如果有则先回滚
            if ($this->db->inTransaction()) {
                writeLog("查询订单状态时检测到活跃事务，执行回滚", 'DEBUG');
                $this->db->rollback();
            }

            $order = $this->db->selectOne('orders', ['order_number' => $orderNumber]);

            if (!$order) {
                throw new Exception('订单不存在');
            }

            // 检查订单是否过期
            if ($order['status'] === 'pending' && strtotime($order['expires_at']) < time()) {
                $this->updateOrderStatus($orderNumber, 'expired');
                $order['status'] = 'expired';
            }

            // 如果订单仍在等待中，查询支付宝订单状态
            if ($order['status'] === 'pending') {
                $alipayStatus = $this->queryAlipayOrder($orderNumber);

                if ($alipayStatus['paid']) {
                    $this->processOrderPayment($orderNumber, $alipayStatus['trade_no']);
                    // 重新获取订单信息
                    $order = $this->db->selectOne('orders', ['order_number' => $orderNumber]);
                }
            }

            return [
                'order_number' => $order['order_number'],
                'amount' => $order['amount'],
                'points' => $order['points'],
                'status' => $order['status'],
                'trade_no' => $order['trade_no'],
                'created_at' => $order['created_at'],
                'expires_at' => $order['expires_at']
            ];

        } catch (Exception $e) {
            // 确保清理任何未完成的事务
            if ($this->db->inTransaction()) {
                $this->db->rollback();
            }
            writeLog("查询订单状态失败: " . $e->getMessage(), 'ERROR');
            throw $e;
        }
    }

    /**
     * 处理支付宝回调通知
     */
    public function handleAlipayNotify($notifyData) {
        try {
            // 验证支付宝签名
            if (!$this->verifyAlipaySign($notifyData)) {
                throw new Exception('签名验证失败');
            }

            $orderNumber = $notifyData['out_trade_no'];
            $tradeNo = $notifyData['trade_no'];
            $tradeStatus = $notifyData['trade_status'];

            // 查找订单
            $order = $this->db->selectOne('orders', ['order_number' => $orderNumber]);

            if (!$order) {
                throw new Exception('订单不存在');
            }

            // 验证金额
            if (abs($order['amount'] - floatval($notifyData['total_amount'])) > 0.01) {
                throw new Exception('订单金额不匹配');
            }

            // 处理支付成功
            if ($tradeStatus === 'TRADE_SUCCESS' || $tradeStatus === 'TRADE_FINISHED') {
                if ($order['status'] === 'pending') {
                    $this->processOrderPayment($orderNumber, $tradeNo, json_encode($notifyData));
                }
            }

            writeLog("支付宝回调处理成功: 订单号 $orderNumber", 'INFO');

            return ['success' => true];

        } catch (Exception $e) {
            writeLog("支付宝回调处理失败: " . $e->getMessage(), 'ERROR');
            throw $e;
        }
    }

    /**
     * 处理订单支付成功
     */
    private function processOrderPayment($orderNumber, $tradeNo, $notifyData = null) {
        try {
            // 检查是否已有活跃事务，如果有则先回滚
            if ($this->db->inTransaction()) {
                writeLog("检测到活跃事务，执行回滚后重新开始", 'DEBUG');
                $this->db->rollback();
            }

            $this->db->beginTransaction();

            // 获取订单信息
            $order = $this->db->selectOne('orders', ['order_number' => $orderNumber]);

            if (!$order) {
                throw new Exception('订单不存在');
            }

            // 检查订单状态，如果已经是completed，直接返回成功
            if ($order['status'] === 'completed') {
                $this->db->commit();
                writeLog("订单已经是完成状态，跳过处理: 订单号 $orderNumber", 'INFO');
                return;
            }

            if ($order['status'] !== 'pending') {
                throw new Exception('订单状态异常: ' . $order['status']);
            }

            // 更新订单状态为已完成
            $this->db->updateData('orders', [
                'status' => 'completed',
                'trade_no' => $tradeNo,
                'notify_data' => $notifyData,
                'paid_at' => date('Y-m-d H:i:s')
            ], ['order_number' => $orderNumber]);

            // 为用户充值积分
            $points = new Points($order['user_id']);
            $points->rechargePoints($order['points'], $orderNumber, "支付宝充值 - 订单号: $orderNumber");

            $this->db->commit();

            writeLog("订单支付处理成功: 订单号 $orderNumber, 充值 {$order['points']} 积分", 'INFO');

        } catch (Exception $e) {
            // 确保回滚事务
            if ($this->db->inTransaction()) {
                $this->db->rollback();
            }
            writeLog("订单支付处理失败: " . $e->getMessage(), 'ERROR');
            throw $e;
        }
    }

    /**
     * 更新订单状态
     */
    public function updateOrderStatus($orderNumber, $status) {
        return $this->db->updateData('orders', 
            ['status' => $status], 
            ['order_number' => $orderNumber]
        );
    }

    /**
     * 获取用户订单列表
     */
    public function getUserOrders($page = 1, $limit = 20, $status = null) {
        if (!$this->userId) {
            throw new Exception('用户未登录');
        }

        $offset = ($page - 1) * $limit;
        $conditions = ['user_id' => $this->userId];

        if ($status) {
            $conditions['status'] = $status;
        }

        // 获取总数
        $total = $this->db->count('orders', $conditions);

        // 获取订单列表
        $sql = "SELECT * FROM orders WHERE user_id = ?";
        $params = [$this->userId];

        if ($status) {
            $sql .= " AND status = ?";
            $params[] = $status;
        }

        $sql .= " ORDER BY created_at DESC LIMIT $limit OFFSET $offset";

        $orders = $this->db->fetchAll($sql, $params);

        return [
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit),
            'data' => $orders
        ];
    }

    /**
     * 创建支付宝当面付订单 - 官方SDK标准实现
     */
    private function createAlipayOrder($orderNumber, $amount, $points) {
        $subject = "积分充值";
        $body = "充值 {$amount} 元获得 {$points} 积分";

        writeLog("创建支付宝当面付订单: 订单号 $orderNumber, 金额 $amount 元", 'INFO');

        // 创建请求对象
        $request = new AlipayTradePrecreateRequest();

        // 设置异步通知地址
        $request->setNotifyUrl(SITE_URL . 'api/payment/notify.php');

        // 设置业务参数
        $bizContent = [
            'out_trade_no' => $orderNumber,                    // 商户订单号
            'total_amount' => number_format($amount, 2, '.', ''), // 订单总金额
            'subject' => $subject,                             // 订单标题
            'body' => $body,                                   // 订单描述
            'timeout_express' => '30m',                        // 订单超时时间
            'store_id' => 'POINTS_STORE_001'                   // 商户门店编号
        ];

        $request->setBizContent(json_encode($bizContent, JSON_UNESCAPED_UNICODE));

        writeLog("支付宝请求参数: " . json_encode($bizContent, JSON_UNESCAPED_UNICODE), 'DEBUG');

        // 执行请求
        $response = $this->aopClient->execute($request);

        // 解析响应
        $responseNode = str_replace(".", "_", $request->getApiMethodName()) . "_response";

        if (!isset($response->$responseNode)) {
            writeLog("支付宝响应格式错误: " . json_encode($response), 'ERROR');
            throw new Exception('支付宝API响应格式错误');
        }

        $result = $response->$responseNode;
        $resultCode = $result->code;

        if ($resultCode == '10000') {
            // 成功创建订单
            if (!isset($result->qr_code)) {
                writeLog("支付宝响应缺少二维码: " . json_encode($result), 'ERROR');
                throw new Exception('支付宝返回数据缺少二维码');
            }

            $qrCode = $result->qr_code;
            writeLog("支付宝当面付订单创建成功: 订单号 $orderNumber, 二维码长度 " . strlen($qrCode), 'INFO');

            return [
                'success' => true,
                'qr_code' => $qrCode,
                'order_number' => $orderNumber
            ];

        } else {
            // 创建订单失败
            $errorMsg = $result->msg ?? '未知错误';
            $subErrorMsg = $result->sub_msg ?? '';
            $fullErrorMsg = $errorMsg . ($subErrorMsg ? ' (' . $subErrorMsg . ')' : '');

            writeLog("支付宝当面付订单创建失败: 错误码 $resultCode, 错误信息 $fullErrorMsg", 'ERROR');
            throw new Exception("支付宝订单创建失败: $fullErrorMsg (错误码: $resultCode)");
        }
    }

    /**
     * 查询支付宝订单状态 - 官方SDK标准实现
     */
    private function queryAlipayOrder($orderNumber) {
        writeLog("查询支付宝订单状态: 订单号 $orderNumber", 'INFO');

        // 创建查询请求对象
        $request = new AlipayTradeQueryRequest();

        // 设置业务参数
        $bizContent = [
            'out_trade_no' => $orderNumber  // 商户订单号
        ];

        $request->setBizContent(json_encode($bizContent));

        writeLog("支付宝查询请求参数: " . json_encode($bizContent), 'DEBUG');

        // 执行请求
        $response = $this->aopClient->execute($request);

        // 解析响应
        $responseNode = str_replace(".", "_", $request->getApiMethodName()) . "_response";

        if (!isset($response->$responseNode)) {
            writeLog("支付宝查询响应格式错误: " . json_encode($response), 'ERROR');
            throw new Exception('支付宝查询API响应格式错误');
        }

        $result = $response->$responseNode;
        $resultCode = $result->code;

        if ($resultCode == '10000') {
            // 查询成功
            $tradeStatus = $result->trade_status ?? '';
            $tradeNo = $result->trade_no ?? null;
            $totalAmount = $result->total_amount ?? null;

            // 判断是否支付成功
            $paid = in_array($tradeStatus, ['TRADE_SUCCESS', 'TRADE_FINISHED']);

            writeLog("支付宝订单查询成功: 订单号 $orderNumber, 交易状态 $tradeStatus, 支付宝交易号 $tradeNo", 'INFO');

            return [
                'success' => true,
                'paid' => $paid,
                'trade_no' => $tradeNo,
                'trade_status' => $tradeStatus,
                'total_amount' => $totalAmount
            ];

        } else if ($resultCode == '40004') {
            // 订单不存在
            writeLog("支付宝订单不存在: 订单号 $orderNumber", 'WARNING');
            return [
                'success' => true,
                'paid' => false,
                'trade_no' => null,
                'trade_status' => 'NOT_EXIST'
            ];

        } else {
            // 查询失败
            $errorMsg = $result->msg ?? '未知错误';
            $subErrorMsg = $result->sub_msg ?? '';
            $fullErrorMsg = $errorMsg . ($subErrorMsg ? ' (' . $subErrorMsg . ')' : '');

            writeLog("支付宝订单查询失败: 订单号 $orderNumber, 错误码 $resultCode, 错误信息 $fullErrorMsg", 'ERROR');
            throw new Exception("支付宝订单查询失败: $fullErrorMsg (错误码: $resultCode)");
        }
    }



    /**
     * 验证支付宝签名 - 官方SDK标准实现
     */
    private function verifyAlipaySign($data) {
        writeLog("开始验证支付宝回调签名", 'DEBUG');

        // 检查必需的签名参数
        if (!isset($data['sign']) || !isset($data['sign_type'])) {
            writeLog("支付宝回调缺少签名信息: sign=" . (isset($data['sign']) ? '存在' : '缺失') .
                    ", sign_type=" . (isset($data['sign_type']) ? '存在' : '缺失'), 'ERROR');
            return false;
        }

        $sign = $data['sign'];
        $signType = $data['sign_type'];

        // 验证签名类型
        if ($signType !== 'RSA2') {
            writeLog("不支持的签名类型: $signType", 'ERROR');
            return false;
        }

        // 移除签名相关参数
        $dataToVerify = $data;
        unset($dataToVerify['sign'], $dataToVerify['sign_type']);

        // 按照支付宝规范构建待验证字符串
        ksort($dataToVerify);
        $stringToBeVerified = '';
        foreach ($dataToVerify as $key => $value) {
            if ($value !== '' && $value !== null) {
                $stringToBeVerified .= $key . '=' . $value . '&';
            }
        }
        $stringToBeVerified = rtrim($stringToBeVerified, '&');

        writeLog("支付宝回调验签字符串: " . $stringToBeVerified, 'DEBUG');

        // 构建支付宝公钥
        $publicKey = "-----BEGIN PUBLIC KEY-----\n" .
                    chunk_split(ALIPAY_PUBLIC_KEY, 64, "\n") .
                    "-----END PUBLIC KEY-----";

        // 执行RSA2验签
        $result = openssl_verify($stringToBeVerified, base64_decode($sign), $publicKey, OPENSSL_ALGO_SHA256);

        if ($result === 1) {
            writeLog("支付宝回调签名验证成功", 'INFO');
            return true;
        } else if ($result === 0) {
            writeLog("支付宝回调签名验证失败: 签名不匹配", 'ERROR');
            return false;
        } else {
            $error = openssl_error_string();
            writeLog("支付宝回调签名验证错误: " . $error, 'ERROR');
            return false;
        }
    }

    /**
     * 生成订单号
     */
    private function generateOrderNumber() {
        return 'PD' . date('YmdHis') . rand(1000, 9999);
    }

    /**
     * 获取系统配置
     */
    private function getSystemConfig($key, $default = null) {
        $config = $this->db->selectOne('system_config', ['config_key' => $key]);
        return $config ? $config['config_value'] : $default;
    }

    /**
     * 取消订单
     */
    public function cancelOrder($orderNumber) {
        if (!$this->userId) {
            throw new Exception('用户未登录');
        }

        $order = $this->db->selectOne('orders', [
            'order_number' => $orderNumber,
            'user_id' => $this->userId
        ]);

        if (!$order) {
            throw new Exception('订单不存在');
        }

        if ($order['status'] !== 'pending') {
            throw new Exception('只能取消待支付订单');
        }

        $affected = $this->db->updateData('orders', 
            ['status' => 'cancelled'], 
            ['order_number' => $orderNumber, 'user_id' => $this->userId]
        );

        if ($affected > 0) {
            writeLog("订单取消成功: 订单号 $orderNumber", 'INFO');
            return true;
        }

        return false;
    }
}

?>
