<?php
/**
 * 积分下载器系统配置文件
 * 版本: 1.0.0
 * 创建时间: 2024-12-19
 */

// 防止直接访问
if (!defined('SYSTEM_ROOT')) {
    define('SYSTEM_ROOT', dirname(__DIR__));
}

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', SYSTEM_ROOT . '/logs/php_errors.log');

// 时区设置
date_default_timezone_set('Asia/Shanghai');

// 数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'mengmeng_kecheng');
define('DB_USER', 'mengmeng_kecheng');
define('DB_PASS', '7ifTzCd3Q3JAEf67');
define('DB_CHARSET', 'utf8mb4');

// 系统配置
define('SITE_NAME', '积分下载器');
define('SITE_URL', 'https://mengmeng.kechengmao.top/');
define('API_VERSION', '1.0.0');

// 安全配置
define('JWT_SECRET', 'your_jwt_secret_key_here_change_in_production');
define('TOKEN_EXPIRE_HOURS', 24);
define('PASSWORD_SALT', 'your_password_salt_here');

// 支付宝配置 - 官方SDK标准配置
define('ALIPAY_APP_ID', '2021005150683259');
define('ALIPAY_GATEWAY', 'https://openapi.alipay.com/gateway.do');
define('ALIPAY_CHARSET', 'utf-8');
define('ALIPAY_SIGN_TYPE', 'RSA2');
define('ALIPAY_FORMAT', 'json');

// 支付宝公钥 (用于验证支付宝返回的数据)
define('ALIPAY_PUBLIC_KEY', 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAj+1qBI3J9UImYn+nJ8TnkL18xDOnbtEagDUFyalibHxvwKlDn2sFOCI/a78CYmLm6plkeEpNBSBXPry21cwR+IE2/Z4Sjfd0WvDrl0QeEXSOqZO5/6QJ4XDGo5thzTZ6ePcSJv6k3dLaqV0lltrMcrg1vYsEyk5Y2zG77SVBiPz71PhYTQHOmmWOpdWZoq8dlcAaQjK9vmB62Ym6H3pQvXcBgYWj5zmIo1THquAu6t1kPJBqcsrUAjbmNaA5+R+WlOg8VDUE0vbMThJ7H7uoTCDU41fW5KaxZ3UCEaVxTNGkWjGToZE4ufBYjoqKk3m++b7P+F9pmf2vEZqchs8J+wIDAQAB');

// 应用私钥 (RSA格式，用于签名请求数据)
// ⚠️ 重要：您需要提供真实的应用私钥，当前只有应用公钥
// 应用公钥 (这个不能用于签名): MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA00rqJeGoZB+LzI7EX9duSPLJ85ca9Ac4J/YjDDfgilZ+7/QNY1+7IeQFSlbJo4WZVhoky1JkpvnwbJlKfzyfFyhyJUL9mFtDcPlmy1kc9j3rnxXb+XAqwgvy/FWuVbDccTwq+lH5adnz0BEwMHdh54+dslyHzyWpAyaS0qtcol6FSmOsY0AG25SPd+G6P1dgjl3O5z4qxj/Z0J3c/7lxcbad/1kB6nGhlzFuB5JTmD8Hm5IIEFcSPpY+gSV4JeKM4zD3+xiku23ifiip/Ch84ohQudnx4UHIPckUumtVM1nw/lDV1Lj6GAC87o03kuDgN8BiGF45QELrQJCrm64QYQIDAQAB
define('ALIPAY_PRIVATE_KEY', 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDTSuol4ahkH4vMjsRf125I8snzlxr0Bzgn9iMMN+CKVn7v9A1jX7sh5AVKVsmjhZlWGiTLUmSm+fBsmUp/PJ8XKHI1Qv2YW0Nw+WbLWRz2PeufFdv5cCrCC/L8Va5VsNxxPCr6Uflp2fPQETAwd2Hnj52yXIfPJakDJpLSq1yiXoVKY6xjQAbblI934bo/V2COXc7nPirGP9nQndz/uXFxtp3/WQHqcaGXMW4HklOYPwebkggQVxI+lj6BJXgl4ozjMPf7GKS7beJ+KKn8KHziiFC52fHhQcg9yRS6a1UzWfD+UNXUuPoYALzujTeS4OA3wGIYXjlAQutAkKubrhBhAgMBAAECggEBAMoey8XZC6BbnPvdRnT4PAOEXTOrxJjTnyB7ECPL//vVqjAYIgaBuR0+ZuEqDFFkp7353GCzPJ2OUjNQoDb+4qDgi//vSW/JPcaAjZjQcigMK61O19LRPdXKYHKJ8+789KNNjz9N020ylUzgZFy4lutQMuZAJZo+yGK/L4xfaInYbGgaRR7xpsvYS4ODDsjO2LuOXsDhF+t2eSu35G1ksQQCTaKEdjhrGW5Or/9vypwhdAYCspkpMhFj6r2cenWytZDhxn4/Dc1RQCJv1rnqXkL0LCw5lHWp7dOIP3+I5D1etv+xSn+ADCDFRQsY16VHNa/ILl8g5PXX/6l05vh3/pECgYEA6hormzpuGdc6gDzcM3CfY1eB0HmyFFJPNfifaFoALZ+R7WalfJ//6vtL9gRxwrqaOUF3psASUprvu2NhFmcE5YEVOZWWJp28MIgwy+nR4dmTG27BzFzYMxx18SxQLzw0oWnjeNEgOtA0yS/WgtjPxFM/7m3+rA25rUWd5eB3Jf8CgYEA5w6LSGls4LH3u4U/kU+JcqDaapfzUG+JU3S+zVnhlVLjqH3XAtQ278m2Vhgy3iQ0pB1pasFMqEV2Ja46BIkwKLMkpfHX96Nbj5ywCvuD9sKlqoDTUCKg3+aDuDn4DD8qdFqOJxfGXnueQk8lX2xGBLgQ7pjAhmyMx8hkkYGoiZ8CgYAVWpHDtQ65+LHzZJnJb2p6i07iD1e5FtD24VjZEeyWPMn417YcqhOUAZwqMrmVw6OxuVEKutZxBoT7mNMQUhFWRfIDsKtjllvGKYYZ85gbe9c5V18CHy3xa5UujJY72MgMOTZwnNLNypQmDeEJSnGZPObF/u6ODypyrbj7cQAtTQKBgGwvtSu8oUw8SEC3TWJemX7griRG7Zh2ARjgiw5fzW47l3knIuuiLe7sxcFeJ0M4NKW7V8ayp5AKph1SjLve+Hu2FazVpke/Z3nA5fTee5Wo4iUJmOfmrujnaDl0ex5Q3TIJzJGlYi6PNzTZFiit68L95H+zakIGxYwt2lj/GskFAoGABEQsaPaprDK4Qvv8PrTblADWrCI5xJJpzKUHsLPrsBUkDP8gdWnQuAQrk+Ale/yT6GvJYIq+yHGexmSPmYABudzCO8vv+lBiup/Bymj+KrQgRAa3+YLaNKMKRO76fs6hs644c/nq34ln8dthyb5qQkdLhmlDbDWubhi9RBcA/tU=');

// 文件上传配置
define('UPLOAD_MAX_SIZE', 100 * 1024 * 1024); // 100MB
define('UPLOAD_ALLOWED_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'zip', 'rar']);

// 积分系统配置
define('DEFAULT_POINTS_PER_YUAN', 1);
define('DEFAULT_REGISTER_GIFT_POINTS', 10);
define('DEFAULT_ORDER_EXPIRE_MINUTES', 30);
define('DEFAULT_MAX_DOWNLOAD_CONCURRENT', 3);

// 日志配置
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('LOG_MAX_FILES', 30);

// API响应配置
define('API_SUCCESS_CODE', 200);
define('API_ERROR_CODE', 400);
define('API_AUTH_ERROR_CODE', 401);
define('API_FORBIDDEN_CODE', 403);
define('API_NOT_FOUND_CODE', 404);
define('API_SERVER_ERROR_CODE', 500);

// CORS配置
define('CORS_ALLOWED_ORIGINS', ['*']);
define('CORS_ALLOWED_METHODS', ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']);
define('CORS_ALLOWED_HEADERS', ['Content-Type', 'Authorization', 'X-Requested-With']);

// 缓存配置
define('CACHE_ENABLED', true);
define('CACHE_EXPIRE_TIME', 3600); // 1小时

// 调试模式
define('DEBUG_MODE', false);

// 自动加载函数
function autoload($className) {
    $file = SYSTEM_ROOT . '/includes/' . $className . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
}

spl_autoload_register('autoload');

// 通用响应函数
function jsonResponse($data, $code = API_SUCCESS_CODE, $message = '') {
    http_response_code($code);
    header('Content-Type: application/json; charset=utf-8');
    
    // 设置CORS头
    if (in_array('*', CORS_ALLOWED_ORIGINS) || 
        (isset($_SERVER['HTTP_ORIGIN']) && in_array($_SERVER['HTTP_ORIGIN'], CORS_ALLOWED_ORIGINS))) {
        header('Access-Control-Allow-Origin: ' . (isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '*'));
    }
    header('Access-Control-Allow-Methods: ' . implode(', ', CORS_ALLOWED_METHODS));
    header('Access-Control-Allow-Headers: ' . implode(', ', CORS_ALLOWED_HEADERS));
    header('Access-Control-Allow-Credentials: true');
    
    $response = [
        'success' => $code == API_SUCCESS_CODE,
        'code' => $code,
        'message' => $message,
        'data' => $data,
        'timestamp' => time()
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

// 错误处理函数
function handleError($message, $code = API_ERROR_CODE, $data = null) {
    if (DEBUG_MODE) {
        error_log("API Error: $message");
    }
    jsonResponse($data, $code, $message);
}

// 验证请求方法
function validateRequestMethod($allowedMethods) {
    $method = $_SERVER['REQUEST_METHOD'];
    if (!in_array($method, $allowedMethods)) {
        handleError('不支持的请求方法', API_ERROR_CODE);
    }
}

// 获取请求数据
function getRequestData() {
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'GET') {
        return $_GET;
    } elseif ($method === 'POST') {
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
        if (strpos($contentType, 'application/json') !== false) {
            $input = file_get_contents('php://input');
            return json_decode($input, true) ?? [];
        } else {
            return $_POST;
        }
    }
    
    return [];
}

// 获取客户端IP
function getClientIP() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

// 生成随机字符串
function generateRandomString($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

// 记录日志
function writeLog($message, $level = 'INFO') {
    $logFile = SYSTEM_ROOT . '/logs/system.log';
    $logDir = dirname($logFile);

    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }

    $timestamp = date('Y-m-d H:i:s');
    $ip = getClientIP();
    $logMessage = "[$timestamp] [$level] [$ip] $message" . PHP_EOL;

    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

// 检查IP是否在CIDR范围内
function ipInRange($ip, $cidr) {
    list($subnet, $mask) = explode('/', $cidr);

    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
        // IPv4
        $ip = ip2long($ip);
        $subnet = ip2long($subnet);
        $mask = -1 << (32 - $mask);
        $subnet &= $mask;
        return ($ip & $mask) == $subnet;
    } elseif (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
        // IPv6 (简化实现)
        return false; // 暂不支持IPv6 CIDR
    }

    return false;
}

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: ' . implode(', ', CORS_ALLOWED_METHODS));
    header('Access-Control-Allow-Headers: ' . implode(', ', CORS_ALLOWED_HEADERS));
    exit;
}

?>
