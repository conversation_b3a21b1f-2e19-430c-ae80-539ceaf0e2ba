<?php
/**
 * 用户登录API
 * 版本: 1.0.0
 * 创建时间: 2024-12-19
 */

require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/User.php';
require_once '../../includes/Points.php';

// 验证请求方法
validateRequestMethod(['POST']);

try {
    // 获取请求数据
    $data = getRequestData();
    
    // 验证必需参数
    if (empty($data['username'])) {
        handleError('用户名不能为空', API_ERROR_CODE);
    }
    
    if (empty($data['password'])) {
        handleError('密码不能为空', API_ERROR_CODE);
    }
    
    // 创建用户实例并登录
    $user = new User();
    $result = $user->login($data['username'], $data['password']);
    
    // 获取用户积分信息
    $points = new Points($result['user_id']);
    $pointsInfo = $points->getAccountInfo();
    
    // 记录登录日志
    writeLog("用户登录成功: {$data['username']} (ID: {$result['user_id']})", 'INFO');
    
    // 返回成功响应
    jsonResponse([
        'token' => $result['token'],
        'user_id' => $result['user_id'],
        'username' => $result['username'],
        'email' => $result['email'],
        'expires_at' => $result['expires_at'],
        'points' => $pointsInfo
    ], API_SUCCESS_CODE, '登录成功');
    
} catch (Exception $e) {
    // 记录错误日志
    writeLog("用户登录失败: " . $e->getMessage(), 'ERROR');
    
    // 返回错误响应
    handleError($e->getMessage(), API_AUTH_ERROR_CODE);
}

?>
