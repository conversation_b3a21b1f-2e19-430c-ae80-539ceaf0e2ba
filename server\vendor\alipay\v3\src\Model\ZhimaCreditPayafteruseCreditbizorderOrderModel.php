<?php
/**
 * ZhimaCreditPayafteruseCreditbizorderOrderModel
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * ZhimaCreditPayafteruseCreditbizorderOrderModel Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class ZhimaCreditPayafteruseCreditbizorderOrderModel implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'ZhimaCreditPayafteruseCreditbizorderOrderModel';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'amountType' => 'string',
        'body' => 'string',
        'categoryId' => 'string',
        'commercialSubMode' => 'string',
        'creditAgreementId' => 'string',
        'creditCommercialMode' => 'string',
        'extendParams' => 'string',
        'orderAmount' => 'string',
        'outOrderNo' => 'string',
        'paymentTotalTimes' => 'string',
        'productCode' => 'string',
        'stagePeriodType' => 'string',
        'subject' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'amountType' => null,
        'body' => null,
        'categoryId' => null,
        'commercialSubMode' => null,
        'creditAgreementId' => null,
        'creditCommercialMode' => null,
        'extendParams' => null,
        'orderAmount' => null,
        'outOrderNo' => null,
        'paymentTotalTimes' => null,
        'productCode' => null,
        'stagePeriodType' => null,
        'subject' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'amountType' => 'amount_type',
        'body' => 'body',
        'categoryId' => 'category_id',
        'commercialSubMode' => 'commercial_sub_mode',
        'creditAgreementId' => 'credit_agreement_id',
        'creditCommercialMode' => 'credit_commercial_mode',
        'extendParams' => 'extend_params',
        'orderAmount' => 'order_amount',
        'outOrderNo' => 'out_order_no',
        'paymentTotalTimes' => 'payment_total_times',
        'productCode' => 'product_code',
        'stagePeriodType' => 'stage_period_type',
        'subject' => 'subject'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'amountType' => 'setAmountType',
        'body' => 'setBody',
        'categoryId' => 'setCategoryId',
        'commercialSubMode' => 'setCommercialSubMode',
        'creditAgreementId' => 'setCreditAgreementId',
        'creditCommercialMode' => 'setCreditCommercialMode',
        'extendParams' => 'setExtendParams',
        'orderAmount' => 'setOrderAmount',
        'outOrderNo' => 'setOutOrderNo',
        'paymentTotalTimes' => 'setPaymentTotalTimes',
        'productCode' => 'setProductCode',
        'stagePeriodType' => 'setStagePeriodType',
        'subject' => 'setSubject'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'amountType' => 'getAmountType',
        'body' => 'getBody',
        'categoryId' => 'getCategoryId',
        'commercialSubMode' => 'getCommercialSubMode',
        'creditAgreementId' => 'getCreditAgreementId',
        'creditCommercialMode' => 'getCreditCommercialMode',
        'extendParams' => 'getExtendParams',
        'orderAmount' => 'getOrderAmount',
        'outOrderNo' => 'getOutOrderNo',
        'paymentTotalTimes' => 'getPaymentTotalTimes',
        'productCode' => 'getProductCode',
        'stagePeriodType' => 'getStagePeriodType',
        'subject' => 'getSubject'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['amountType'] = $data['amountType'] ?? null;
        $this->container['body'] = $data['body'] ?? null;
        $this->container['categoryId'] = $data['categoryId'] ?? null;
        $this->container['commercialSubMode'] = $data['commercialSubMode'] ?? null;
        $this->container['creditAgreementId'] = $data['creditAgreementId'] ?? null;
        $this->container['creditCommercialMode'] = $data['creditCommercialMode'] ?? null;
        $this->container['extendParams'] = $data['extendParams'] ?? null;
        $this->container['orderAmount'] = $data['orderAmount'] ?? null;
        $this->container['outOrderNo'] = $data['outOrderNo'] ?? null;
        $this->container['paymentTotalTimes'] = $data['paymentTotalTimes'] ?? null;
        $this->container['productCode'] = $data['productCode'] ?? null;
        $this->container['stagePeriodType'] = $data['stagePeriodType'] ?? null;
        $this->container['subject'] = $data['subject'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets amountType
     *
     * @return string|null
     */
    public function getAmountType()
    {
        return $this->container['amountType'];
    }

    /**
     * Sets amountType
     *
     * @param string|null $amountType 只有当传递了order_amount时，该参数才有意义； 1）该参数不传时，默认为ORDER_AMOUNT。 2）传ORDER_AMOUNT时，表示order_amount传入的金额为后付金额，在发起扣款时，最大扣款支付金额为order_amount传入的值（取值单位为元）； 3）传RISK_AMOUNT时，表示order_amount传入的金额为风险预估金额，在发起扣款时，最大扣款支付金额为商户签约时约定的上限额度（取值单位为元）。
     *
     * @return self
     */
    public function setAmountType($amountType)
    {
        $this->container['amountType'] = $amountType;

        return $this;
    }

    /**
     * Gets body
     *
     * @return string|null
     */
    public function getBody()
    {
        return $this->container['body'];
    }

    /**
     * Sets body
     *
     * @param string|null $body 订单描述
     *
     * @return self
     */
    public function setBody($body)
    {
        $this->container['body'] = $body;

        return $this;
    }

    /**
     * Gets categoryId
     *
     * @return string|null
     */
    public function getCategoryId()
    {
        return $this->container['categoryId'];
    }

    /**
     * Sets categoryId
     *
     * @param string|null $categoryId 芝麻外部类目，芝麻先享接入无差异化风控诉求可不传
     *
     * @return self
     */
    public function setCategoryId($categoryId)
    {
        $this->container['categoryId'] = $categoryId;

        return $this;
    }

    /**
     * Gets commercialSubMode
     *
     * @return string|null
     */
    public function getCommercialSubMode()
    {
        return $this->container['commercialSubMode'];
    }

    /**
     * Sets commercialSubMode
     *
     * @param string|null $commercialSubMode 业务子模式。默认的单次付模式无需传入，阶段付模式传入以区分是分次还是分期子模式。
     *
     * @return self
     */
    public function setCommercialSubMode($commercialSubMode)
    {
        $this->container['commercialSubMode'] = $commercialSubMode;

        return $this;
    }

    /**
     * Gets creditAgreementId
     *
     * @return string|null
     */
    public function getCreditAgreementId()
    {
        return $this->container['creditAgreementId'];
    }

    /**
     * Sets creditAgreementId
     *
     * @param string|null $creditAgreementId 芝麻开通协议号
     *
     * @return self
     */
    public function setCreditAgreementId($creditAgreementId)
    {
        $this->container['creditAgreementId'] = $creditAgreementId;

        return $this;
    }

    /**
     * Gets creditCommercialMode
     *
     * @return string|null
     */
    public function getCreditCommercialMode()
    {
        return $this->container['creditCommercialMode'];
    }

    /**
     * Sets creditCommercialMode
     *
     * @param string|null $creditCommercialMode 信用业务模式，不填默认为单次扣款模式。阶段付模式为STAGE_PAYMENT，其它模式请根据对应的技术支持文档传入
     *
     * @return self
     */
    public function setCreditCommercialMode($creditCommercialMode)
    {
        $this->container['creditCommercialMode'] = $creditCommercialMode;

        return $this;
    }

    /**
     * Gets extendParams
     *
     * @return string|null
     */
    public function getExtendParams()
    {
        return $this->container['extendParams'];
    }

    /**
     * Sets extendParams
     *
     * @param string|null $extendParams 业务扩展参数
     *
     * @return self
     */
    public function setExtendParams($extendParams)
    {
        $this->container['extendParams'] = $extendParams;

        return $this;
    }

    /**
     * Gets orderAmount
     *
     * @return string|null
     */
    public function getOrderAmount()
    {
        return $this->container['orderAmount'];
    }

    /**
     * Sets orderAmount
     *
     * @param string|null $orderAmount 订单金额，该金额为当前订单扣款的累计最大额度。例如，下单时传递10.00，则扣款时最大支付金额为10元。若该参数不传，则默认使用商户签约时约定的上限额度。芝麻速办业务场景（极速回收、极速返利、极速退款等）商户请求时，order_amount必传，且amount_type类型需传递ORDER_AMOUNT。
     *
     * @return self
     */
    public function setOrderAmount($orderAmount)
    {
        $this->container['orderAmount'] = $orderAmount;

        return $this;
    }

    /**
     * Gets outOrderNo
     *
     * @return string|null
     */
    public function getOutOrderNo()
    {
        return $this->container['outOrderNo'];
    }

    /**
     * Sets outOrderNo
     *
     * @param string|null $outOrderNo 商户外部订单号，保证不重复
     *
     * @return self
     */
    public function setOutOrderNo($outOrderNo)
    {
        $this->container['outOrderNo'] = $outOrderNo;

        return $this;
    }

    /**
     * Gets paymentTotalTimes
     *
     * @return string|null
     */
    public function getPaymentTotalTimes()
    {
        return $this->container['paymentTotalTimes'];
    }

    /**
     * Sets paymentTotalTimes
     *
     * @param string|null $paymentTotalTimes 多阶段订单次数，业务模式为阶段付模式下时需传入
     *
     * @return self
     */
    public function setPaymentTotalTimes($paymentTotalTimes)
    {
        $this->container['paymentTotalTimes'] = $paymentTotalTimes;

        return $this;
    }

    /**
     * Gets productCode
     *
     * @return string|null
     */
    public function getProductCode()
    {
        return $this->container['productCode'];
    }

    /**
     * Sets productCode
     *
     * @param string|null $productCode 产品码，不传默认为CREDIT_PAY_AFTER_USE
     *
     * @return self
     */
    public function setProductCode($productCode)
    {
        $this->container['productCode'] = $productCode;

        return $this;
    }

    /**
     * Gets stagePeriodType
     *
     * @return string|null
     */
    public function getStagePeriodType()
    {
        return $this->container['stagePeriodType'];
    }

    /**
     * Sets stagePeriodType
     *
     * @param string|null $stagePeriodType 阶段付分期类型。阶段付模式，且子业务模式为分期模式下需要传入，分次不需要
     *
     * @return self
     */
    public function setStagePeriodType($stagePeriodType)
    {
        $this->container['stagePeriodType'] = $stagePeriodType;

        return $this;
    }

    /**
     * Gets subject
     *
     * @return string|null
     */
    public function getSubject()
    {
        return $this->container['subject'];
    }

    /**
     * Sets subject
     *
     * @param string|null $subject 订单标题。 注意：不可使用特殊字符，如 /，=，& 等。
     *
     * @return self
     */
    public function setSubject($subject)
    {
        $this->container['subject'] = $subject;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


