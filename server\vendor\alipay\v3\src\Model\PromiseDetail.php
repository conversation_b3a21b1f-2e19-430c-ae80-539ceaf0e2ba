<?php
/**
 * PromiseDetail
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * PromiseDetail Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class PromiseDetail implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'PromiseDetail';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'authStatus' => 'bool',
        'createTime' => 'string',
        'endTime' => 'string',
        'finalTime' => 'string',
        'finishPeriods' => 'int',
        'merchantId' => 'string',
        'merchantLogo' => 'string',
        'merchantName' => 'string',
        'outBizNo' => 'string',
        'periodType' => 'string',
        'promiseName' => 'string',
        'recordId' => 'string',
        'recordStatus' => 'string',
        'startTime' => 'string',
        'subRecordStatus' => 'string',
        'subTitle' => 'string',
        'templateId' => 'string',
        'totalPeriods' => 'int'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'authStatus' => null,
        'createTime' => null,
        'endTime' => null,
        'finalTime' => null,
        'finishPeriods' => null,
        'merchantId' => null,
        'merchantLogo' => null,
        'merchantName' => null,
        'outBizNo' => null,
        'periodType' => null,
        'promiseName' => null,
        'recordId' => null,
        'recordStatus' => null,
        'startTime' => null,
        'subRecordStatus' => null,
        'subTitle' => null,
        'templateId' => null,
        'totalPeriods' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'authStatus' => 'auth_status',
        'createTime' => 'create_time',
        'endTime' => 'end_time',
        'finalTime' => 'final_time',
        'finishPeriods' => 'finish_periods',
        'merchantId' => 'merchant_id',
        'merchantLogo' => 'merchant_logo',
        'merchantName' => 'merchant_name',
        'outBizNo' => 'out_biz_no',
        'periodType' => 'period_type',
        'promiseName' => 'promise_name',
        'recordId' => 'record_id',
        'recordStatus' => 'record_status',
        'startTime' => 'start_time',
        'subRecordStatus' => 'sub_record_status',
        'subTitle' => 'sub_title',
        'templateId' => 'template_id',
        'totalPeriods' => 'total_periods'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'authStatus' => 'setAuthStatus',
        'createTime' => 'setCreateTime',
        'endTime' => 'setEndTime',
        'finalTime' => 'setFinalTime',
        'finishPeriods' => 'setFinishPeriods',
        'merchantId' => 'setMerchantId',
        'merchantLogo' => 'setMerchantLogo',
        'merchantName' => 'setMerchantName',
        'outBizNo' => 'setOutBizNo',
        'periodType' => 'setPeriodType',
        'promiseName' => 'setPromiseName',
        'recordId' => 'setRecordId',
        'recordStatus' => 'setRecordStatus',
        'startTime' => 'setStartTime',
        'subRecordStatus' => 'setSubRecordStatus',
        'subTitle' => 'setSubTitle',
        'templateId' => 'setTemplateId',
        'totalPeriods' => 'setTotalPeriods'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'authStatus' => 'getAuthStatus',
        'createTime' => 'getCreateTime',
        'endTime' => 'getEndTime',
        'finalTime' => 'getFinalTime',
        'finishPeriods' => 'getFinishPeriods',
        'merchantId' => 'getMerchantId',
        'merchantLogo' => 'getMerchantLogo',
        'merchantName' => 'getMerchantName',
        'outBizNo' => 'getOutBizNo',
        'periodType' => 'getPeriodType',
        'promiseName' => 'getPromiseName',
        'recordId' => 'getRecordId',
        'recordStatus' => 'getRecordStatus',
        'startTime' => 'getStartTime',
        'subRecordStatus' => 'getSubRecordStatus',
        'subTitle' => 'getSubTitle',
        'templateId' => 'getTemplateId',
        'totalPeriods' => 'getTotalPeriods'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['authStatus'] = $data['authStatus'] ?? null;
        $this->container['createTime'] = $data['createTime'] ?? null;
        $this->container['endTime'] = $data['endTime'] ?? null;
        $this->container['finalTime'] = $data['finalTime'] ?? null;
        $this->container['finishPeriods'] = $data['finishPeriods'] ?? null;
        $this->container['merchantId'] = $data['merchantId'] ?? null;
        $this->container['merchantLogo'] = $data['merchantLogo'] ?? null;
        $this->container['merchantName'] = $data['merchantName'] ?? null;
        $this->container['outBizNo'] = $data['outBizNo'] ?? null;
        $this->container['periodType'] = $data['periodType'] ?? null;
        $this->container['promiseName'] = $data['promiseName'] ?? null;
        $this->container['recordId'] = $data['recordId'] ?? null;
        $this->container['recordStatus'] = $data['recordStatus'] ?? null;
        $this->container['startTime'] = $data['startTime'] ?? null;
        $this->container['subRecordStatus'] = $data['subRecordStatus'] ?? null;
        $this->container['subTitle'] = $data['subTitle'] ?? null;
        $this->container['templateId'] = $data['templateId'] ?? null;
        $this->container['totalPeriods'] = $data['totalPeriods'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets authStatus
     *
     * @return bool|null
     */
    public function getAuthStatus()
    {
        return $this->container['authStatus'];
    }

    /**
     * Sets authStatus
     *
     * @param bool|null $authStatus 授权状态
     *
     * @return self
     */
    public function setAuthStatus($authStatus)
    {
        $this->container['authStatus'] = $authStatus;

        return $this;
    }

    /**
     * Gets createTime
     *
     * @return string|null
     */
    public function getCreateTime()
    {
        return $this->container['createTime'];
    }

    /**
     * Sets createTime
     *
     * @param string|null $createTime 任务创建时间
     *
     * @return self
     */
    public function setCreateTime($createTime)
    {
        $this->container['createTime'] = $createTime;

        return $this;
    }

    /**
     * Gets endTime
     *
     * @return string|null
     */
    public function getEndTime()
    {
        return $this->container['endTime'];
    }

    /**
     * Sets endTime
     *
     * @param string|null $endTime 任务结束时间
     *
     * @return self
     */
    public function setEndTime($endTime)
    {
        $this->container['endTime'] = $endTime;

        return $this;
    }

    /**
     * Gets finalTime
     *
     * @return string|null
     */
    public function getFinalTime()
    {
        return $this->container['finalTime'];
    }

    /**
     * Sets finalTime
     *
     * @param string|null $finalTime 任务到达终态的时间
     *
     * @return self
     */
    public function setFinalTime($finalTime)
    {
        $this->container['finalTime'] = $finalTime;

        return $this;
    }

    /**
     * Gets finishPeriods
     *
     * @return int|null
     */
    public function getFinishPeriods()
    {
        return $this->container['finishPeriods'];
    }

    /**
     * Sets finishPeriods
     *
     * @param int|null $finishPeriods 任务完成期数
     *
     * @return self
     */
    public function setFinishPeriods($finishPeriods)
    {
        $this->container['finishPeriods'] = $finishPeriods;

        return $this;
    }

    /**
     * Gets merchantId
     *
     * @return string|null
     */
    public function getMerchantId()
    {
        return $this->container['merchantId'];
    }

    /**
     * Sets merchantId
     *
     * @param string|null $merchantId 芝麻侧的商户id
     *
     * @return self
     */
    public function setMerchantId($merchantId)
    {
        $this->container['merchantId'] = $merchantId;

        return $this;
    }

    /**
     * Gets merchantLogo
     *
     * @return string|null
     */
    public function getMerchantLogo()
    {
        return $this->container['merchantLogo'];
    }

    /**
     * Sets merchantLogo
     *
     * @param string|null $merchantLogo 商户logo
     *
     * @return self
     */
    public function setMerchantLogo($merchantLogo)
    {
        $this->container['merchantLogo'] = $merchantLogo;

        return $this;
    }

    /**
     * Gets merchantName
     *
     * @return string|null
     */
    public function getMerchantName()
    {
        return $this->container['merchantName'];
    }

    /**
     * Sets merchantName
     *
     * @param string|null $merchantName 商户名称
     *
     * @return self
     */
    public function setMerchantName($merchantName)
    {
        $this->container['merchantName'] = $merchantName;

        return $this;
    }

    /**
     * Gets outBizNo
     *
     * @return string|null
     */
    public function getOutBizNo()
    {
        return $this->container['outBizNo'];
    }

    /**
     * Sets outBizNo
     *
     * @param string|null $outBizNo 生活记录加入时的外部业务号
     *
     * @return self
     */
    public function setOutBizNo($outBizNo)
    {
        $this->container['outBizNo'] = $outBizNo;

        return $this;
    }

    /**
     * Gets periodType
     *
     * @return string|null
     */
    public function getPeriodType()
    {
        return $this->container['periodType'];
    }

    /**
     * Sets periodType
     *
     * @param string|null $periodType 周期类型
     *
     * @return self
     */
    public function setPeriodType($periodType)
    {
        $this->container['periodType'] = $periodType;

        return $this;
    }

    /**
     * Gets promiseName
     *
     * @return string|null
     */
    public function getPromiseName()
    {
        return $this->container['promiseName'];
    }

    /**
     * Sets promiseName
     *
     * @param string|null $promiseName 生活记录模板名称
     *
     * @return self
     */
    public function setPromiseName($promiseName)
    {
        $this->container['promiseName'] = $promiseName;

        return $this;
    }

    /**
     * Gets recordId
     *
     * @return string|null
     */
    public function getRecordId()
    {
        return $this->container['recordId'];
    }

    /**
     * Sets recordId
     *
     * @param string|null $recordId 生活记录主记录id
     *
     * @return self
     */
    public function setRecordId($recordId)
    {
        $this->container['recordId'] = $recordId;

        return $this;
    }

    /**
     * Gets recordStatus
     *
     * @return string|null
     */
    public function getRecordStatus()
    {
        return $this->container['recordStatus'];
    }

    /**
     * Sets recordStatus
     *
     * @param string|null $recordStatus 主任务状态
     *
     * @return self
     */
    public function setRecordStatus($recordStatus)
    {
        $this->container['recordStatus'] = $recordStatus;

        return $this;
    }

    /**
     * Gets startTime
     *
     * @return string|null
     */
    public function getStartTime()
    {
        return $this->container['startTime'];
    }

    /**
     * Sets startTime
     *
     * @param string|null $startTime 任务开始时间
     *
     * @return self
     */
    public function setStartTime($startTime)
    {
        $this->container['startTime'] = $startTime;

        return $this;
    }

    /**
     * Gets subRecordStatus
     *
     * @return string|null
     */
    public function getSubRecordStatus()
    {
        return $this->container['subRecordStatus'];
    }

    /**
     * Sets subRecordStatus
     *
     * @param string|null $subRecordStatus 子记录状态
     *
     * @return self
     */
    public function setSubRecordStatus($subRecordStatus)
    {
        $this->container['subRecordStatus'] = $subRecordStatus;

        return $this;
    }

    /**
     * Gets subTitle
     *
     * @return string|null
     */
    public function getSubTitle()
    {
        return $this->container['subTitle'];
    }

    /**
     * Sets subTitle
     *
     * @param string|null $subTitle 副标题
     *
     * @return self
     */
    public function setSubTitle($subTitle)
    {
        $this->container['subTitle'] = $subTitle;

        return $this;
    }

    /**
     * Gets templateId
     *
     * @return string|null
     */
    public function getTemplateId()
    {
        return $this->container['templateId'];
    }

    /**
     * Sets templateId
     *
     * @param string|null $templateId 生活记录模板id
     *
     * @return self
     */
    public function setTemplateId($templateId)
    {
        $this->container['templateId'] = $templateId;

        return $this;
    }

    /**
     * Gets totalPeriods
     *
     * @return int|null
     */
    public function getTotalPeriods()
    {
        return $this->container['totalPeriods'];
    }

    /**
     * Sets totalPeriods
     *
     * @param int|null $totalPeriods 任务总期数
     *
     * @return self
     */
    public function setTotalPeriods($totalPeriods)
    {
        $this->container['totalPeriods'] = $totalPeriods;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


