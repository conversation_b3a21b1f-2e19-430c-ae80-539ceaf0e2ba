<?php
/**
 * EcConsumeInfo
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * EcConsumeInfo Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class EcConsumeInfo implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'EcConsumeInfo';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'accountId' => 'string',
        'agreementPeerPayerId' => 'string',
        'benefitAmount' => 'string',
        'bizOutNo' => 'string',
        'categoryName' => 'string',
        'consumeAmount' => 'string',
        'consumeBizType' => 'string',
        'consumeCategory' => 'string',
        'consumeFeeWithDiscount' => 'string',
        'consumeMemo' => 'string',
        'consumeTitle' => 'string',
        'consumeType' => 'string',
        'employeeId' => 'string',
        'enterpriseId' => 'string',
        'expenseRuleGroupId' => 'string',
        'expenseSceneCode' => 'string',
        'expenseType' => 'string',
        'expenseTypeSubCategory' => 'string',
        'extInfos' => 'string',
        'fundBizType' => 'string',
        'gmtBizCreate' => 'string',
        'gmtReceivePay' => 'string',
        'invoiceOpenMode' => 'string',
        'merchantId' => 'string',
        'merchantName' => 'string',
        'openId' => 'string',
        'oppositeFullName' => 'string',
        'orderCompleteLabel' => 'string',
        'orderCompleteTime' => 'string',
        'payNo' => 'string',
        'payerCardNo' => 'string',
        'payerLogonId' => 'string',
        'payerName' => 'string',
        'peerPayAmount' => 'string',
        'peerPayerCardNo' => 'string',
        'peerRefundAmount' => 'string',
        'peerRefundStatus' => 'string',
        'refundAmount' => 'string',
        'refundStatus' => 'string',
        'relatedPayNo' => 'string',
        'sceneCode' => 'string',
        'sellerId' => 'string',
        'shopId' => 'string',
        'storeId' => 'string',
        'summaryApplyId' => 'string',
        'userId' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'accountId' => null,
        'agreementPeerPayerId' => null,
        'benefitAmount' => null,
        'bizOutNo' => null,
        'categoryName' => null,
        'consumeAmount' => null,
        'consumeBizType' => null,
        'consumeCategory' => null,
        'consumeFeeWithDiscount' => null,
        'consumeMemo' => null,
        'consumeTitle' => null,
        'consumeType' => null,
        'employeeId' => null,
        'enterpriseId' => null,
        'expenseRuleGroupId' => null,
        'expenseSceneCode' => null,
        'expenseType' => null,
        'expenseTypeSubCategory' => null,
        'extInfos' => null,
        'fundBizType' => null,
        'gmtBizCreate' => null,
        'gmtReceivePay' => null,
        'invoiceOpenMode' => null,
        'merchantId' => null,
        'merchantName' => null,
        'openId' => null,
        'oppositeFullName' => null,
        'orderCompleteLabel' => null,
        'orderCompleteTime' => null,
        'payNo' => null,
        'payerCardNo' => null,
        'payerLogonId' => null,
        'payerName' => null,
        'peerPayAmount' => null,
        'peerPayerCardNo' => null,
        'peerRefundAmount' => null,
        'peerRefundStatus' => null,
        'refundAmount' => null,
        'refundStatus' => null,
        'relatedPayNo' => null,
        'sceneCode' => null,
        'sellerId' => null,
        'shopId' => null,
        'storeId' => null,
        'summaryApplyId' => null,
        'userId' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'accountId' => 'account_id',
        'agreementPeerPayerId' => 'agreement_peer_payer_id',
        'benefitAmount' => 'benefit_amount',
        'bizOutNo' => 'biz_out_no',
        'categoryName' => 'category_name',
        'consumeAmount' => 'consume_amount',
        'consumeBizType' => 'consume_biz_type',
        'consumeCategory' => 'consume_category',
        'consumeFeeWithDiscount' => 'consume_fee_with_discount',
        'consumeMemo' => 'consume_memo',
        'consumeTitle' => 'consume_title',
        'consumeType' => 'consume_type',
        'employeeId' => 'employee_id',
        'enterpriseId' => 'enterprise_id',
        'expenseRuleGroupId' => 'expense_rule_group_id',
        'expenseSceneCode' => 'expense_scene_code',
        'expenseType' => 'expense_type',
        'expenseTypeSubCategory' => 'expense_type_sub_category',
        'extInfos' => 'ext_infos',
        'fundBizType' => 'fund_biz_type',
        'gmtBizCreate' => 'gmt_biz_create',
        'gmtReceivePay' => 'gmt_receive_pay',
        'invoiceOpenMode' => 'invoice_open_mode',
        'merchantId' => 'merchant_id',
        'merchantName' => 'merchant_name',
        'openId' => 'open_id',
        'oppositeFullName' => 'opposite_full_name',
        'orderCompleteLabel' => 'order_complete_label',
        'orderCompleteTime' => 'order_complete_time',
        'payNo' => 'pay_no',
        'payerCardNo' => 'payer_card_no',
        'payerLogonId' => 'payer_logon_id',
        'payerName' => 'payer_name',
        'peerPayAmount' => 'peer_pay_amount',
        'peerPayerCardNo' => 'peer_payer_card_no',
        'peerRefundAmount' => 'peer_refund_amount',
        'peerRefundStatus' => 'peer_refund_status',
        'refundAmount' => 'refund_amount',
        'refundStatus' => 'refund_status',
        'relatedPayNo' => 'related_pay_no',
        'sceneCode' => 'scene_code',
        'sellerId' => 'seller_id',
        'shopId' => 'shop_id',
        'storeId' => 'store_id',
        'summaryApplyId' => 'summary_apply_id',
        'userId' => 'user_id'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'accountId' => 'setAccountId',
        'agreementPeerPayerId' => 'setAgreementPeerPayerId',
        'benefitAmount' => 'setBenefitAmount',
        'bizOutNo' => 'setBizOutNo',
        'categoryName' => 'setCategoryName',
        'consumeAmount' => 'setConsumeAmount',
        'consumeBizType' => 'setConsumeBizType',
        'consumeCategory' => 'setConsumeCategory',
        'consumeFeeWithDiscount' => 'setConsumeFeeWithDiscount',
        'consumeMemo' => 'setConsumeMemo',
        'consumeTitle' => 'setConsumeTitle',
        'consumeType' => 'setConsumeType',
        'employeeId' => 'setEmployeeId',
        'enterpriseId' => 'setEnterpriseId',
        'expenseRuleGroupId' => 'setExpenseRuleGroupId',
        'expenseSceneCode' => 'setExpenseSceneCode',
        'expenseType' => 'setExpenseType',
        'expenseTypeSubCategory' => 'setExpenseTypeSubCategory',
        'extInfos' => 'setExtInfos',
        'fundBizType' => 'setFundBizType',
        'gmtBizCreate' => 'setGmtBizCreate',
        'gmtReceivePay' => 'setGmtReceivePay',
        'invoiceOpenMode' => 'setInvoiceOpenMode',
        'merchantId' => 'setMerchantId',
        'merchantName' => 'setMerchantName',
        'openId' => 'setOpenId',
        'oppositeFullName' => 'setOppositeFullName',
        'orderCompleteLabel' => 'setOrderCompleteLabel',
        'orderCompleteTime' => 'setOrderCompleteTime',
        'payNo' => 'setPayNo',
        'payerCardNo' => 'setPayerCardNo',
        'payerLogonId' => 'setPayerLogonId',
        'payerName' => 'setPayerName',
        'peerPayAmount' => 'setPeerPayAmount',
        'peerPayerCardNo' => 'setPeerPayerCardNo',
        'peerRefundAmount' => 'setPeerRefundAmount',
        'peerRefundStatus' => 'setPeerRefundStatus',
        'refundAmount' => 'setRefundAmount',
        'refundStatus' => 'setRefundStatus',
        'relatedPayNo' => 'setRelatedPayNo',
        'sceneCode' => 'setSceneCode',
        'sellerId' => 'setSellerId',
        'shopId' => 'setShopId',
        'storeId' => 'setStoreId',
        'summaryApplyId' => 'setSummaryApplyId',
        'userId' => 'setUserId'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'accountId' => 'getAccountId',
        'agreementPeerPayerId' => 'getAgreementPeerPayerId',
        'benefitAmount' => 'getBenefitAmount',
        'bizOutNo' => 'getBizOutNo',
        'categoryName' => 'getCategoryName',
        'consumeAmount' => 'getConsumeAmount',
        'consumeBizType' => 'getConsumeBizType',
        'consumeCategory' => 'getConsumeCategory',
        'consumeFeeWithDiscount' => 'getConsumeFeeWithDiscount',
        'consumeMemo' => 'getConsumeMemo',
        'consumeTitle' => 'getConsumeTitle',
        'consumeType' => 'getConsumeType',
        'employeeId' => 'getEmployeeId',
        'enterpriseId' => 'getEnterpriseId',
        'expenseRuleGroupId' => 'getExpenseRuleGroupId',
        'expenseSceneCode' => 'getExpenseSceneCode',
        'expenseType' => 'getExpenseType',
        'expenseTypeSubCategory' => 'getExpenseTypeSubCategory',
        'extInfos' => 'getExtInfos',
        'fundBizType' => 'getFundBizType',
        'gmtBizCreate' => 'getGmtBizCreate',
        'gmtReceivePay' => 'getGmtReceivePay',
        'invoiceOpenMode' => 'getInvoiceOpenMode',
        'merchantId' => 'getMerchantId',
        'merchantName' => 'getMerchantName',
        'openId' => 'getOpenId',
        'oppositeFullName' => 'getOppositeFullName',
        'orderCompleteLabel' => 'getOrderCompleteLabel',
        'orderCompleteTime' => 'getOrderCompleteTime',
        'payNo' => 'getPayNo',
        'payerCardNo' => 'getPayerCardNo',
        'payerLogonId' => 'getPayerLogonId',
        'payerName' => 'getPayerName',
        'peerPayAmount' => 'getPeerPayAmount',
        'peerPayerCardNo' => 'getPeerPayerCardNo',
        'peerRefundAmount' => 'getPeerRefundAmount',
        'peerRefundStatus' => 'getPeerRefundStatus',
        'refundAmount' => 'getRefundAmount',
        'refundStatus' => 'getRefundStatus',
        'relatedPayNo' => 'getRelatedPayNo',
        'sceneCode' => 'getSceneCode',
        'sellerId' => 'getSellerId',
        'shopId' => 'getShopId',
        'storeId' => 'getStoreId',
        'summaryApplyId' => 'getSummaryApplyId',
        'userId' => 'getUserId'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['accountId'] = $data['accountId'] ?? null;
        $this->container['agreementPeerPayerId'] = $data['agreementPeerPayerId'] ?? null;
        $this->container['benefitAmount'] = $data['benefitAmount'] ?? null;
        $this->container['bizOutNo'] = $data['bizOutNo'] ?? null;
        $this->container['categoryName'] = $data['categoryName'] ?? null;
        $this->container['consumeAmount'] = $data['consumeAmount'] ?? null;
        $this->container['consumeBizType'] = $data['consumeBizType'] ?? null;
        $this->container['consumeCategory'] = $data['consumeCategory'] ?? null;
        $this->container['consumeFeeWithDiscount'] = $data['consumeFeeWithDiscount'] ?? null;
        $this->container['consumeMemo'] = $data['consumeMemo'] ?? null;
        $this->container['consumeTitle'] = $data['consumeTitle'] ?? null;
        $this->container['consumeType'] = $data['consumeType'] ?? null;
        $this->container['employeeId'] = $data['employeeId'] ?? null;
        $this->container['enterpriseId'] = $data['enterpriseId'] ?? null;
        $this->container['expenseRuleGroupId'] = $data['expenseRuleGroupId'] ?? null;
        $this->container['expenseSceneCode'] = $data['expenseSceneCode'] ?? null;
        $this->container['expenseType'] = $data['expenseType'] ?? null;
        $this->container['expenseTypeSubCategory'] = $data['expenseTypeSubCategory'] ?? null;
        $this->container['extInfos'] = $data['extInfos'] ?? null;
        $this->container['fundBizType'] = $data['fundBizType'] ?? null;
        $this->container['gmtBizCreate'] = $data['gmtBizCreate'] ?? null;
        $this->container['gmtReceivePay'] = $data['gmtReceivePay'] ?? null;
        $this->container['invoiceOpenMode'] = $data['invoiceOpenMode'] ?? null;
        $this->container['merchantId'] = $data['merchantId'] ?? null;
        $this->container['merchantName'] = $data['merchantName'] ?? null;
        $this->container['openId'] = $data['openId'] ?? null;
        $this->container['oppositeFullName'] = $data['oppositeFullName'] ?? null;
        $this->container['orderCompleteLabel'] = $data['orderCompleteLabel'] ?? null;
        $this->container['orderCompleteTime'] = $data['orderCompleteTime'] ?? null;
        $this->container['payNo'] = $data['payNo'] ?? null;
        $this->container['payerCardNo'] = $data['payerCardNo'] ?? null;
        $this->container['payerLogonId'] = $data['payerLogonId'] ?? null;
        $this->container['payerName'] = $data['payerName'] ?? null;
        $this->container['peerPayAmount'] = $data['peerPayAmount'] ?? null;
        $this->container['peerPayerCardNo'] = $data['peerPayerCardNo'] ?? null;
        $this->container['peerRefundAmount'] = $data['peerRefundAmount'] ?? null;
        $this->container['peerRefundStatus'] = $data['peerRefundStatus'] ?? null;
        $this->container['refundAmount'] = $data['refundAmount'] ?? null;
        $this->container['refundStatus'] = $data['refundStatus'] ?? null;
        $this->container['relatedPayNo'] = $data['relatedPayNo'] ?? null;
        $this->container['sceneCode'] = $data['sceneCode'] ?? null;
        $this->container['sellerId'] = $data['sellerId'] ?? null;
        $this->container['shopId'] = $data['shopId'] ?? null;
        $this->container['storeId'] = $data['storeId'] ?? null;
        $this->container['summaryApplyId'] = $data['summaryApplyId'] ?? null;
        $this->container['userId'] = $data['userId'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets accountId
     *
     * @return string|null
     */
    public function getAccountId()
    {
        return $this->container['accountId'];
    }

    /**
     * Sets accountId
     *
     * @param string|null $accountId 共同账户ID
     *
     * @return self
     */
    public function setAccountId($accountId)
    {
        $this->container['accountId'] = $accountId;

        return $this;
    }

    /**
     * Gets agreementPeerPayerId
     *
     * @return string|null
     */
    public function getAgreementPeerPayerId()
    {
        return $this->container['agreementPeerPayerId'];
    }

    /**
     * Sets agreementPeerPayerId
     *
     * @param string|null $agreementPeerPayerId 协议出资支付宝账号
     *
     * @return self
     */
    public function setAgreementPeerPayerId($agreementPeerPayerId)
    {
        $this->container['agreementPeerPayerId'] = $agreementPeerPayerId;

        return $this;
    }

    /**
     * Gets benefitAmount
     *
     * @return string|null
     */
    public function getBenefitAmount()
    {
        return $this->container['benefitAmount'];
    }

    /**
     * Sets benefitAmount
     *
     * @param string|null $benefitAmount 员工优惠金额，单位：元
     *
     * @return self
     */
    public function setBenefitAmount($benefitAmount)
    {
        $this->container['benefitAmount'] = $benefitAmount;

        return $this;
    }

    /**
     * Gets bizOutNo
     *
     * @return string|null
     */
    public function getBizOutNo()
    {
        return $this->container['bizOutNo'];
    }

    /**
     * Sets bizOutNo
     *
     * @param string|null $bizOutNo 外部交易流水号
     *
     * @return self
     */
    public function setBizOutNo($bizOutNo)
    {
        $this->container['bizOutNo'] = $bizOutNo;

        return $this;
    }

    /**
     * Gets categoryName
     *
     * @return string|null
     */
    public function getCategoryName()
    {
        return $this->container['categoryName'];
    }

    /**
     * Sets categoryName
     *
     * @param string|null $categoryName 账单分类名称，如餐饮等
     *
     * @return self
     */
    public function setCategoryName($categoryName)
    {
        $this->container['categoryName'] = $categoryName;

        return $this;
    }

    /**
     * Gets consumeAmount
     *
     * @return string|null
     */
    public function getConsumeAmount()
    {
        return $this->container['consumeAmount'];
    }

    /**
     * Sets consumeAmount
     *
     * @param string|null $consumeAmount 账单金额，单位：元，不包含营销资产
     *
     * @return self
     */
    public function setConsumeAmount($consumeAmount)
    {
        $this->container['consumeAmount'] = $consumeAmount;

        return $this;
    }

    /**
     * Gets consumeBizType
     *
     * @return string|null
     */
    public function getConsumeBizType()
    {
        return $this->container['consumeBizType'];
    }

    /**
     * Sets consumeBizType
     *
     * @param string|null $consumeBizType 账单业务类型 -因公支付：EC_PAY -因公收款：EC_CLLCT
     *
     * @return self
     */
    public function setConsumeBizType($consumeBizType)
    {
        $this->container['consumeBizType'] = $consumeBizType;

        return $this;
    }

    /**
     * Gets consumeCategory
     *
     * @return string|null
     */
    public function getConsumeCategory()
    {
        return $this->container['consumeCategory'];
    }

    /**
     * Sets consumeCategory
     *
     * @param string|null $consumeCategory 员工消费记账分类： \"cater\": \"餐饮\" \"purchase\": \"采购\" \"trip\": \"出行\" \"train\": \"培训\" \"entertainment\": \"娱乐\" \"hotel\": \"酒店\" \"pay\": \"缴费\" \"rent\": \"房租\" \"other\": \"其他\"
     *
     * @return self
     */
    public function setConsumeCategory($consumeCategory)
    {
        $this->container['consumeCategory'] = $consumeCategory;

        return $this;
    }

    /**
     * Gets consumeFeeWithDiscount
     *
     * @return string|null
     */
    public function getConsumeFeeWithDiscount()
    {
        return $this->container['consumeFeeWithDiscount'];
    }

    /**
     * Sets consumeFeeWithDiscount
     *
     * @param string|null $consumeFeeWithDiscount 订单原价，单位：元，包含营销资产
     *
     * @return self
     */
    public function setConsumeFeeWithDiscount($consumeFeeWithDiscount)
    {
        $this->container['consumeFeeWithDiscount'] = $consumeFeeWithDiscount;

        return $this;
    }

    /**
     * Gets consumeMemo
     *
     * @return string|null
     */
    public function getConsumeMemo()
    {
        return $this->container['consumeMemo'];
    }

    /**
     * Sets consumeMemo
     *
     * @param string|null $consumeMemo 员工消费记账备注
     *
     * @return self
     */
    public function setConsumeMemo($consumeMemo)
    {
        $this->container['consumeMemo'] = $consumeMemo;

        return $this;
    }

    /**
     * Gets consumeTitle
     *
     * @return string|null
     */
    public function getConsumeTitle()
    {
        return $this->container['consumeTitle'];
    }

    /**
     * Sets consumeTitle
     *
     * @param string|null $consumeTitle 商户收款时传的商品备注说明，在账单内进行表达
     *
     * @return self
     */
    public function setConsumeTitle($consumeTitle)
    {
        $this->container['consumeTitle'] = $consumeTitle;

        return $this;
    }

    /**
     * Gets consumeType
     *
     * @return string|null
     */
    public function getConsumeType()
    {
        return $this->container['consumeType'];
    }

    /**
     * Sets consumeType
     *
     * @param string|null $consumeType 账单类型 -消费账单：CONSUME -退款账单：REFUND -转账账单：TRANSFER
     *
     * @return self
     */
    public function setConsumeType($consumeType)
    {
        $this->container['consumeType'] = $consumeType;

        return $this;
    }

    /**
     * Gets employeeId
     *
     * @return string|null
     */
    public function getEmployeeId()
    {
        return $this->container['employeeId'];
    }

    /**
     * Sets employeeId
     *
     * @param string|null $employeeId 员工账号ID
     *
     * @return self
     */
    public function setEmployeeId($employeeId)
    {
        $this->container['employeeId'] = $employeeId;

        return $this;
    }

    /**
     * Gets enterpriseId
     *
     * @return string|null
     */
    public function getEnterpriseId()
    {
        return $this->container['enterpriseId'];
    }

    /**
     * Sets enterpriseId
     *
     * @param string|null $enterpriseId 企业ID
     *
     * @return self
     */
    public function setEnterpriseId($enterpriseId)
    {
        $this->container['enterpriseId'] = $enterpriseId;

        return $this;
    }

    /**
     * Gets expenseRuleGroupId
     *
     * @return string|null
     */
    public function getExpenseRuleGroupId()
    {
        return $this->container['expenseRuleGroupId'];
    }

    /**
     * Sets expenseRuleGroupId
     *
     * @param string|null $expenseRuleGroupId 使用规则ID
     *
     * @return self
     */
    public function setExpenseRuleGroupId($expenseRuleGroupId)
    {
        $this->container['expenseRuleGroupId'] = $expenseRuleGroupId;

        return $this;
    }

    /**
     * Gets expenseSceneCode
     *
     * @return string|null
     */
    public function getExpenseSceneCode()
    {
        return $this->container['expenseSceneCode'];
    }

    /**
     * Sets expenseSceneCode
     *
     * @param string|null $expenseSceneCode 费用场景
     *
     * @return self
     */
    public function setExpenseSceneCode($expenseSceneCode)
    {
        $this->container['expenseSceneCode'] = $expenseSceneCode;

        return $this;
    }

    /**
     * Gets expenseType
     *
     * @return string|null
     */
    public function getExpenseType()
    {
        return $this->container['expenseType'];
    }

    /**
     * Sets expenseType
     *
     * @param string|null $expenseType 费用类型
     *
     * @return self
     */
    public function setExpenseType($expenseType)
    {
        $this->container['expenseType'] = $expenseType;

        return $this;
    }

    /**
     * Gets expenseTypeSubCategory
     *
     * @return string|null
     */
    public function getExpenseTypeSubCategory()
    {
        return $this->container['expenseTypeSubCategory'];
    }

    /**
     * Sets expenseTypeSubCategory
     *
     * @param string|null $expenseTypeSubCategory 费用类型子类目
     *
     * @return self
     */
    public function setExpenseTypeSubCategory($expenseTypeSubCategory)
    {
        $this->container['expenseTypeSubCategory'] = $expenseTypeSubCategory;

        return $this;
    }

    /**
     * Gets extInfos
     *
     * @return string|null
     */
    public function getExtInfos()
    {
        return $this->container['extInfos'];
    }

    /**
     * Sets extInfos
     *
     * @param string|null $extInfos 账单扩展信息，JsonMap格式，以下信息可组合返回，\"SUMMARY_INFO\"表示\"账单汇总信息\"，\"MERCHANT_EXTEND_INFO\"表示\"商户信息\"，\"TERMINAL_INFO\"表示\"终端设备信息\"，\"BENEFIT_INFO\"表示\"员工优惠信息\"，\"CONSUMPTION_LOCATION\"表示\"消费上报位置\"，\"THIRD_PARTY_PAYMENT_INFO\"表示\"代收款详情\"
     *
     * @return self
     */
    public function setExtInfos($extInfos)
    {
        $this->container['extInfos'] = $extInfos;

        return $this;
    }

    /**
     * Gets fundBizType
     *
     * @return string|null
     */
    public function getFundBizType()
    {
        return $this->container['fundBizType'];
    }

    /**
     * Sets fundBizType
     *
     * @param string|null $fundBizType 出资模式 个人出资：PERSONAL 企业出资：ENTERPRISE 三方垫资合作伙伴出资：TP 记账：ACCOUNTING
     *
     * @return self
     */
    public function setFundBizType($fundBizType)
    {
        $this->container['fundBizType'] = $fundBizType;

        return $this;
    }

    /**
     * Gets gmtBizCreate
     *
     * @return string|null
     */
    public function getGmtBizCreate()
    {
        return $this->container['gmtBizCreate'];
    }

    /**
     * Sets gmtBizCreate
     *
     * @param string|null $gmtBizCreate 账单创建时间，格式：yyyy-MM-dd HH:mm:ss
     *
     * @return self
     */
    public function setGmtBizCreate($gmtBizCreate)
    {
        $this->container['gmtBizCreate'] = $gmtBizCreate;

        return $this;
    }

    /**
     * Gets gmtReceivePay
     *
     * @return string|null
     */
    public function getGmtReceivePay()
    {
        return $this->container['gmtReceivePay'];
    }

    /**
     * Sets gmtReceivePay
     *
     * @param string|null $gmtReceivePay 账单支付时间，格式：yyyy-MM-dd HH:mm:ss
     *
     * @return self
     */
    public function setGmtReceivePay($gmtReceivePay)
    {
        $this->container['gmtReceivePay'] = $gmtReceivePay;

        return $this;
    }

    /**
     * Gets invoiceOpenMode
     *
     * @return string|null
     */
    public function getInvoiceOpenMode()
    {
        return $this->container['invoiceOpenMode'];
    }

    /**
     * Sets invoiceOpenMode
     *
     * @param string|null $invoiceOpenMode 开票模式 企业汇总开：ENTERPRISE_AUTO_BATCH
     *
     * @return self
     */
    public function setInvoiceOpenMode($invoiceOpenMode)
    {
        $this->container['invoiceOpenMode'] = $invoiceOpenMode;

        return $this;
    }

    /**
     * Gets merchantId
     *
     * @return string|null
     */
    public function getMerchantId()
    {
        return $this->container['merchantId'];
    }

    /**
     * Sets merchantId
     *
     * @param string|null $merchantId 商户ID
     *
     * @return self
     */
    public function setMerchantId($merchantId)
    {
        $this->container['merchantId'] = $merchantId;

        return $this;
    }

    /**
     * Gets merchantName
     *
     * @return string|null
     */
    public function getMerchantName()
    {
        return $this->container['merchantName'];
    }

    /**
     * Sets merchantName
     *
     * @param string|null $merchantName 商户名称
     *
     * @return self
     */
    public function setMerchantName($merchantName)
    {
        $this->container['merchantName'] = $merchantName;

        return $this;
    }

    /**
     * Gets openId
     *
     * @return string|null
     */
    public function getOpenId()
    {
        return $this->container['openId'];
    }

    /**
     * Sets openId
     *
     * @param string|null $openId 员工支付宝UID
     *
     * @return self
     */
    public function setOpenId($openId)
    {
        $this->container['openId'] = $openId;

        return $this;
    }

    /**
     * Gets oppositeFullName
     *
     * @return string|null
     */
    public function getOppositeFullName()
    {
        return $this->container['oppositeFullName'];
    }

    /**
     * Sets oppositeFullName
     *
     * @param string|null $oppositeFullName 收款方全称（如果是个人会显示脱敏后的名称）
     *
     * @return self
     */
    public function setOppositeFullName($oppositeFullName)
    {
        $this->container['oppositeFullName'] = $oppositeFullName;

        return $this;
    }

    /**
     * Gets orderCompleteLabel
     *
     * @return string|null
     */
    public function getOrderCompleteLabel()
    {
        return $this->container['orderCompleteLabel'];
    }

    /**
     * Sets orderCompleteLabel
     *
     * @param string|null $orderCompleteLabel 订单完结标识，0标识“未完结”，1标识“已完结”
     *
     * @return self
     */
    public function setOrderCompleteLabel($orderCompleteLabel)
    {
        $this->container['orderCompleteLabel'] = $orderCompleteLabel;

        return $this;
    }

    /**
     * Gets orderCompleteTime
     *
     * @return string|null
     */
    public function getOrderCompleteTime()
    {
        return $this->container['orderCompleteTime'];
    }

    /**
     * Sets orderCompleteTime
     *
     * @param string|null $orderCompleteTime 2022-01-01 01:01:02 订单完结时间
     *
     * @return self
     */
    public function setOrderCompleteTime($orderCompleteTime)
    {
        $this->container['orderCompleteTime'] = $orderCompleteTime;

        return $this;
    }

    /**
     * Gets payNo
     *
     * @return string|null
     */
    public function getPayNo()
    {
        return $this->container['payNo'];
    }

    /**
     * Sets payNo
     *
     * @param string|null $payNo 交易流水号
     *
     * @return self
     */
    public function setPayNo($payNo)
    {
        $this->container['payNo'] = $payNo;

        return $this;
    }

    /**
     * Gets payerCardNo
     *
     * @return string|null
     */
    public function getPayerCardNo()
    {
        return $this->container['payerCardNo'];
    }

    /**
     * Sets payerCardNo
     *
     * @param string|null $payerCardNo 付款方卡号
     *
     * @return self
     */
    public function setPayerCardNo($payerCardNo)
    {
        $this->container['payerCardNo'] = $payerCardNo;

        return $this;
    }

    /**
     * Gets payerLogonId
     *
     * @return string|null
     */
    public function getPayerLogonId()
    {
        return $this->container['payerLogonId'];
    }

    /**
     * Sets payerLogonId
     *
     * @param string|null $payerLogonId 付款方登录账号
     *
     * @return self
     */
    public function setPayerLogonId($payerLogonId)
    {
        $this->container['payerLogonId'] = $payerLogonId;

        return $this;
    }

    /**
     * Gets payerName
     *
     * @return string|null
     */
    public function getPayerName()
    {
        return $this->container['payerName'];
    }

    /**
     * Sets payerName
     *
     * @param string|null $payerName 付款方名称
     *
     * @return self
     */
    public function setPayerName($payerName)
    {
        $this->container['payerName'] = $payerName;

        return $this;
    }

    /**
     * Gets peerPayAmount
     *
     * @return string|null
     */
    public function getPeerPayAmount()
    {
        return $this->container['peerPayAmount'];
    }

    /**
     * Sets peerPayAmount
     *
     * @param string|null $peerPayAmount 企业代付金额，单位：元
     *
     * @return self
     */
    public function setPeerPayAmount($peerPayAmount)
    {
        $this->container['peerPayAmount'] = $peerPayAmount;

        return $this;
    }

    /**
     * Gets peerPayerCardNo
     *
     * @return string|null
     */
    public function getPeerPayerCardNo()
    {
        return $this->container['peerPayerCardNo'];
    }

    /**
     * Sets peerPayerCardNo
     *
     * @param string|null $peerPayerCardNo 实际出资支付宝账号
     *
     * @return self
     */
    public function setPeerPayerCardNo($peerPayerCardNo)
    {
        $this->container['peerPayerCardNo'] = $peerPayerCardNo;

        return $this;
    }

    /**
     * Gets peerRefundAmount
     *
     * @return string|null
     */
    public function getPeerRefundAmount()
    {
        return $this->container['peerRefundAmount'];
    }

    /**
     * Sets peerRefundAmount
     *
     * @param string|null $peerRefundAmount 消费账单企业代付部分退款金额，单位：元。退款账单该值无意义，值为0。
     *
     * @return self
     */
    public function setPeerRefundAmount($peerRefundAmount)
    {
        $this->container['peerRefundAmount'] = $peerRefundAmount;

        return $this;
    }

    /**
     * Gets peerRefundStatus
     *
     * @return string|null
     */
    public function getPeerRefundStatus()
    {
        return $this->container['peerRefundStatus'];
    }

    /**
     * Sets peerRefundStatus
     *
     * @param string|null $peerRefundStatus 该字段只对因公付交易生效，默认为INIT。当消费账单有退款，该值会变为REFUND_PART或REFUND_FULL；退款账单该值无意义，为初始值INIT。 未退款：INIT 部分退款：REFUND_PART， 全额退款：REFUND_FULL
     *
     * @return self
     */
    public function setPeerRefundStatus($peerRefundStatus)
    {
        $this->container['peerRefundStatus'] = $peerRefundStatus;

        return $this;
    }

    /**
     * Gets refundAmount
     *
     * @return string|null
     */
    public function getRefundAmount()
    {
        return $this->container['refundAmount'];
    }

    /**
     * Sets refundAmount
     *
     * @param string|null $refundAmount 消费账单退款金额，单位：元。退款账单该值无意义，值为0。
     *
     * @return self
     */
    public function setRefundAmount($refundAmount)
    {
        $this->container['refundAmount'] = $refundAmount;

        return $this;
    }

    /**
     * Gets refundStatus
     *
     * @return string|null
     */
    public function getRefundStatus()
    {
        return $this->container['refundStatus'];
    }

    /**
     * Sets refundStatus
     *
     * @param string|null $refundStatus 当消费账单有退款，该值会变为REFUND_PART或REFUND_FULL；退款账单该值无意义，为初始值INIT。
     *
     * @return self
     */
    public function setRefundStatus($refundStatus)
    {
        $this->container['refundStatus'] = $refundStatus;

        return $this;
    }

    /**
     * Gets relatedPayNo
     *
     * @return string|null
     */
    public function getRelatedPayNo()
    {
        return $this->container['relatedPayNo'];
    }

    /**
     * Sets relatedPayNo
     *
     * @param string|null $relatedPayNo 退款账单关联的消费账单交易流水号，退款账单才有值
     *
     * @return self
     */
    public function setRelatedPayNo($relatedPayNo)
    {
        $this->container['relatedPayNo'] = $relatedPayNo;

        return $this;
    }

    /**
     * Gets sceneCode
     *
     * @return string|null
     */
    public function getSceneCode()
    {
        return $this->container['sceneCode'];
    }

    /**
     * Sets sceneCode
     *
     * @param string|null $sceneCode 账单场景 TAKE_AWAY：外卖 METRO：地铁 OTHER：其他
     *
     * @return self
     */
    public function setSceneCode($sceneCode)
    {
        $this->container['sceneCode'] = $sceneCode;

        return $this;
    }

    /**
     * Gets sellerId
     *
     * @return string|null
     */
    public function getSellerId()
    {
        return $this->container['sellerId'];
    }

    /**
     * Sets sellerId
     *
     * @param string|null $sellerId 卖家ID
     *
     * @return self
     */
    public function setSellerId($sellerId)
    {
        $this->container['sellerId'] = $sellerId;

        return $this;
    }

    /**
     * Gets shopId
     *
     * @return string|null
     */
    public function getShopId()
    {
        return $this->container['shopId'];
    }

    /**
     * Sets shopId
     *
     * @param string|null $shopId 门店ID
     *
     * @return self
     */
    public function setShopId($shopId)
    {
        $this->container['shopId'] = $shopId;

        return $this;
    }

    /**
     * Gets storeId
     *
     * @return string|null
     */
    public function getStoreId()
    {
        return $this->container['storeId'];
    }

    /**
     * Sets storeId
     *
     * @param string|null $storeId 外部门店ID
     *
     * @return self
     */
    public function setStoreId($storeId)
    {
        $this->container['storeId'] = $storeId;

        return $this;
    }

    /**
     * Gets summaryApplyId
     *
     * @return string|null
     */
    public function getSummaryApplyId()
    {
        return $this->container['summaryApplyId'];
    }

    /**
     * Sets summaryApplyId
     *
     * @param string|null $summaryApplyId 汇总批次ID
     *
     * @return self
     */
    public function setSummaryApplyId($summaryApplyId)
    {
        $this->container['summaryApplyId'] = $summaryApplyId;

        return $this;
    }

    /**
     * Gets userId
     *
     * @return string|null
     */
    public function getUserId()
    {
        return $this->container['userId'];
    }

    /**
     * Sets userId
     *
     * @param string|null $userId 员工支付宝UID
     *
     * @return self
     */
    public function setUserId($userId)
    {
        $this->container['userId'] = $userId;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


