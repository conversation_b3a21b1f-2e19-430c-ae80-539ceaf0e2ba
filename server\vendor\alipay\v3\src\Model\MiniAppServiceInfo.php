<?php
/**
 * MiniAppServiceInfo
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * MiniAppServiceInfo Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class MiniAppServiceInfo implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'MiniAppServiceInfo';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'bizStatus' => 'string',
        'isInner' => 'bool',
        'isOrder' => 'bool',
        'isvAppId' => 'string',
        'miniAppId' => 'string',
        'miniAppName' => 'string',
        'sellerId' => 'string',
        'sellerName' => 'string',
        'serviceCode' => 'string',
        'serviceLogo' => 'string',
        'serviceName' => 'string',
        'serviceSlogan' => 'string',
        'showType' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'bizStatus' => null,
        'isInner' => null,
        'isOrder' => null,
        'isvAppId' => null,
        'miniAppId' => null,
        'miniAppName' => null,
        'sellerId' => null,
        'sellerName' => null,
        'serviceCode' => null,
        'serviceLogo' => null,
        'serviceName' => null,
        'serviceSlogan' => null,
        'showType' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'bizStatus' => 'biz_status',
        'isInner' => 'is_inner',
        'isOrder' => 'is_order',
        'isvAppId' => 'isv_app_id',
        'miniAppId' => 'mini_app_id',
        'miniAppName' => 'mini_app_name',
        'sellerId' => 'seller_id',
        'sellerName' => 'seller_name',
        'serviceCode' => 'service_code',
        'serviceLogo' => 'service_logo',
        'serviceName' => 'service_name',
        'serviceSlogan' => 'service_slogan',
        'showType' => 'show_type'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'bizStatus' => 'setBizStatus',
        'isInner' => 'setIsInner',
        'isOrder' => 'setIsOrder',
        'isvAppId' => 'setIsvAppId',
        'miniAppId' => 'setMiniAppId',
        'miniAppName' => 'setMiniAppName',
        'sellerId' => 'setSellerId',
        'sellerName' => 'setSellerName',
        'serviceCode' => 'setServiceCode',
        'serviceLogo' => 'setServiceLogo',
        'serviceName' => 'setServiceName',
        'serviceSlogan' => 'setServiceSlogan',
        'showType' => 'setShowType'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'bizStatus' => 'getBizStatus',
        'isInner' => 'getIsInner',
        'isOrder' => 'getIsOrder',
        'isvAppId' => 'getIsvAppId',
        'miniAppId' => 'getMiniAppId',
        'miniAppName' => 'getMiniAppName',
        'sellerId' => 'getSellerId',
        'sellerName' => 'getSellerName',
        'serviceCode' => 'getServiceCode',
        'serviceLogo' => 'getServiceLogo',
        'serviceName' => 'getServiceName',
        'serviceSlogan' => 'getServiceSlogan',
        'showType' => 'getShowType'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['bizStatus'] = $data['bizStatus'] ?? null;
        $this->container['isInner'] = $data['isInner'] ?? null;
        $this->container['isOrder'] = $data['isOrder'] ?? null;
        $this->container['isvAppId'] = $data['isvAppId'] ?? null;
        $this->container['miniAppId'] = $data['miniAppId'] ?? null;
        $this->container['miniAppName'] = $data['miniAppName'] ?? null;
        $this->container['sellerId'] = $data['sellerId'] ?? null;
        $this->container['sellerName'] = $data['sellerName'] ?? null;
        $this->container['serviceCode'] = $data['serviceCode'] ?? null;
        $this->container['serviceLogo'] = $data['serviceLogo'] ?? null;
        $this->container['serviceName'] = $data['serviceName'] ?? null;
        $this->container['serviceSlogan'] = $data['serviceSlogan'] ?? null;
        $this->container['showType'] = $data['showType'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets bizStatus
     *
     * @return string|null
     */
    public function getBizStatus()
    {
        return $this->container['bizStatus'];
    }

    /**
     * Sets bizStatus
     *
     * @param string|null $bizStatus 插件发布状态码，暂存100，风控审核200，运营审核300，等待上架400，已预发上架500，已上架501，已下架600，已驳回700
     *
     * @return self
     */
    public function setBizStatus($bizStatus)
    {
        $this->container['bizStatus'] = $bizStatus;

        return $this;
    }

    /**
     * Gets isInner
     *
     * @return bool|null
     */
    public function getIsInner()
    {
        return $this->container['isInner'];
    }

    /**
     * Sets isInner
     *
     * @param bool|null $isInner 是否是内部标，true/false
     *
     * @return self
     */
    public function setIsInner($isInner)
    {
        $this->container['isInner'] = $isInner;

        return $this;
    }

    /**
     * Gets isOrder
     *
     * @return bool|null
     */
    public function getIsOrder()
    {
        return $this->container['isOrder'];
    }

    /**
     * Sets isOrder
     *
     * @param bool|null $isOrder 是否订购，true/false
     *
     * @return self
     */
    public function setIsOrder($isOrder)
    {
        $this->container['isOrder'] = $isOrder;

        return $this;
    }

    /**
     * Gets isvAppId
     *
     * @return string|null
     */
    public function getIsvAppId()
    {
        return $this->container['isvAppId'];
    }

    /**
     * Sets isvAppId
     *
     * @param string|null $isvAppId 三方应用appid
     *
     * @return self
     */
    public function setIsvAppId($isvAppId)
    {
        $this->container['isvAppId'] = $isvAppId;

        return $this;
    }

    /**
     * Gets miniAppId
     *
     * @return string|null
     */
    public function getMiniAppId()
    {
        return $this->container['miniAppId'];
    }

    /**
     * Sets miniAppId
     *
     * @param string|null $miniAppId 应用id
     *
     * @return self
     */
    public function setMiniAppId($miniAppId)
    {
        $this->container['miniAppId'] = $miniAppId;

        return $this;
    }

    /**
     * Gets miniAppName
     *
     * @return string|null
     */
    public function getMiniAppName()
    {
        return $this->container['miniAppName'];
    }

    /**
     * Sets miniAppName
     *
     * @param string|null $miniAppName 测试插件
     *
     * @return self
     */
    public function setMiniAppName($miniAppName)
    {
        $this->container['miniAppName'] = $miniAppName;

        return $this;
    }

    /**
     * Gets sellerId
     *
     * @return string|null
     */
    public function getSellerId()
    {
        return $this->container['sellerId'];
    }

    /**
     * Sets sellerId
     *
     * @param string|null $sellerId 卖家pid
     *
     * @return self
     */
    public function setSellerId($sellerId)
    {
        $this->container['sellerId'] = $sellerId;

        return $this;
    }

    /**
     * Gets sellerName
     *
     * @return string|null
     */
    public function getSellerName()
    {
        return $this->container['sellerName'];
    }

    /**
     * Sets sellerName
     *
     * @param string|null $sellerName 卖家名
     *
     * @return self
     */
    public function setSellerName($sellerName)
    {
        $this->container['sellerName'] = $sellerName;

        return $this;
    }

    /**
     * Gets serviceCode
     *
     * @return string|null
     */
    public function getServiceCode()
    {
        return $this->container['serviceCode'];
    }

    /**
     * Sets serviceCode
     *
     * @param string|null $serviceCode 商品CODE
     *
     * @return self
     */
    public function setServiceCode($serviceCode)
    {
        $this->container['serviceCode'] = $serviceCode;

        return $this;
    }

    /**
     * Gets serviceLogo
     *
     * @return string|null
     */
    public function getServiceLogo()
    {
        return $this->container['serviceLogo'];
    }

    /**
     * Sets serviceLogo
     *
     * @param string|null $serviceLogo 服务图标
     *
     * @return self
     */
    public function setServiceLogo($serviceLogo)
    {
        $this->container['serviceLogo'] = $serviceLogo;

        return $this;
    }

    /**
     * Gets serviceName
     *
     * @return string|null
     */
    public function getServiceName()
    {
        return $this->container['serviceName'];
    }

    /**
     * Sets serviceName
     *
     * @param string|null $serviceName 服务名
     *
     * @return self
     */
    public function setServiceName($serviceName)
    {
        $this->container['serviceName'] = $serviceName;

        return $this;
    }

    /**
     * Gets serviceSlogan
     *
     * @return string|null
     */
    public function getServiceSlogan()
    {
        return $this->container['serviceSlogan'];
    }

    /**
     * Sets serviceSlogan
     *
     * @param string|null $serviceSlogan 服务简介
     *
     * @return self
     */
    public function setServiceSlogan($serviceSlogan)
    {
        $this->container['serviceSlogan'] = $serviceSlogan;

        return $this;
    }

    /**
     * Gets showType
     *
     * @return string|null
     */
    public function getShowType()
    {
        return $this->container['showType'];
    }

    /**
     * Sets showType
     *
     * @param string|null $showType 是否在服务市场透出，SHOW展示、HIDE隐藏
     *
     * @return self
     */
    public function setShowType($showType)
    {
        $this->container['showType'] = $showType;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


