<?php
/**
 * 获取资源下载链接API
 * 版本: 1.0.0
 * 创建时间: 2024-12-19
 */

require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/User.php';
require_once '../../includes/Points.php';

// 验证请求方法
validateRequestMethod(['POST']);

try {
    // 验证用户Token
    $token = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    if (empty($token)) {
        handleError('缺少授权令牌', API_AUTH_ERROR_CODE);
    }
    
    $user = new User();
    $userId = $user->validateToken($token);
    
    if (!$userId) {
        handleError('无效的授权令牌', API_AUTH_ERROR_CODE);
    }
    
    // 获取请求数据
    $data = getRequestData();
    $resourceId = $data['resource_id'] ?? '';
    
    if (empty($resourceId)) {
        handleError('资源ID不能为空', API_ERROR_CODE);
    }
    
    $db = getDB();
    
    // 获取资源信息
    $resource = $db->selectOne('resources', [
        'id' => $resourceId,
        'status' => 'active'
    ]);
    
    if (!$resource) {
        handleError('资源不存在或已下架', API_NOT_FOUND_CODE);
    }
    
    // 检查用户积分是否足够
    $points = new Points($userId);
    $currentBalance = $points->getBalance();
    $pointsCost = (int)$resource['points_cost'];
    
    if ($currentBalance < $pointsCost) {
        handleError('积分余额不足，当前余额: ' . $currentBalance . '，需要: ' . $pointsCost, API_ERROR_CODE);
    }
    
    // 检查用户并发下载数量
    $maxConcurrent = (int)$db->selectOne('system_config', ['config_key' => 'max_download_concurrent'])['config_value'] ?? DEFAULT_MAX_DOWNLOAD_CONCURRENT;
    $currentDownloads = $db->count('downloads', [
        'user_id' => $userId,
        'status' => 'in_progress'
    ]);
    
    if ($currentDownloads >= $maxConcurrent) {
        handleError("同时下载数量不能超过 $maxConcurrent 个", API_ERROR_CODE);
    }
    
    try {
        $db->beginTransaction();
        
        // 消费积分
        if ($pointsCost > 0) {
            $consumeResult = $points->consumePoints($pointsCost, $resourceId, "下载资源: {$resource['name']}");
            if (!$consumeResult['success']) {
                throw new Exception('积分消费失败');
            }
        }
        
        // 记录下载记录
        $downloadData = [
            'user_id' => $userId,
            'resource_id' => $resourceId,
            'points_cost' => $pointsCost,
            'ip_address' => getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'status' => 'in_progress'
        ];
        
        $downloadId = $db->insertData('downloads', $downloadData);
        
        // 更新资源下载次数
        $db->execute("UPDATE resources SET download_count = download_count + 1 WHERE id = ?", [$resourceId]);
        
        $db->commit();
        
        // 生成下载链接（这里应该根据实际平台生成真实的下载链接）
        $downloadUrl = $this->generateDownloadUrl($resource);
        
        // 记录下载日志
        writeLog("资源下载开始: 用户ID $userId, 资源ID $resourceId, 消费积分 $pointsCost", 'INFO');
        
        // 返回成功响应
        jsonResponse([
            'download_id' => $downloadId,
            'resource_id' => $resourceId,
            'resource_name' => $resource['name'],
            'download_url' => $downloadUrl,
            'points_cost' => $pointsCost,
            'remaining_balance' => $currentBalance - $pointsCost,
            'file_size' => $resource['file_size'],
            'expires_in' => 3600 // 下载链接1小时有效
        ], API_SUCCESS_CODE, '获取下载链接成功');
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    // 记录错误日志
    writeLog("获取下载链接失败: " . $e->getMessage(), 'ERROR');
    
    // 返回错误响应
    handleError($e->getMessage(), API_ERROR_CODE);
}

/**
 * 生成下载链接
 */
function generateDownloadUrl($resource) {
    // 这里应该根据不同平台生成实际的下载链接
    // 目前返回模拟链接
    $baseUrl = SITE_URL . 'download/';
    $token = base64_encode($resource['platform'] . '|' . $resource['resource_id'] . '|' . time());
    
    return $baseUrl . '?token=' . $token;
}

?>
