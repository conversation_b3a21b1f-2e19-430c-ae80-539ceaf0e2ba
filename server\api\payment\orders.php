<?php
/**
 * 获取用户订单列表API
 * 版本: 1.0.0
 * 创建时间: 2024-12-19
 */

require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/User.php';
require_once '../../includes/Payment.php';

// 验证请求方法
validateRequestMethod(['GET']);

try {
    // 验证用户Token
    $token = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    if (empty($token)) {
        handleError('缺少授权令牌', API_AUTH_ERROR_CODE);
    }
    
    $user = new User();
    $userId = $user->validateToken($token);
    
    if (!$userId) {
        handleError('无效的授权令牌', API_AUTH_ERROR_CODE);
    }
    
    // 获取查询参数
    $page = max(1, (int)($_GET['page'] ?? 1));
    $limit = min(100, max(1, (int)($_GET['limit'] ?? 20)));
    $status = $_GET['status'] ?? null;
    
    // 验证订单状态
    if ($status && !in_array($status, ['pending', 'completed', 'failed', 'cancelled', 'expired'])) {
        handleError('无效的订单状态', API_ERROR_CODE);
    }
    
    // 获取用户订单列表
    $payment = new Payment($userId);
    $orders = $payment->getUserOrders($page, $limit, $status);
    
    // 记录查询日志
    writeLog("订单列表查询: 用户ID $userId, 页码 $page, 状态 " . ($status ?: '全部'), 'INFO');
    
    // 返回成功响应
    jsonResponse($orders, API_SUCCESS_CODE, '获取订单列表成功');
    
} catch (Exception $e) {
    // 记录错误日志
    writeLog("获取订单列表失败: " . $e->getMessage(), 'ERROR');
    
    // 返回错误响应
    handleError($e->getMessage(), API_AUTH_ERROR_CODE);
}

?>
