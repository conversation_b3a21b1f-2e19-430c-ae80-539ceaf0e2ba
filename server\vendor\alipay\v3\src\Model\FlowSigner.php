<?php
/**
 * FlowSigner
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * FlowSigner Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class FlowSigner implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'FlowSigner';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'orgThirdPartyUserId' => 'string',
        'signStatus' => 'int',
        'signerAccountType' => 'int',
        'thirdPartyUserId' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'orgThirdPartyUserId' => null,
        'signStatus' => null,
        'signerAccountType' => null,
        'thirdPartyUserId' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'orgThirdPartyUserId' => 'org_third_party_user_id',
        'signStatus' => 'sign_status',
        'signerAccountType' => 'signer_account_type',
        'thirdPartyUserId' => 'third_party_user_id'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'orgThirdPartyUserId' => 'setOrgThirdPartyUserId',
        'signStatus' => 'setSignStatus',
        'signerAccountType' => 'setSignerAccountType',
        'thirdPartyUserId' => 'setThirdPartyUserId'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'orgThirdPartyUserId' => 'getOrgThirdPartyUserId',
        'signStatus' => 'getSignStatus',
        'signerAccountType' => 'getSignerAccountType',
        'thirdPartyUserId' => 'getThirdPartyUserId'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['orgThirdPartyUserId'] = $data['orgThirdPartyUserId'] ?? null;
        $this->container['signStatus'] = $data['signStatus'] ?? null;
        $this->container['signerAccountType'] = $data['signerAccountType'] ?? null;
        $this->container['thirdPartyUserId'] = $data['thirdPartyUserId'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets orgThirdPartyUserId
     *
     * @return string|null
     */
    public function getOrgThirdPartyUserId()
    {
        return $this->container['orgThirdPartyUserId'];
    }

    /**
     * Sets orgThirdPartyUserId
     *
     * @param string|null $orgThirdPartyUserId 创建流程时指定的企业唯一标识，仅签署主体为企业时返回
     *
     * @return self
     */
    public function setOrgThirdPartyUserId($orgThirdPartyUserId)
    {
        $this->container['orgThirdPartyUserId'] = $orgThirdPartyUserId;

        return $this;
    }

    /**
     * Gets signStatus
     *
     * @return int|null
     */
    public function getSignStatus()
    {
        return $this->container['signStatus'];
    }

    /**
     * Sets signStatus
     *
     * @param int|null $signStatus 签署状态：0-待签, 1-未签, 2-已签 3-待审批 4-拒签
     *
     * @return self
     */
    public function setSignStatus($signStatus)
    {
        $this->container['signStatus'] = $signStatus;

        return $this;
    }

    /**
     * Gets signerAccountType
     *
     * @return int|null
     */
    public function getSignerAccountType()
    {
        return $this->container['signerAccountType'];
    }

    /**
     * Sets signerAccountType
     *
     * @param int|null $signerAccountType 签署主体类型：0-个人, 1-企业
     *
     * @return self
     */
    public function setSignerAccountType($signerAccountType)
    {
        $this->container['signerAccountType'] = $signerAccountType;

        return $this;
    }

    /**
     * Gets thirdPartyUserId
     *
     * @return string|null
     */
    public function getThirdPartyUserId()
    {
        return $this->container['thirdPartyUserId'];
    }

    /**
     * Sets thirdPartyUserId
     *
     * @param string|null $thirdPartyUserId 创建流程时指定个人唯一标识
     *
     * @return self
     */
    public function setThirdPartyUserId($thirdPartyUserId)
    {
        $this->container['thirdPartyUserId'] = $thirdPartyUserId;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


