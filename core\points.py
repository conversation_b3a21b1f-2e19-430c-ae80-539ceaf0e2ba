# -*- coding: utf-8 -*-
"""
积分管理模块
版本: 1.0.0
创建时间: 2024-12-19
"""

import json
from typing import Dict, List, Optional, Any
from .utils import config, logger, http_client

class PointsManager:
    """积分管理器"""
    
    def __init__(self, token: Optional[str] = None):
        self.token = token
        self.api_base = config.get('server.api_base')
        self.balance = 0
        self.account_info = {}
        
    def set_token(self, token: str):
        """设置用户令牌"""
        self.token = token
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        headers = {'Content-Type': 'application/json'}
        if self.token:
            headers['Authorization'] = f'Bearer {self.token}'
        return headers
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """发送API请求"""
        if not self.token:
            raise Exception("用户未登录，请先登录")
        
        url = f"{self.api_base}{endpoint}"
        headers = self._get_headers()
        
        try:
            if method.upper() == 'GET':
                response = http_client.get(url, headers=headers, params=data)
            else:
                response = http_client.post(url, headers=headers, json=data)
            
            result = response.json()
            
            if not result.get('success', False):
                raise Exception(result.get('message', '请求失败'))
            
            return result
            
        except Exception as e:
            logger.error(f"积分API请求失败: {e}")
            raise e
    
    def get_balance(self) -> Dict[str, Any]:
        """获取积分余额"""
        try:
            result = self._make_request('GET', 'points/balance.php')
            self.balance = result['data']['balance']
            self.account_info = result['data']
            
            logger.info(f"获取积分余额成功: {self.balance}")
            return result['data']
            
        except Exception as e:
            logger.error(f"获取积分余额失败: {e}")
            raise e
    
    def consume_points(self, points: int, resource_id: str, description: str = "下载资源") -> Dict[str, Any]:
        """消费积分"""
        if points <= 0:
            raise ValueError("消费积分必须大于0")
        
        try:
            data = {
                'points': points,
                'resource_id': resource_id,
                'description': description
            }
            
            result = self._make_request('POST', 'points/consume.php', data)
            
            # 更新本地余额
            self.balance = result['data']['balance']
            
            logger.info(f"积分消费成功: 消费 {points} 积分，余额 {self.balance}")
            return result['data']
            
        except Exception as e:
            logger.error(f"积分消费失败: {e}")
            raise e
    
    def refund_points(self, points: int, resource_id: str, description: str = "下载失败退还") -> Dict[str, Any]:
        """退还积分"""
        if points <= 0:
            raise ValueError("退还积分必须大于0")
        
        try:
            data = {
                'points': points,
                'resource_id': resource_id,
                'description': description
            }
            
            result = self._make_request('POST', 'points/refund.php', data)
            
            # 更新本地余额
            self.balance = result['data']['balance']
            
            logger.info(f"积分退还成功: 退还 {points} 积分，余额 {self.balance}")
            return result['data']
            
        except Exception as e:
            logger.error(f"积分退还失败: {e}")
            raise e
    
    def get_transaction_history(self, page: int = 1, limit: int = 20, 
                              transaction_type: Optional[str] = None) -> Dict[str, Any]:
        """获取积分交易历史"""
        try:
            params = {
                'page': page,
                'limit': limit
            }
            
            if transaction_type:
                params['type'] = transaction_type
            
            result = self._make_request('GET', 'points/history.php', params)
            
            logger.info(f"获取积分历史成功: 页码 {page}, 总数 {result['data']['total']}")
            return result['data']
            
        except Exception as e:
            logger.error(f"获取积分历史失败: {e}")
            raise e
    
    def check_sufficient_points(self, required_points: int) -> bool:
        """检查积分是否足够"""
        try:
            # 获取最新余额
            self.get_balance()
            return self.balance >= required_points
        except Exception:
            return False
    
    def format_transaction_type(self, transaction_type: str) -> str:
        """格式化交易类型"""
        type_map = {
            'recharge': '充值',
            'consume': '消费',
            'refund': '退还',
            'gift': '赠送',
            'admin': '管理员调整'
        }
        return type_map.get(transaction_type, transaction_type)
    
    def format_points(self, points: int) -> str:
        """格式化积分显示"""
        if points >= 0:
            return f"+{points}"
        else:
            return str(points)
    
    def get_statistics(self, days: int = 30) -> Dict[str, Any]:
        """获取积分统计信息"""
        try:
            # 获取账户信息
            account_info = self.get_balance()
            
            # 获取最近交易记录
            history = self.get_transaction_history(limit=100)
            
            # 计算统计数据
            recent_recharge = 0
            recent_consume = 0
            recent_transactions = 0
            
            for transaction in history['data']:
                recent_transactions += 1
                amount = transaction['amount']
                if amount > 0:
                    recent_recharge += amount
                else:
                    recent_consume += abs(amount)
            
            statistics = {
                'current_balance': account_info['balance'],
                'total_recharged': account_info.get('total_recharged', 0),
                'total_consumed': account_info.get('total_consumed', 0),
                'recent_recharge': recent_recharge,
                'recent_consume': recent_consume,
                'recent_transactions': recent_transactions,
                'last_updated': account_info.get('last_updated')
            }
            
            return statistics
            
        except Exception as e:
            logger.error(f"获取积分统计失败: {e}")
            return {
                'current_balance': 0,
                'total_recharged': 0,
                'total_consumed': 0,
                'recent_recharge': 0,
                'recent_consume': 0,
                'recent_transactions': 0,
                'last_updated': None
            }
    
    def get_current_balance(self) -> int:
        """获取当前积分余额（简化版）"""
        return self.balance
    
    def refresh_balance(self) -> int:
        """刷新积分余额"""
        try:
            self.get_balance()
            return self.balance
        except Exception:
            return self.balance

__all__ = ['PointsManager']
