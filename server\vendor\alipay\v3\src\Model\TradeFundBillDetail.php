<?php
/**
 * TradeFundBillDetail
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * TradeFundBillDetail Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class TradeFundBillDetail implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'TradeFundBillDetail';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'amount' => 'string',
        'assetTypeCode' => 'string',
        'assetUserId' => 'string',
        'assetUserOpenId' => 'string',
        'bizPayType' => 'string',
        'createTime' => 'string',
        'paymentNo' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'amount' => null,
        'assetTypeCode' => null,
        'assetUserId' => null,
        'assetUserOpenId' => null,
        'bizPayType' => null,
        'createTime' => null,
        'paymentNo' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'amount' => 'amount',
        'assetTypeCode' => 'asset_type_code',
        'assetUserId' => 'asset_user_id',
        'assetUserOpenId' => 'asset_user_open_id',
        'bizPayType' => 'biz_pay_type',
        'createTime' => 'create_time',
        'paymentNo' => 'payment_no'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'amount' => 'setAmount',
        'assetTypeCode' => 'setAssetTypeCode',
        'assetUserId' => 'setAssetUserId',
        'assetUserOpenId' => 'setAssetUserOpenId',
        'bizPayType' => 'setBizPayType',
        'createTime' => 'setCreateTime',
        'paymentNo' => 'setPaymentNo'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'amount' => 'getAmount',
        'assetTypeCode' => 'getAssetTypeCode',
        'assetUserId' => 'getAssetUserId',
        'assetUserOpenId' => 'getAssetUserOpenId',
        'bizPayType' => 'getBizPayType',
        'createTime' => 'getCreateTime',
        'paymentNo' => 'getPaymentNo'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['amount'] = $data['amount'] ?? null;
        $this->container['assetTypeCode'] = $data['assetTypeCode'] ?? null;
        $this->container['assetUserId'] = $data['assetUserId'] ?? null;
        $this->container['assetUserOpenId'] = $data['assetUserOpenId'] ?? null;
        $this->container['bizPayType'] = $data['bizPayType'] ?? null;
        $this->container['createTime'] = $data['createTime'] ?? null;
        $this->container['paymentNo'] = $data['paymentNo'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets amount
     *
     * @return string|null
     */
    public function getAmount()
    {
        return $this->container['amount'];
    }

    /**
     * Sets amount
     *
     * @param string|null $amount 交易金额
     *
     * @return self
     */
    public function setAmount($amount)
    {
        $this->container['amount'] = $amount;

        return $this;
    }

    /**
     * Gets assetTypeCode
     *
     * @return string|null
     */
    public function getAssetTypeCode()
    {
        return $this->container['assetTypeCode'];
    }

    /**
     * Sets assetTypeCode
     *
     * @param string|null $assetTypeCode 资产类型编码
     *
     * @return self
     */
    public function setAssetTypeCode($assetTypeCode)
    {
        $this->container['assetTypeCode'] = $assetTypeCode;

        return $this;
    }

    /**
     * Gets assetUserId
     *
     * @return string|null
     */
    public function getAssetUserId()
    {
        return $this->container['assetUserId'];
    }

    /**
     * Sets assetUserId
     *
     * @param string|null $assetUserId 资产的属主
     *
     * @return self
     */
    public function setAssetUserId($assetUserId)
    {
        $this->container['assetUserId'] = $assetUserId;

        return $this;
    }

    /**
     * Gets assetUserOpenId
     *
     * @return string|null
     */
    public function getAssetUserOpenId()
    {
        return $this->container['assetUserOpenId'];
    }

    /**
     * Sets assetUserOpenId
     *
     * @param string|null $assetUserOpenId 资产的属主openid
     *
     * @return self
     */
    public function setAssetUserOpenId($assetUserOpenId)
    {
        $this->container['assetUserOpenId'] = $assetUserOpenId;

        return $this;
    }

    /**
     * Gets bizPayType
     *
     * @return string|null
     */
    public function getBizPayType()
    {
        return $this->container['bizPayType'];
    }

    /**
     * Sets bizPayType
     *
     * @param string|null $bizPayType 支付业务类型  PAY-支付  REFUND-退款
     *
     * @return self
     */
    public function setBizPayType($bizPayType)
    {
        $this->container['bizPayType'] = $bizPayType;

        return $this;
    }

    /**
     * Gets createTime
     *
     * @return string|null
     */
    public function getCreateTime()
    {
        return $this->container['createTime'];
    }

    /**
     * Sets createTime
     *
     * @param string|null $createTime 创建时间
     *
     * @return self
     */
    public function setCreateTime($createTime)
    {
        $this->container['createTime'] = $createTime;

        return $this;
    }

    /**
     * Gets paymentNo
     *
     * @return string|null
     */
    public function getPaymentNo()
    {
        return $this->container['paymentNo'];
    }

    /**
     * Sets paymentNo
     *
     * @param string|null $paymentNo 支付单据号
     *
     * @return self
     */
    public function setPaymentNo($paymentNo)
    {
        $this->container['paymentNo'] = $paymentNo;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


