<?php
/**
 * WaitRepaymentOrderInfo
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * WaitRepaymentOrderInfo Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class WaitRepaymentOrderInfo implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'WaitRepaymentOrderInfo';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'advanceOrderId' => 'string',
        'alipayUserId' => 'string',
        'bizProduct' => 'string',
        'openId' => 'string',
        'origBizOrderId' => 'string',
        'waitRepaymentAmount' => 'int'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'advanceOrderId' => null,
        'alipayUserId' => null,
        'bizProduct' => null,
        'openId' => null,
        'origBizOrderId' => null,
        'waitRepaymentAmount' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'advanceOrderId' => 'advance_order_id',
        'alipayUserId' => 'alipay_user_id',
        'bizProduct' => 'biz_product',
        'openId' => 'open_id',
        'origBizOrderId' => 'orig_biz_order_id',
        'waitRepaymentAmount' => 'wait_repayment_amount'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'advanceOrderId' => 'setAdvanceOrderId',
        'alipayUserId' => 'setAlipayUserId',
        'bizProduct' => 'setBizProduct',
        'openId' => 'setOpenId',
        'origBizOrderId' => 'setOrigBizOrderId',
        'waitRepaymentAmount' => 'setWaitRepaymentAmount'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'advanceOrderId' => 'getAdvanceOrderId',
        'alipayUserId' => 'getAlipayUserId',
        'bizProduct' => 'getBizProduct',
        'openId' => 'getOpenId',
        'origBizOrderId' => 'getOrigBizOrderId',
        'waitRepaymentAmount' => 'getWaitRepaymentAmount'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['advanceOrderId'] = $data['advanceOrderId'] ?? null;
        $this->container['alipayUserId'] = $data['alipayUserId'] ?? null;
        $this->container['bizProduct'] = $data['bizProduct'] ?? null;
        $this->container['openId'] = $data['openId'] ?? null;
        $this->container['origBizOrderId'] = $data['origBizOrderId'] ?? null;
        $this->container['waitRepaymentAmount'] = $data['waitRepaymentAmount'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets advanceOrderId
     *
     * @return string|null
     */
    public function getAdvanceOrderId()
    {
        return $this->container['advanceOrderId'];
    }

    /**
     * Sets advanceOrderId
     *
     * @param string|null $advanceOrderId 垫资单id
     *
     * @return self
     */
    public function setAdvanceOrderId($advanceOrderId)
    {
        $this->container['advanceOrderId'] = $advanceOrderId;

        return $this;
    }

    /**
     * Gets alipayUserId
     *
     * @return string|null
     */
    public function getAlipayUserId()
    {
        return $this->container['alipayUserId'];
    }

    /**
     * Sets alipayUserId
     *
     * @param string|null $alipayUserId 买家的支付宝用户id
     *
     * @return self
     */
    public function setAlipayUserId($alipayUserId)
    {
        $this->container['alipayUserId'] = $alipayUserId;

        return $this;
    }

    /**
     * Gets bizProduct
     *
     * @return string|null
     */
    public function getBizProduct()
    {
        return $this->container['bizProduct'];
    }

    /**
     * Sets bizProduct
     *
     * @param string|null $bizProduct 通常为商户签约的收单产品码
     *
     * @return self
     */
    public function setBizProduct($bizProduct)
    {
        $this->container['bizProduct'] = $bizProduct;

        return $this;
    }

    /**
     * Gets openId
     *
     * @return string|null
     */
    public function getOpenId()
    {
        return $this->container['openId'];
    }

    /**
     * Sets openId
     *
     * @param string|null $openId 买家的支付宝用户id
     *
     * @return self
     */
    public function setOpenId($openId)
    {
        $this->container['openId'] = $openId;

        return $this;
    }

    /**
     * Gets origBizOrderId
     *
     * @return string|null
     */
    public function getOrigBizOrderId()
    {
        return $this->container['origBizOrderId'];
    }

    /**
     * Sets origBizOrderId
     *
     * @param string|null $origBizOrderId 原始的业务单号，通常为支付宝交易号
     *
     * @return self
     */
    public function setOrigBizOrderId($origBizOrderId)
    {
        $this->container['origBizOrderId'] = $origBizOrderId;

        return $this;
    }

    /**
     * Gets waitRepaymentAmount
     *
     * @return int|null
     */
    public function getWaitRepaymentAmount()
    {
        return $this->container['waitRepaymentAmount'];
    }

    /**
     * Sets waitRepaymentAmount
     *
     * @param int|null $waitRepaymentAmount 垫资金额
     *
     * @return self
     */
    public function setWaitRepaymentAmount($waitRepaymentAmount)
    {
        $this->container['waitRepaymentAmount'] = $waitRepaymentAmount;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


