<?php
/**
 * OperationApiTest
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.0.0
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Please update the test case below to test the endpoint.
 */

namespace Alipay\OpenAPISDK\Test\Api;

use Alipay\OpenAPISDK\Api\AlipayTradeApi;
use \Alipay\OpenAPISDK\Configuration;
use \Alipay\OpenAPISDK\ApiException;
use Alipay\OpenAPISDK\Model\AlipayTradePayModel;
use \Alipay\OpenAPISDK\ObjectSerializer;
use Ali<PERSON>y\OpenAPISDK\Util\AlipayConfigUtil;
use Alipay\OpenAPISDK\Util\AlipaySignature;
use Alipay\OpenAPISDK\Util\GenericExecuteApi;
use Ali<PERSON>y\OpenAPISDK\Util\Model\AlipayConfig;
use Alipay\OpenAPISDK\Util\Model\CustomizedParams;
use Alipay\OpenAPISDK\Util\Model\OpenApiGenericRequest;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use PHPUnit\Framework\TestCase;

/**
 * OperationApiTest Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */
class OperationApiTest extends TestCase
{
    private $apiInstance;
    /**
     * Setup before running any test cases
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
        $alipayConfig = new AlipayConfig();
        $alipayConfig->setServerUrl('https://openapipre.alipay.com');
        $alipayConfig->setAppId('2021003126691367');
        $alipayConfig->setPrivateKey('');
        $alipayConfig->setAlipayPublicKey('MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlr+2Mir816Ye8ybsC8QgIigrG2oqVTwYeCjiJQPGP0x7iRVVwQuuM54rO+OyyfJcvI5UWFtfcpy+si+3JyaInKP69DA0AS4XkVPV/81xcIHMZP/CJAYOtLFLhhyEwp1CMoN45BtJes5lG65UXz/7QzVjcF+6AC+qVKx66HKSsT9b5HyWRxfDkOYixy1gZt39w+sgxaAPVt6pf7UZHX+ENE/gYpTCHHflVPzPfl8TIXTZmEMGqjBLY8GFXVJkvATX3h12VX3AwUlIndY4gJND9l1isFV9BfpmZYyv864z14UH6Kq9u8WVWdF5bsJevo4oU0Q0UB8EmyOdlxBPTDiOzwIDAQAB');
//        $alipayConfig->setEncryptKey('');

//        $alipayConfig->setAppId('2021003156675030');
//        $alipayConfig->setPrivateKey('');
//        $alipayConfig->setAlipayPublicCertPath('test/cert/alipayCertPublicKey_RSA2.crt');
//        $alipayConfig->setAppCertPath('test/cert/appCertPublicKey_2021003156675030.crt');
//        $alipayConfig->setRootCertPath('test/cert/alipayRootCert.crt');
//        $alipayConfig->setEncryptKey('');


        $alipayConfigUtil = new AlipayConfigUtil($alipayConfig);
        $this->apiInstance = new AlipayTradeApi(
        // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
        // This is optional, `GuzzleHttp\Client` will be used as default.
            new Client()
        );
        $this->apiInstance->setAlipayConfigUtil($alipayConfigUtil);
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test case for query
     *
     * 测试路由转发closelyrzone.
     *
     */
    public function testQuery()
    {
        $customizedParams = new CustomizedParams();
        $customizedParams->setHeaderParams(["alipay-request-id"=>"222"]);

        $model = new AlipayTradePayModel();
        $model->setOutTradeNo('20210817010101001');
        $model->setTotalAmount('0.011');
        $model->setSubject('测试商品');
        $model->setScene('bar_code');
        $model->setAuthCode('28763443825664394');
        try {
            $result = $this->apiInstance->pay($model, $customizedParams);
            print_r($result);
        } catch (ApiException $e) {
            var_dump($e->getResponseBody());
            echo 'Exception when calling AlipayTradeApi->pay: ', $e->getMessage(), PHP_EOL;
            echo 'body: ', $e->getResponseBody(), PHP_EOL;
            echo 'header: ', $e->getResponseHeaders(), PHP_EOL;
        }
    }

    public function testPageExecute(){
        $execute = new GenericExecuteApi($this->apiInstance->getAlipayConfigUtil());
        $execute->setLoadTest(true);
        $customizedParams = new CustomizedParams();
        $queryParams = ['notify_url' => 'https://www.aaa.com',
            'return_url' => 'https://www.bbb.com'];
        $customizedParams->setQueryParams($queryParams);

//        $customizedParams->setBodyContent("{" .
//            "    \"body\":\"对一笔交易的具体描述信息。如果是多种商品，请将商品描述字符串累加传给body。\"," .
//            "    \"subject\":\"测试\"," .
//            "    \"out_trade_no\":\"70501111111S001111119\"," .
//            "    \"timeout_express\":\"90m\"," .
//            "    \"total_amount\":9.00," .
//            "    \"product_code\":\"QUICK_WAP_WAY\"" .
//            "  }");

        $bizParams = [];
        $otherParams = [];
        $otherParams['body'] = '对一笔交易的具体描述信息。如果是多种商品，请将商品描述字符串累加传给body。';
        $otherParams['out_trade_no'] = '70501111111S001111119';
        $otherParams['timeout_express'] = '90m';
        $otherParams['total_amount'] = 9.00;
        $otherParams['product_code'] = 'QUICK_WAP_WAY';
        $bizParams['biz_content'] = $otherParams;

//        $alipayMarketingActivityOrdervoucherAppendModel = new \Alipay\OpenAPISDK\Model\AlipayMarketingActivityOrdervoucherAppendModel(); // \Alipay\OpenAPISDK\Model\AlipayMarketingActivityOrdervoucherAppendModel
//        $alipayMarketingActivityOrdervoucherAppendModel->setOutBizNo('1231xxq12312ty111112xxxxqqewx');
//        $alipayMarketingActivityOrdervoucherAppendModel->setProductVersion('2.0.0');
//        $alipayMarketingActivityOrdervoucherAppendModel->setVoucherQuantity(15);
//        $bizParams['biz_content'] = $alipayMarketingActivityOrdervoucherAppendModel;

        $pageExecute = $execute->pageExecute("alipay.trade.wap.pay", "POST", $bizParams, "123sdc", null, $customizedParams);
        var_dump($pageExecute);

        $pageExecute2 = $execute->pageExecute("alipay.trade.wap.pay", "GET", $bizParams, "123sdc", null, $customizedParams);
        var_dump($pageExecute2);

        $sdkExecute = $execute->sdkExecute("alipay.trade.wap.pay", $bizParams, "123sdc", null, $customizedParams);
        var_dump($sdkExecute);
    }

    public function testExecute(){
        $execute = new GenericExecuteApi(
            $this->apiInstance->getAlipayConfigUtil(),
            new Client());
        $request = new OpenApiGenericRequest();

        $pathParams['activity_id'] = '123';
        $pathParams['voucher_code'] = '券码1';
        $request->setPathParams($pathParams);

        $queryParams['return_url'] = 'www.baidu.com';
        $request->setQueryParams($queryParams);

        $bizParams['merchant_access_mode'] = 'AGENCY_MODE';
        $bizParams['biz_dt'] = '2017-01-01 00:00:01';
        $bizParams['out_biz_no'] = '1002600620019090123143254436';
        $bizParams['total_fee'] = '999.99';
        $request->setBizParams($bizParams);

        $response = $execute->execute('/v3/alipay/marketing/activity/{activity_id}/ordervoucher/{voucher_code}/refund', 'POST', $request);
        var_dump($response);
    }

    public function testFileExecute(){
        $execute = new GenericExecuteApi(
            $this->apiInstance->getAlipayConfigUtil(),
            new Client());
        $request = new OpenApiGenericRequest();

        $queryParams['return_url'] = 'www.baidu.com';
        $request->setQueryParams($queryParams);

        $fileParams['app_logo'] = 'test/cert/pic.jpg';
        $request->setFileParams($fileParams);

        $bizParams['app_category_ids'] = '11_12;12_13';
        $bizParams['app_english_name'] = 'demoexample';
        $bizParams['app_name'] = '杭州支小宝潮流女装店';
        $bizParams['service_email'] = '<EMAIL>';
        $request->setBizParams($bizParams);

        try {
            $response = $execute->execute('/v3/alipay/open/mini/baseinfo/modify', 'POST', $request);
            var_dump($response);
        } catch (ApiException $e) {
            var_dump($e->getResponseBody());
        }
    }

    public function testSign()
    {
        $privateKey = "";
        $aloneRsaSign = AlipaySignature::aloneRsaSign("ceshi", $privateKey);
        var_dump($aloneRsaSign);

        $publicKey = '';
        $verify = AlipaySignature::verify("ceshi", $aloneRsaSign, $publicKey);
        var_dump($verify);

        $params = [];
        $params['method'] = 'koubei.marketing.data.indicator.query';
        $params['app_id'] = '201909036687xxx';
        $params['sign_type'] = 'RSA2';
        $params['sign'] = 'NQHIJKRCoCJCUXqzzK8WUtA/58m4/e3NGLutUFxFUD8udlauzRCgCINxDqs3atXrJ8KeSxpf8IU8nrnK7qzCQiYdj2XjfkXDrZllPEXlrelHitj9qpPqMXlAbP1HArNlBTpaGmq6hwvf/W50O/8kOjPEpmdpFcYZrlR/aA3uwdiOXHGNfd6p2wA7JjHiLQtPXGVm2oLLp+HDEtdhqxs66Br4bTQZPrQNKJrhqRcqwTzA5ZyDMQK724ECTYBI/+8PEvFCpi4Ny6piO78clFE/bSAuKCdhbivBzWDYkvmQymq0M86W3usljVENbnOyBatl7/y4f7S53Ht/yBmO/f+gYg==';
        $rsaCheckV1 = AlipaySignature::rsaCheckV2($params, $publicKey);
        var_dump($rsaCheckV1);
    }

}
