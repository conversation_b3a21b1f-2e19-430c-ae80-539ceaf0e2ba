<?php
/**
 * ZMGOBasicConfig
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * ZMGOBasicConfig Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class ZMGOBasicConfig implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'ZMGOBasicConfig';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'bizType' => 'string',
        'contact' => 'string',
        'isvPid' => 'string',
        'merchantCustomLogo' => 'string',
        'outBizNo' => 'string',
        'partnerId' => 'string',
        'templateName' => 'string',
        'templateNo' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'bizType' => null,
        'contact' => null,
        'isvPid' => null,
        'merchantCustomLogo' => null,
        'outBizNo' => null,
        'partnerId' => null,
        'templateName' => null,
        'templateNo' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'bizType' => 'biz_type',
        'contact' => 'contact',
        'isvPid' => 'isv_pid',
        'merchantCustomLogo' => 'merchant_custom_logo',
        'outBizNo' => 'out_biz_no',
        'partnerId' => 'partner_id',
        'templateName' => 'template_name',
        'templateNo' => 'template_no'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'bizType' => 'setBizType',
        'contact' => 'setContact',
        'isvPid' => 'setIsvPid',
        'merchantCustomLogo' => 'setMerchantCustomLogo',
        'outBizNo' => 'setOutBizNo',
        'partnerId' => 'setPartnerId',
        'templateName' => 'setTemplateName',
        'templateNo' => 'setTemplateNo'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'bizType' => 'getBizType',
        'contact' => 'getContact',
        'isvPid' => 'getIsvPid',
        'merchantCustomLogo' => 'getMerchantCustomLogo',
        'outBizNo' => 'getOutBizNo',
        'partnerId' => 'getPartnerId',
        'templateName' => 'getTemplateName',
        'templateNo' => 'getTemplateNo'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['bizType'] = $data['bizType'] ?? null;
        $this->container['contact'] = $data['contact'] ?? null;
        $this->container['isvPid'] = $data['isvPid'] ?? null;
        $this->container['merchantCustomLogo'] = $data['merchantCustomLogo'] ?? null;
        $this->container['outBizNo'] = $data['outBizNo'] ?? null;
        $this->container['partnerId'] = $data['partnerId'] ?? null;
        $this->container['templateName'] = $data['templateName'] ?? null;
        $this->container['templateNo'] = $data['templateNo'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets bizType
     *
     * @return string|null
     */
    public function getBizType()
    {
        return $this->container['bizType'];
    }

    /**
     * Sets bizType
     *
     * @param string|null $bizType 商户在芝麻GO配置的业务身份编码
     *
     * @return self
     */
    public function setBizType($bizType)
    {
        $this->container['bizType'] = $bizType;

        return $this;
    }

    /**
     * Gets contact
     *
     * @return string|null
     */
    public function getContact()
    {
        return $this->container['contact'];
    }

    /**
     * Sets contact
     *
     * @param string|null $contact 商家客服电话
     *
     * @return self
     */
    public function setContact($contact)
    {
        $this->container['contact'] = $contact;

        return $this;
    }

    /**
     * Gets isvPid
     *
     * @return string|null
     */
    public function getIsvPid()
    {
        return $this->container['isvPid'];
    }

    /**
     * Sets isvPid
     *
     * @param string|null $isvPid 运营商商户支付宝ID。若非ISV代理模式，也就是商户自运营模式，此属性取值与partner_id一致。
     *
     * @return self
     */
    public function setIsvPid($isvPid)
    {
        $this->container['isvPid'] = $isvPid;

        return $this;
    }

    /**
     * Gets merchantCustomLogo
     *
     * @return string|null
     */
    public function getMerchantCustomLogo()
    {
        return $this->container['merchantCustomLogo'];
    }

    /**
     * Sets merchantCustomLogo
     *
     * @param string|null $merchantCustomLogo 商户LOGO
     *
     * @return self
     */
    public function setMerchantCustomLogo($merchantCustomLogo)
    {
        $this->container['merchantCustomLogo'] = $merchantCustomLogo;

        return $this;
    }

    /**
     * Gets outBizNo
     *
     * @return string|null
     */
    public function getOutBizNo()
    {
        return $this->container['outBizNo'];
    }

    /**
     * Sets outBizNo
     *
     * @param string|null $outBizNo 外部业务单号，供幂等使用，需保证每次请求的值都不同
     *
     * @return self
     */
    public function setOutBizNo($outBizNo)
    {
        $this->container['outBizNo'] = $outBizNo;

        return $this;
    }

    /**
     * Gets partnerId
     *
     * @return string|null
     */
    public function getPartnerId()
    {
        return $this->container['partnerId'];
    }

    /**
     * Sets partnerId
     *
     * @param string|null $partnerId 商户的支付宝ID，即为此商户创建芝麻GO模板
     *
     * @return self
     */
    public function setPartnerId($partnerId)
    {
        $this->container['partnerId'] = $partnerId;

        return $this;
    }

    /**
     * Gets templateName
     *
     * @return string|null
     */
    public function getTemplateName()
    {
        return $this->container['templateName'];
    }

    /**
     * Sets templateName
     *
     * @param string|null $templateName 芝麻GO模板名称
     *
     * @return self
     */
    public function setTemplateName($templateName)
    {
        $this->container['templateName'] = $templateName;

        return $this;
    }

    /**
     * Gets templateNo
     *
     * @return string|null
     */
    public function getTemplateNo()
    {
        return $this->container['templateNo'];
    }

    /**
     * Sets templateNo
     *
     * @param string|null $templateNo 模板编号
     *
     * @return self
     */
    public function setTemplateNo($templateNo)
    {
        $this->container['templateNo'] = $templateNo;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


