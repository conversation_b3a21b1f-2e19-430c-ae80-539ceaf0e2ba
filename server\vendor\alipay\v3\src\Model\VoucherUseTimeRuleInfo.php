<?php
/**
 * VoucherUseTimeRuleInfo
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * VoucherUseTimeRuleInfo Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class VoucherUseTimeRuleInfo implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'VoucherUseTimeRuleInfo';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'dateRuleInfo' => '\Alipay\OpenAPISDK\Model\DateRuleInfo',
        'holidayRuleInfo' => '\Alipay\OpenAPISDK\Model\HolidayRuleInfo',
        'ruleType' => 'string',
        'weekRuleInfo' => '\Alipay\OpenAPISDK\Model\WeekRuleInfo'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'dateRuleInfo' => null,
        'holidayRuleInfo' => null,
        'ruleType' => null,
        'weekRuleInfo' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'dateRuleInfo' => 'date_rule_info',
        'holidayRuleInfo' => 'holiday_rule_info',
        'ruleType' => 'rule_type',
        'weekRuleInfo' => 'week_rule_info'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'dateRuleInfo' => 'setDateRuleInfo',
        'holidayRuleInfo' => 'setHolidayRuleInfo',
        'ruleType' => 'setRuleType',
        'weekRuleInfo' => 'setWeekRuleInfo'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'dateRuleInfo' => 'getDateRuleInfo',
        'holidayRuleInfo' => 'getHolidayRuleInfo',
        'ruleType' => 'getRuleType',
        'weekRuleInfo' => 'getWeekRuleInfo'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['dateRuleInfo'] = $data['dateRuleInfo'] ?? null;
        $this->container['holidayRuleInfo'] = $data['holidayRuleInfo'] ?? null;
        $this->container['ruleType'] = $data['ruleType'] ?? null;
        $this->container['weekRuleInfo'] = $data['weekRuleInfo'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets dateRuleInfo
     *
     * @return \Alipay\OpenAPISDK\Model\DateRuleInfo|null
     */
    public function getDateRuleInfo()
    {
        return $this->container['dateRuleInfo'];
    }

    /**
     * Sets dateRuleInfo
     *
     * @param \Alipay\OpenAPISDK\Model\DateRuleInfo|null $dateRuleInfo dateRuleInfo
     *
     * @return self
     */
    public function setDateRuleInfo($dateRuleInfo)
    {
        $this->container['dateRuleInfo'] = $dateRuleInfo;

        return $this;
    }

    /**
     * Gets holidayRuleInfo
     *
     * @return \Alipay\OpenAPISDK\Model\HolidayRuleInfo|null
     */
    public function getHolidayRuleInfo()
    {
        return $this->container['holidayRuleInfo'];
    }

    /**
     * Sets holidayRuleInfo
     *
     * @param \Alipay\OpenAPISDK\Model\HolidayRuleInfo|null $holidayRuleInfo holidayRuleInfo
     *
     * @return self
     */
    public function setHolidayRuleInfo($holidayRuleInfo)
    {
        $this->container['holidayRuleInfo'] = $holidayRuleInfo;

        return $this;
    }

    /**
     * Gets ruleType
     *
     * @return string|null
     */
    public function getRuleType()
    {
        return $this->container['ruleType'];
    }

    /**
     * Sets ruleType
     *
     * @param string|null $ruleType 规则类型
     *
     * @return self
     */
    public function setRuleType($ruleType)
    {
        $this->container['ruleType'] = $ruleType;

        return $this;
    }

    /**
     * Gets weekRuleInfo
     *
     * @return \Alipay\OpenAPISDK\Model\WeekRuleInfo|null
     */
    public function getWeekRuleInfo()
    {
        return $this->container['weekRuleInfo'];
    }

    /**
     * Sets weekRuleInfo
     *
     * @param \Alipay\OpenAPISDK\Model\WeekRuleInfo|null $weekRuleInfo weekRuleInfo
     *
     * @return self
     */
    public function setWeekRuleInfo($weekRuleInfo)
    {
        $this->container['weekRuleInfo'] = $weekRuleInfo;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


