<?php
/**
 * MdCodeInfoDTO
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * MdCodeInfoDTO Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class MdCodeInfoDTO implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'MdCodeInfoDTO';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'codeStatus' => 'string',
        'codeValue' => 'string',
        'expireTime' => 'string',
        'timeStamp' => 'int'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'codeStatus' => null,
        'codeValue' => null,
        'expireTime' => null,
        'timeStamp' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'codeStatus' => 'code_status',
        'codeValue' => 'code_value',
        'expireTime' => 'expire_time',
        'timeStamp' => 'time_stamp'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'codeStatus' => 'setCodeStatus',
        'codeValue' => 'setCodeValue',
        'expireTime' => 'setExpireTime',
        'timeStamp' => 'setTimeStamp'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'codeStatus' => 'getCodeStatus',
        'codeValue' => 'getCodeValue',
        'expireTime' => 'getExpireTime',
        'timeStamp' => 'getTimeStamp'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['codeStatus'] = $data['codeStatus'] ?? null;
        $this->container['codeValue'] = $data['codeValue'] ?? null;
        $this->container['expireTime'] = $data['expireTime'] ?? null;
        $this->container['timeStamp'] = $data['timeStamp'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets codeStatus
     *
     * @return string|null
     */
    public function getCodeStatus()
    {
        return $this->container['codeStatus'];
    }

    /**
     * Sets codeStatus
     *
     * @param string|null $codeStatus 本次回传动态码的状态：  SUCCESS: 本次发码成功  FAIL_RETRY: 本次发码失败，且需要支付宝重试（重新通知商户发码）  FAIL_NOT_RETRY: 本次发码失败，且无需支付宝重试（无需重新通知商户发码）
     *
     * @return self
     */
    public function setCodeStatus($codeStatus)
    {
        $this->container['codeStatus'] = $codeStatus;

        return $this;
    }

    /**
     * Gets codeValue
     *
     * @return string|null
     */
    public function getCodeValue()
    {
        return $this->container['codeValue'];
    }

    /**
     * Sets codeValue
     *
     * @param string|null $codeValue 动态码的码值：  code_status为SUCCESS时必填；  基于此码值生成条形码或二维码用于扫码核销。
     *
     * @return self
     */
    public function setCodeValue($codeValue)
    {
        $this->container['codeValue'] = $codeValue;

        return $this;
    }

    /**
     * Gets expireTime
     *
     * @return string|null
     */
    public function getExpireTime()
    {
        return $this->container['expireTime'];
    }

    /**
     * Sets expireTime
     *
     * @param string|null $expireTime 当前动态码的过期（失效）时间：   code_status为SUCCESS时必填。
     *
     * @return self
     */
    public function setExpireTime($expireTime)
    {
        $this->container['expireTime'] = $expireTime;

        return $this;
    }

    /**
     * Gets timeStamp
     *
     * @return int|null
     */
    public function getTimeStamp()
    {
        return $this->container['timeStamp'];
    }

    /**
     * Sets timeStamp
     *
     * @param int|null $timeStamp 商户回传动态码的时间戳 (单位秒)。    即商户调接口回传动态码时刻对应的long类型时间戳，用于区分不同的发码请求。
     *
     * @return self
     */
    public function setTimeStamp($timeStamp)
    {
        $this->container['timeStamp'] = $timeStamp;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


