<?php
/**
 * 用户登出API
 * 版本: 1.0.0
 * 创建时间: 2024-12-19
 */

require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/User.php';

// 验证请求方法
validateRequestMethod(['POST']);

try {
    // 验证用户Token
    $token = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    if (empty($token)) {
        handleError('缺少授权令牌', API_AUTH_ERROR_CODE);
    }
    
    $user = new User();
    $userId = $user->validateToken($token);
    
    if (!$userId) {
        handleError('无效的授权令牌', API_AUTH_ERROR_CODE);
    }
    
    // 执行登出操作
    $result = $user->logout($userId);
    
    if ($result) {
        // 记录登出日志
        writeLog("用户登出成功: 用户ID $userId", 'INFO');
        
        // 返回成功响应
        jsonResponse(null, API_SUCCESS_CODE, '登出成功');
    } else {
        handleError('登出失败', API_ERROR_CODE);
    }
    
} catch (Exception $e) {
    // 记录错误日志
    writeLog("用户登出失败: " . $e->getMessage(), 'ERROR');
    
    // 返回错误响应
    handleError($e->getMessage(), API_ERROR_CODE);
}

?>
