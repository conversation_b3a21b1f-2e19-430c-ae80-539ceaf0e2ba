# 平台下载器模块
# 版本: 1.0.0

import os
import importlib
from typing import Dict, List, Type
from core.downloader import BaseDownloader

class PlatformManager:
    """平台管理器，负责动态加载各平台下载器"""
    
    def __init__(self):
        self.platforms: Dict[str, Type[BaseDownloader]] = {}
        self._load_platforms()
    
    def _load_platforms(self):
        """动态加载所有平台下载器"""
        platforms_dir = os.path.dirname(__file__)
        
        for item in os.listdir(platforms_dir):
            platform_path = os.path.join(platforms_dir, item)
            
            # 跳过非目录和特殊目录
            if not os.path.isdir(platform_path) or item.startswith('__'):
                continue
                
            try:
                # 动态导入平台模块
                module = importlib.import_module(f'platforms.{item}')
                
                # 查找下载器类
                for attr_name in dir(module):
                    attr = getattr(module, attr_name)
                    if (isinstance(attr, type) and 
                        issubclass(attr, BaseDownloader) and 
                        attr != BaseDownloader):
                        self.platforms[item] = attr
                        break
                        
            except ImportError as e:
                print(f"警告: 无法加载平台 {item}: {e}")
    
    def get_platform(self, platform_name: str) -> Type[BaseDownloader]:
        """获取指定平台的下载器类"""
        return self.platforms.get(platform_name)
    
    def list_platforms(self) -> List[str]:
        """获取所有可用平台列表"""
        return list(self.platforms.keys())
    
    def is_platform_available(self, platform_name: str) -> bool:
        """检查平台是否可用"""
        return platform_name in self.platforms

# 全局平台管理器实例
platform_manager = PlatformManager()

__all__ = ['platform_manager', 'PlatformManager']
