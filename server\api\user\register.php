<?php
/**
 * 用户注册API
 * 版本: 1.0.0
 * 创建时间: 2024-12-19
 */

require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/User.php';

// 验证请求方法
validateRequestMethod(['POST']);

try {
    // 获取请求数据
    $data = getRequestData();
    
    // 验证必需参数
    if (empty($data['username'])) {
        handleError('用户名不能为空', API_ERROR_CODE);
    }
    
    if (empty($data['password'])) {
        handleError('密码不能为空', API_ERROR_CODE);
    }
    
    // 验证用户名格式
    if (!preg_match('/^[a-zA-Z0-9_]{3,20}$/', $data['username'])) {
        handleError('用户名只能包含字母、数字和下划线，长度3-20位', API_ERROR_CODE);
    }
    
    // 验证密码强度
    if (strlen($data['password']) < 6) {
        handleError('密码长度不能少于6位', API_ERROR_CODE);
    }
    
    // 验证邮箱格式（如果提供）
    $email = null;
    if (!empty($data['email'])) {
        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            handleError('邮箱格式不正确', API_ERROR_CODE);
        }
        $email = $data['email'];
    }
    
    // 创建用户实例并注册
    $user = new User();
    $result = $user->register($data['username'], $data['password'], $email);
    
    // 记录注册日志
    writeLog("用户注册成功: {$data['username']}", 'INFO');
    
    // 返回成功响应
    jsonResponse([
        'user_id' => $result['user_id'],
        'username' => $data['username'],
        'email' => $email,
        'register_gift_points' => DEFAULT_REGISTER_GIFT_POINTS
    ], API_SUCCESS_CODE, '注册成功');
    
} catch (Exception $e) {
    // 记录错误日志
    writeLog("用户注册失败: " . $e->getMessage(), 'ERROR');
    
    // 返回错误响应
    handleError($e->getMessage(), API_ERROR_CODE);
}

?>
