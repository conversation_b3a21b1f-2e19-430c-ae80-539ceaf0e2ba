<?php
/**
 * 积分管理类
 * 版本: 1.0.0
 * 创建时间: 2024-12-19
 */

require_once 'database.php';

class Points {
    private $db;
    private $userId;

    public function __construct($userId) {
        $this->db = getDB();
        $this->userId = $userId;
        
        // 确保用户有积分账户
        $this->ensurePointsAccount();
    }

    /**
     * 确保用户有积分账户
     */
    private function ensurePointsAccount() {
        if (!$this->db->exists('points', ['user_id' => $this->userId])) {
            $this->db->insertData('points', [
                'user_id' => $this->userId,
                'balance' => 0
            ]);
        }
    }

    /**
     * 获取积分余额
     */
    public function getBalance() {
        $result = $this->db->selectOne('points', ['user_id' => $this->userId]);
        return $result ? (int)$result['balance'] : 0;
    }

    /**
     * 获取积分账户详细信息
     */
    public function getAccountInfo() {
        $result = $this->db->selectOne('points', ['user_id' => $this->userId]);
        
        if (!$result) {
            return [
                'balance' => 0,
                'total_recharged' => 0,
                'total_consumed' => 0,
                'last_updated' => null
            ];
        }

        return [
            'balance' => (int)$result['balance'],
            'total_recharged' => (int)$result['total_recharged'],
            'total_consumed' => (int)$result['total_consumed'],
            'last_updated' => $result['last_updated']
        ];
    }

    /**
     * 消费积分
     */
    public function consumePoints($amount, $resourceId = null, $description = '下载资源') {
        if ($amount <= 0) {
            throw new Exception('消费积分必须大于0');
        }

        try {
            $this->db->beginTransaction();

            // 获取当前余额
            $currentBalance = $this->getBalance();
            
            if ($currentBalance < $amount) {
                throw new Exception('积分余额不足');
            }

            $newBalance = $currentBalance - $amount;

            // 更新积分余额
            $this->db->updateData('points', [
                'balance' => $newBalance,
                'total_consumed' => $this->db->fetchOne(
                    'SELECT total_consumed FROM points WHERE user_id = ?', 
                    [$this->userId]
                )['total_consumed'] + $amount
            ], ['user_id' => $this->userId]);

            // 记录交易
            $this->recordTransaction(-$amount, 'consume', $resourceId, null, $description, $currentBalance, $newBalance);

            $this->db->commit();

            writeLog("积分消费成功: 用户ID {$this->userId}, 消费 {$amount} 积分", 'INFO');

            return [
                'success' => true,
                'balance' => $newBalance,
                'consumed' => $amount,
                'message' => '积分消费成功'
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            writeLog("积分消费失败: " . $e->getMessage(), 'ERROR');
            throw $e;
        }
    }

    /**
     * 充值积分
     */
    public function rechargePoints($amount, $orderId, $description = '积分充值') {
        if ($amount <= 0) {
            throw new Exception('充值积分必须大于0');
        }

        try {
            // 检查是否已经在事务中，如果不在则开启新事务
            $needTransaction = !$this->db->inTransaction();

            if ($needTransaction) {
                $this->db->beginTransaction();
            }

            // 获取当前余额
            $currentBalance = $this->getBalance();
            $newBalance = $currentBalance + $amount;

            // 更新积分余额
            $this->db->updateData('points', [
                'balance' => $newBalance,
                'total_recharged' => $this->db->fetchOne(
                    'SELECT total_recharged FROM points WHERE user_id = ?',
                    [$this->userId]
                )['total_recharged'] + $amount
            ], ['user_id' => $this->userId]);

            // 记录交易
            $this->recordTransaction($amount, 'recharge', null, $orderId, $description, $currentBalance, $newBalance);

            // 只有当我们开启了事务时才提交
            if ($needTransaction) {
                $this->db->commit();
            }

            writeLog("积分充值成功: 用户ID {$this->userId}, 充值 {$amount} 积分", 'INFO');

            return [
                'success' => true,
                'balance' => $newBalance,
                'recharged' => $amount,
                'message' => '积分充值成功'
            ];

        } catch (Exception $e) {
            // 只有当我们开启了事务时才回滚
            if ($needTransaction && $this->db->inTransaction()) {
                $this->db->rollback();
            }
            writeLog("积分充值失败: " . $e->getMessage(), 'ERROR');
            throw $e;
        }
    }

    /**
     * 退还积分
     */
    public function refundPoints($amount, $resourceId = null, $description = '积分退还') {
        if ($amount <= 0) {
            throw new Exception('退还积分必须大于0');
        }

        try {
            $this->db->beginTransaction();

            // 获取当前余额
            $currentBalance = $this->getBalance();
            $newBalance = $currentBalance + $amount;

            // 更新积分余额
            $this->db->updateData('points', [
                'balance' => $newBalance,
                'total_consumed' => max(0, $this->db->fetchOne(
                    'SELECT total_consumed FROM points WHERE user_id = ?', 
                    [$this->userId]
                )['total_consumed'] - $amount)
            ], ['user_id' => $this->userId]);

            // 记录交易
            $this->recordTransaction($amount, 'refund', $resourceId, null, $description, $currentBalance, $newBalance);

            $this->db->commit();

            writeLog("积分退还成功: 用户ID {$this->userId}, 退还 {$amount} 积分", 'INFO');

            return [
                'success' => true,
                'balance' => $newBalance,
                'refunded' => $amount,
                'message' => '积分退还成功'
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            writeLog("积分退还失败: " . $e->getMessage(), 'ERROR');
            throw $e;
        }
    }

    /**
     * 管理员调整积分
     */
    public function adjustPoints($amount, $description = '管理员调整') {
        if ($amount == 0) {
            throw new Exception('调整积分不能为0');
        }

        try {
            $this->db->beginTransaction();

            // 获取当前余额
            $currentBalance = $this->getBalance();
            $newBalance = $currentBalance + $amount;

            if ($newBalance < 0) {
                throw new Exception('调整后积分余额不能为负数');
            }

            // 更新积分余额
            $updateData = ['balance' => $newBalance];
            
            if ($amount > 0) {
                $updateData['total_recharged'] = $this->db->fetchOne(
                    'SELECT total_recharged FROM points WHERE user_id = ?', 
                    [$this->userId]
                )['total_recharged'] + $amount;
            }

            $this->db->updateData('points', $updateData, ['user_id' => $this->userId]);

            // 记录交易
            $this->recordTransaction($amount, 'admin', null, null, $description, $currentBalance, $newBalance);

            $this->db->commit();

            writeLog("管理员积分调整: 用户ID {$this->userId}, 调整 {$amount} 积分", 'INFO');

            return [
                'success' => true,
                'balance' => $newBalance,
                'adjusted' => $amount,
                'message' => '积分调整成功'
            ];

        } catch (Exception $e) {
            $this->db->rollback();
            writeLog("积分调整失败: " . $e->getMessage(), 'ERROR');
            throw $e;
        }
    }

    /**
     * 获取积分交易历史
     */
    public function getTransactionHistory($page = 1, $limit = 20, $type = null) {
        $offset = ($page - 1) * $limit;
        
        $conditions = ['user_id' => $this->userId];
        if ($type) {
            $conditions['transaction_type'] = $type;
        }

        // 获取总数
        $total = $this->db->count('point_transactions', $conditions);

        // 获取交易记录
        $sql = "SELECT * FROM point_transactions WHERE user_id = ?";
        $params = [$this->userId];

        if ($type) {
            $sql .= " AND transaction_type = ?";
            $params[] = $type;
        }

        $sql .= " ORDER BY created_at DESC LIMIT $limit OFFSET $offset";

        $transactions = $this->db->fetchAll($sql, $params);

        return [
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit),
            'data' => $transactions
        ];
    }

    /**
     * 检查是否有足够积分
     */
    public function hasEnoughPoints($amount) {
        return $this->getBalance() >= $amount;
    }

    /**
     * 记录积分交易
     */
    private function recordTransaction($amount, $type, $resourceId = null, $orderId = null, $description = '', $balanceBefore = null, $balanceAfter = null) {
        if ($balanceBefore === null) {
            $balanceBefore = $this->getBalance();
        }
        
        if ($balanceAfter === null) {
            $balanceAfter = $balanceBefore + $amount;
        }

        $transactionData = [
            'user_id' => $this->userId,
            'amount' => $amount,
            'transaction_type' => $type,
            'resource_id' => $resourceId,
            'order_id' => $orderId,
            'description' => $description,
            'balance_before' => $balanceBefore,
            'balance_after' => $balanceAfter,
            'ip_address' => getClientIP()
        ];

        return $this->db->insertData('point_transactions', $transactionData);
    }

    /**
     * 获取积分统计信息
     */
    public function getStatistics($days = 30) {
        $startDate = date('Y-m-d H:i:s', time() - $days * 24 * 3600);

        // 获取指定天数内的统计
        $stats = $this->db->fetchOne("
            SELECT 
                SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as total_income,
                SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as total_expense,
                COUNT(*) as total_transactions
            FROM point_transactions 
            WHERE user_id = ? AND created_at >= ?
        ", [$this->userId, $startDate]);

        return [
            'current_balance' => $this->getBalance(),
            'total_income' => (int)($stats['total_income'] ?? 0),
            'total_expense' => (int)($stats['total_expense'] ?? 0),
            'total_transactions' => (int)($stats['total_transactions'] ?? 0),
            'period_days' => $days
        ];
    }

    /**
     * 获取用户ID
     */
    public function getUserId() {
        return $this->userId;
    }
}

?>
