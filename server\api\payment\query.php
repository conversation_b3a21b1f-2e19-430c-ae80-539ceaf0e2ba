<?php
/**
 * 查询订单状态API
 * 版本: 1.0.0
 * 创建时间: 2024-12-19
 */

require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/User.php';
require_once '../../includes/Payment.php';

// 验证请求方法
validateRequestMethod(['GET', 'POST']);

try {
    // 验证用户Token
    $token = $_SERVER['HTTP_AUTHORIZATION'] ?? '';

    // 记录调试信息
    writeLog("查询订单API调试: 请求方法={$_SERVER['REQUEST_METHOD']}, Authorization=" . ($token ? '存在' : '缺失'), 'DEBUG');

    if (empty($token)) {
        handleError('缺少授权令牌', API_AUTH_ERROR_CODE);
    }
    
    $user = new User();
    $userId = $user->validateToken($token);
    
    if (!$userId) {
        handleError('无效的授权令牌', API_AUTH_ERROR_CODE);
    }
    
    // 获取订单号
    $orderNumber = $_GET['order_number'] ?? $_POST['order_number'] ?? '';
    
    if (empty($orderNumber)) {
        handleError('订单号不能为空', API_ERROR_CODE);
    }
    
    // 验证订单号格式
    if (!preg_match('/^PD\d{18}$/', $orderNumber)) {
        handleError('订单号格式不正确', API_ERROR_CODE);
    }
    
    // 验证订单所有权
    $db = getDB();
    $order = $db->selectOne('orders', [
        'order_number' => $orderNumber,
        'user_id' => $userId
    ]);
    
    if (!$order) {
        handleError('订单不存在或无权限访问', API_FORBIDDEN_CODE);
    }
    
    // 查询订单状态
    $payment = new Payment($userId);
    $result = $payment->queryOrderStatus($orderNumber);
    
    // 记录查询日志
    writeLog("订单状态查询: 用户ID $userId, 订单号 $orderNumber, 状态 {$result['status']}", 'INFO');
    
    // 返回成功响应
    jsonResponse($result, API_SUCCESS_CODE, '查询订单状态成功');
    
} catch (Exception $e) {
    // 记录错误日志
    writeLog("查询订单状态失败: " . $e->getMessage(), 'ERROR');
    
    // 返回错误响应
    handleError($e->getMessage(), API_ERROR_CODE);
}

?>
