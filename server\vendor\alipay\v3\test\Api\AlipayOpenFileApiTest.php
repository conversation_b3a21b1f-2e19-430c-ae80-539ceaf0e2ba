<?php
/**
 * AlipayOpenFileApiTest
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 1.0
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.0.0
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Please update the test case below to test the endpoint.
 */

namespace Alipay\OpenAPISDK\Test\Api;

use Alipay\OpenAPISDK\Api\AlipayOpenFileApi;
use \Alipay\OpenAPISDK\Configuration;
use \Alipay\OpenAPISDK\ApiException;
use Alipay\OpenAPISDK\Model\AlipayOpenFileUploadModel;
use \Alipay\OpenAPISDK\ObjectSerializer;
use <PERSON><PERSON>y\OpenAPISDK\Util\AlipayConfigUtil;
use Ali<PERSON>y\OpenAPISDK\Util\Model\AlipayConfig;
use GuzzleHttp\Client;
use PHPUnit\Framework\TestCase;

/**
 * AlipayOpenFileApiTest Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */
class AlipayOpenFileApiTest extends TestCase
{
    private $apiInstance;
    /**
     * Setup before running any test cases
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
        $alipayConfig = new AlipayConfig();

        $alipayConfig->setAppId('2021003126677278');
        $alipayConfig->setPrivateKey('');
        $alipayConfig->setAlipayPublicKey('MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlr+2Mir816Ye8ybsC8QgIigrG2oqVTwYeCjiJQPGP0x7iRVVwQuuM54rO+OyyfJcvI5UWFtfcpy+si+3JyaInKP69DA0AS4XkVPV/81xcIHMZP/CJAYOtLFLhhyEwp1CMoN45BtJes5lG65UXz/7QzVjcF+6AC+qVKx66HKSsT9b5HyWRxfDkOYixy1gZt39w+sgxaAPVt6pf7UZHX+ENE/gYpTCHHflVPzPfl8TIXTZmEMGqjBLY8GFXVJkvATX3h12VX3AwUlIndY4gJND9l1isFV9BfpmZYyv864z14UH6Kq9u8WVWdF5bsJevo4oU0Q0UB8EmyOdlxBPTDiOzwIDAQAB');
        $alipayConfig->setEncryptKey('');

//        $alipayConfig->setAppId('2021003156675030');
//        $alipayConfig->setPrivateKey('');
//        $alipayConfig->setAlipayPublicCertPath('test/cert/alipayCertPublicKey_RSA2.crt');
//        $alipayConfig->setAppCertPath('test/cert/appCertPublicKey_2021003156675030.crt');
//        $alipayConfig->setRootCertPath('test/cert/alipayRootCert.crt');
//        $alipayConfig->setEncryptKey('');


        $alipayConfigUtil = new AlipayConfigUtil($alipayConfig);
//        $this->apiInstance = new AlipayOpenFileApi(
//        // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
//        // This is optional, `GuzzleHttp\Client` will be used as default.
//            new Client()
//        );
//        $this->apiInstance->setAlipayConfigUtil($alipayConfigUtil);
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test case for upload
     *
     * 支付宝文件上传接口.
     *
     */
    public function testUpload()
    {
//        $model = new AlipayOpenFileUploadModel();
//        $model->setBizCode("content_creation");
//        $this->apiInstance->upload(null,"", $model, "test/cert/pic.jpg");
        // TODO: implement
//        $this->markTestIncomplete('Not implemented');
    }
}
