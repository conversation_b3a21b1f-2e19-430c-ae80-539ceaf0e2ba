<?php
/**
 * SearchBaseOrderCreateApiRequest
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * SearchBaseOrderCreateApiRequest Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class SearchBaseOrderCreateApiRequest implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'SearchBaseOrderCreateApiRequest';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'accessType' => 'string',
        'appid' => 'string',
        'baseItems' => '\Alipay\OpenAPISDK\Model\SearchBaseItems',
        'descprise' => 'string',
        'isDraft' => 'bool',
        'orderId' => 'string',
        'specCode' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'accessType' => null,
        'appid' => null,
        'baseItems' => null,
        'descprise' => null,
        'isDraft' => null,
        'orderId' => null,
        'specCode' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'accessType' => 'access_type',
        'appid' => 'appid',
        'baseItems' => 'base_items',
        'descprise' => 'descprise',
        'isDraft' => 'is_draft',
        'orderId' => 'order_id',
        'specCode' => 'spec_code'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'accessType' => 'setAccessType',
        'appid' => 'setAppid',
        'baseItems' => 'setBaseItems',
        'descprise' => 'setDescprise',
        'isDraft' => 'setIsDraft',
        'orderId' => 'setOrderId',
        'specCode' => 'setSpecCode'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'accessType' => 'getAccessType',
        'appid' => 'getAppid',
        'baseItems' => 'getBaseItems',
        'descprise' => 'getDescprise',
        'isDraft' => 'getIsDraft',
        'orderId' => 'getOrderId',
        'specCode' => 'getSpecCode'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['accessType'] = $data['accessType'] ?? null;
        $this->container['appid'] = $data['appid'] ?? null;
        $this->container['baseItems'] = $data['baseItems'] ?? null;
        $this->container['descprise'] = $data['descprise'] ?? null;
        $this->container['isDraft'] = $data['isDraft'] ?? null;
        $this->container['orderId'] = $data['orderId'] ?? null;
        $this->container['specCode'] = $data['specCode'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets accessType
     *
     * @return string|null
     */
    public function getAccessType()
    {
        return $this->container['accessType'];
    }

    /**
     * Sets accessType
     *
     * @param string|null $accessType 搜索直达类型 BASE：基础信息，只支持基础信息工单提报
     *
     * @return self
     */
    public function setAccessType($accessType)
    {
        $this->container['accessType'] = $accessType;

        return $this;
    }

    /**
     * Gets appid
     *
     * @return string|null
     */
    public function getAppid()
    {
        return $this->container['appid'];
    }

    /**
     * Sets appid
     *
     * @param string|null $appid 小程序Id
     *
     * @return self
     */
    public function setAppid($appid)
    {
        $this->container['appid'] = $appid;

        return $this;
    }

    /**
     * Gets baseItems
     *
     * @return \Alipay\OpenAPISDK\Model\SearchBaseItems|null
     */
    public function getBaseItems()
    {
        return $this->container['baseItems'];
    }

    /**
     * Sets baseItems
     *
     * @param \Alipay\OpenAPISDK\Model\SearchBaseItems|null $baseItems baseItems
     *
     * @return self
     */
    public function setBaseItems($baseItems)
    {
        $this->container['baseItems'] = $baseItems;

        return $this;
    }

    /**
     * Gets descprise
     *
     * @return string|null
     */
    public function getDescprise()
    {
        return $this->container['descprise'];
    }

    /**
     * Sets descprise
     *
     * @param string|null $descprise 服务描述
     *
     * @return self
     */
    public function setDescprise($descprise)
    {
        $this->container['descprise'] = $descprise;

        return $this;
    }

    /**
     * Gets isDraft
     *
     * @return bool|null
     */
    public function getIsDraft()
    {
        return $this->container['isDraft'];
    }

    /**
     * Sets isDraft
     *
     * @param bool|null $isDraft 是否为草稿态
     *
     * @return self
     */
    public function setIsDraft($isDraft)
    {
        $this->container['isDraft'] = $isDraft;

        return $this;
    }

    /**
     * Gets orderId
     *
     * @return string|null
     */
    public function getOrderId()
    {
        return $this->container['orderId'];
    }

    /**
     * Sets orderId
     *
     * @param string|null $orderId 申请单id，仅仅驳回或修改是传入
     *
     * @return self
     */
    public function setOrderId($orderId)
    {
        $this->container['orderId'] = $orderId;

        return $this;
    }

    /**
     * Gets specCode
     *
     * @return string|null
     */
    public function getSpecCode()
    {
        return $this->container['specCode'];
    }

    /**
     * Sets specCode
     *
     * @param string|null $specCode 服务的类型 SP_MINI_APP 小程序 SP_PUBLIC_APP 生活号
     *
     * @return self
     */
    public function setSpecCode($specCode)
    {
        $this->container['specCode'] = $specCode;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


