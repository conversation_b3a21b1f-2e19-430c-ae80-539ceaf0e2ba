<?php
/**
 * 系统健康检查
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/Payment.php';

header('Content-Type: application/json; charset=utf-8');

$healthStatus = [
    'overall' => 'healthy',
    'timestamp' => date('Y-m-d H:i:s'),
    'checks' => []
];

try {
    // 1. 数据库连接检查
    try {
        $db = getDB();
        $db->fetchOne("SELECT 1");
        $healthStatus['checks']['database'] = [
            'status' => 'healthy',
            'message' => '数据库连接正常'
        ];
    } catch (Exception $e) {
        $healthStatus['checks']['database'] = [
            'status' => 'unhealthy',
            'message' => '数据库连接失败: ' . $e->getMessage()
        ];
        $healthStatus['overall'] = 'unhealthy';
    }
    
    // 2. 支付宝SDK检查
    try {
        if (class_exists('AopClient')) {
            $healthStatus['checks']['alipay_sdk'] = [
                'status' => 'healthy',
                'message' => '支付宝SDK加载正常'
            ];
        } else {
            throw new Exception('AopClient类不存在');
        }
    } catch (Exception $e) {
        $healthStatus['checks']['alipay_sdk'] = [
            'status' => 'unhealthy',
            'message' => '支付宝SDK加载失败: ' . $e->getMessage()
        ];
        $healthStatus['overall'] = 'unhealthy';
    }
    
    // 3. 日志目录检查
    $logDir = SYSTEM_ROOT . '/logs';
    if (is_dir($logDir) && is_writable($logDir)) {
        $healthStatus['checks']['logs'] = [
            'status' => 'healthy',
            'message' => '日志目录可写'
        ];
    } else {
        $healthStatus['checks']['logs'] = [
            'status' => 'warning',
            'message' => '日志目录不存在或不可写'
        ];
    }
    
    // 4. 配置检查
    $configChecks = [
        'ALIPAY_APP_ID' => ALIPAY_APP_ID,
        'ALIPAY_PRIVATE_KEY' => strlen(ALIPAY_PRIVATE_KEY) > 100 ? '已配置' : '未配置',
        'ALIPAY_PUBLIC_KEY' => strlen(ALIPAY_PUBLIC_KEY) > 100 ? '已配置' : '未配置'
    ];
    
    $configHealthy = true;
    foreach ($configChecks as $key => $value) {
        if ($value === '未配置' || empty($value)) {
            $configHealthy = false;
            break;
        }
    }
    
    $healthStatus['checks']['configuration'] = [
        'status' => $configHealthy ? 'healthy' : 'unhealthy',
        'message' => $configHealthy ? '关键配置已设置' : '关键配置缺失',
        'details' => $configChecks
    ];
    
    if (!$configHealthy) {
        $healthStatus['overall'] = 'unhealthy';
    }
    
    // 5. 订单统计
    try {
        $orderStats = $db->fetchOne("
            SELECT 
                COUNT(*) as total_orders,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_orders,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_orders,
                SUM(CASE WHEN status = 'expired' THEN 1 ELSE 0 END) as expired_orders
            FROM orders 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ");
        
        $healthStatus['checks']['orders_24h'] = [
            'status' => 'info',
            'message' => '24小时订单统计',
            'details' => $orderStats
        ];
    } catch (Exception $e) {
        $healthStatus['checks']['orders_24h'] = [
            'status' => 'warning',
            'message' => '无法获取订单统计: ' . $e->getMessage()
        ];
    }
    
    // 6. 系统资源检查
    $diskFree = disk_free_space('.');
    $diskTotal = disk_total_space('.');
    $diskUsagePercent = round((($diskTotal - $diskFree) / $diskTotal) * 100, 2);
    
    $memoryUsage = memory_get_usage(true);
    $memoryLimit = ini_get('memory_limit');
    
    $healthStatus['checks']['system_resources'] = [
        'status' => $diskUsagePercent > 90 ? 'warning' : 'healthy',
        'message' => '系统资源状态',
        'details' => [
            'disk_usage_percent' => $diskUsagePercent,
            'memory_usage_mb' => round($memoryUsage / 1024 / 1024, 2),
            'memory_limit' => $memoryLimit
        ]
    ];
    
} catch (Exception $e) {
    $healthStatus['overall'] = 'unhealthy';
    $healthStatus['error'] = $e->getMessage();
}

echo json_encode($healthStatus, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>
