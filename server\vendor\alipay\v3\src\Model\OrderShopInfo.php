<?php
/**
 * OrderShopInfo
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * OrderShopInfo Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class OrderShopInfo implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'OrderShopInfo';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'address' => 'string',
        'alipayShopId' => 'string',
        'extInfo' => '\Alipay\OpenAPISDK\Model\OrderExtInfo[]',
        'merchantShopId' => 'string',
        'merchantShopLinkPage' => 'string',
        'name' => 'string',
        'phoneNum' => 'string',
        'type' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'address' => null,
        'alipayShopId' => null,
        'extInfo' => null,
        'merchantShopId' => null,
        'merchantShopLinkPage' => null,
        'name' => null,
        'phoneNum' => null,
        'type' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'address' => 'address',
        'alipayShopId' => 'alipay_shop_id',
        'extInfo' => 'ext_info',
        'merchantShopId' => 'merchant_shop_id',
        'merchantShopLinkPage' => 'merchant_shop_link_page',
        'name' => 'name',
        'phoneNum' => 'phone_num',
        'type' => 'type'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'address' => 'setAddress',
        'alipayShopId' => 'setAlipayShopId',
        'extInfo' => 'setExtInfo',
        'merchantShopId' => 'setMerchantShopId',
        'merchantShopLinkPage' => 'setMerchantShopLinkPage',
        'name' => 'setName',
        'phoneNum' => 'setPhoneNum',
        'type' => 'setType'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'address' => 'getAddress',
        'alipayShopId' => 'getAlipayShopId',
        'extInfo' => 'getExtInfo',
        'merchantShopId' => 'getMerchantShopId',
        'merchantShopLinkPage' => 'getMerchantShopLinkPage',
        'name' => 'getName',
        'phoneNum' => 'getPhoneNum',
        'type' => 'getType'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['address'] = $data['address'] ?? null;
        $this->container['alipayShopId'] = $data['alipayShopId'] ?? null;
        $this->container['extInfo'] = $data['extInfo'] ?? null;
        $this->container['merchantShopId'] = $data['merchantShopId'] ?? null;
        $this->container['merchantShopLinkPage'] = $data['merchantShopLinkPage'] ?? null;
        $this->container['name'] = $data['name'] ?? null;
        $this->container['phoneNum'] = $data['phoneNum'] ?? null;
        $this->container['type'] = $data['type'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets address
     *
     * @return string|null
     */
    public function getAddress()
    {
        return $this->container['address'];
    }

    /**
     * Sets address
     *
     * @param string|null $address 店铺地址
     *
     * @return self
     */
    public function setAddress($address)
    {
        $this->container['address'] = $address;

        return $this;
    }

    /**
     * Gets alipayShopId
     *
     * @return string|null
     */
    public function getAlipayShopId()
    {
        return $this->container['alipayShopId'];
    }

    /**
     * Sets alipayShopId
     *
     * @param string|null $alipayShopId 蚂蚁门店shop_id
     *
     * @return self
     */
    public function setAlipayShopId($alipayShopId)
    {
        $this->container['alipayShopId'] = $alipayShopId;

        return $this;
    }

    /**
     * Gets extInfo
     *
     * @return \Alipay\OpenAPISDK\Model\OrderExtInfo[]|null
     */
    public function getExtInfo()
    {
        return $this->container['extInfo'];
    }

    /**
     * Sets extInfo
     *
     * @param \Alipay\OpenAPISDK\Model\OrderExtInfo[]|null $extInfo 门店其他业务属性，不同业务场景KEY枚举值不同，使用前请参考产品文档
     *
     * @return self
     */
    public function setExtInfo($extInfo)
    {
        $this->container['extInfo'] = $extInfo;

        return $this;
    }

    /**
     * Gets merchantShopId
     *
     * @return string|null
     */
    public function getMerchantShopId()
    {
        return $this->container['merchantShopId'];
    }

    /**
     * Sets merchantShopId
     *
     * @param string|null $merchantShopId 商户门店id 支持英文、数字的组合
     *
     * @return self
     */
    public function setMerchantShopId($merchantShopId)
    {
        $this->container['merchantShopId'] = $merchantShopId;

        return $this;
    }

    /**
     * Gets merchantShopLinkPage
     *
     * @return string|null
     */
    public function getMerchantShopLinkPage()
    {
        return $this->container['merchantShopLinkPage'];
    }

    /**
     * Sets merchantShopLinkPage
     *
     * @param string|null $merchantShopLinkPage 店铺详情链接地址
     *
     * @return self
     */
    public function setMerchantShopLinkPage($merchantShopLinkPage)
    {
        $this->container['merchantShopLinkPage'] = $merchantShopLinkPage;

        return $this;
    }

    /**
     * Gets name
     *
     * @return string|null
     */
    public function getName()
    {
        return $this->container['name'];
    }

    /**
     * Sets name
     *
     * @param string|null $name 店铺名称
     *
     * @return self
     */
    public function setName($name)
    {
        $this->container['name'] = $name;

        return $this;
    }

    /**
     * Gets phoneNum
     *
     * @return string|null
     */
    public function getPhoneNum()
    {
        return $this->container['phoneNum'];
    }

    /**
     * Sets phoneNum
     *
     * @param string|null $phoneNum 联系电话-支持固话或手机号 仅支持数字、+、- 。例如 手机：1380***1111、固话：021-888**888
     *
     * @return self
     */
    public function setPhoneNum($phoneNum)
    {
        $this->container['phoneNum'] = $phoneNum;

        return $this;
    }

    /**
     * Gets type
     *
     * @return string|null
     */
    public function getType()
    {
        return $this->container['type'];
    }

    /**
     * Sets type
     *
     * @param string|null $type 仅当alipay_shop_id字段值为非标准蚂蚁门店时使用，其他场景无需传入
     *
     * @return self
     */
    public function setType($type)
    {
        $this->container['type'] = $type;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


