<?php
/**
 * IndirectBenefitPersonInfo
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * IndirectBenefitPersonInfo Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class IndirectBenefitPersonInfo implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'IndirectBenefitPersonInfo';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'cardBackImg' => 'string',
        'cardFrontImg' => 'string',
        'cardNo' => 'string',
        'cardType' => 'string',
        'effectTime' => 'string',
        'expireTime' => 'string',
        'personName' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'cardBackImg' => null,
        'cardFrontImg' => null,
        'cardNo' => null,
        'cardType' => null,
        'effectTime' => null,
        'expireTime' => null,
        'personName' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'cardBackImg' => 'card_back_img',
        'cardFrontImg' => 'card_front_img',
        'cardNo' => 'card_no',
        'cardType' => 'card_type',
        'effectTime' => 'effect_time',
        'expireTime' => 'expire_time',
        'personName' => 'person_name'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'cardBackImg' => 'setCardBackImg',
        'cardFrontImg' => 'setCardFrontImg',
        'cardNo' => 'setCardNo',
        'cardType' => 'setCardType',
        'effectTime' => 'setEffectTime',
        'expireTime' => 'setExpireTime',
        'personName' => 'setPersonName'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'cardBackImg' => 'getCardBackImg',
        'cardFrontImg' => 'getCardFrontImg',
        'cardNo' => 'getCardNo',
        'cardType' => 'getCardType',
        'effectTime' => 'getEffectTime',
        'expireTime' => 'getExpireTime',
        'personName' => 'getPersonName'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['cardBackImg'] = $data['cardBackImg'] ?? null;
        $this->container['cardFrontImg'] = $data['cardFrontImg'] ?? null;
        $this->container['cardNo'] = $data['cardNo'] ?? null;
        $this->container['cardType'] = $data['cardType'] ?? null;
        $this->container['effectTime'] = $data['effectTime'] ?? null;
        $this->container['expireTime'] = $data['expireTime'] ?? null;
        $this->container['personName'] = $data['personName'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets cardBackImg
     *
     * @return string|null
     */
    public function getCardBackImg()
    {
        return $this->container['cardBackImg'];
    }

    /**
     * Sets cardBackImg
     *
     * @param string|null $cardBackImg 受益人证件反面照（使用图片上传接口的image_id）。当证件类型为身份证时，此字段必传；当证件类型为非身份证时，此字段非必传。
     *
     * @return self
     */
    public function setCardBackImg($cardBackImg)
    {
        $this->container['cardBackImg'] = $cardBackImg;

        return $this;
    }

    /**
     * Gets cardFrontImg
     *
     * @return string|null
     */
    public function getCardFrontImg()
    {
        return $this->container['cardFrontImg'];
    }

    /**
     * Sets cardFrontImg
     *
     * @param string|null $cardFrontImg 受益人证件正面照（使用图片上传接口的image_id）
     *
     * @return self
     */
    public function setCardFrontImg($cardFrontImg)
    {
        $this->container['cardFrontImg'] = $cardFrontImg;

        return $this;
    }

    /**
     * Gets cardNo
     *
     * @return string|null
     */
    public function getCardNo()
    {
        return $this->container['cardNo'];
    }

    /**
     * Sets cardNo
     *
     * @param string|null $cardNo 证件号码
     *
     * @return self
     */
    public function setCardNo($cardNo)
    {
        $this->container['cardNo'] = $cardNo;

        return $this;
    }

    /**
     * Gets cardType
     *
     * @return string|null
     */
    public function getCardType()
    {
        return $this->container['cardType'];
    }

    /**
     * Sets cardType
     *
     * @param string|null $cardType 证件类型，枚举值：中国大陆居民-身份证(RESIDENT)、其他国家或地区居民-护照(PASSPORT)、中国港澳居民-来往内地通行证(PASSPORT_HK_MO)、中国台湾居民-来往大陆通行证(PASSPORT_TWN)、港澳居民居住证（RESIDENCE_PERMIT_HM）、台湾居民居住证（RESIDENCE_PERMIT_TW）、外国人永久居住证（PERMANENT_RESIDENCE_FOREIGNER）。个体户/企业/事业单位/社会组织：可选择任一证件类型，政府机关、小微商户仅支持身份证类型。
     *
     * @return self
     */
    public function setCardType($cardType)
    {
        $this->container['cardType'] = $cardType;

        return $this;
    }

    /**
     * Gets effectTime
     *
     * @return string|null
     */
    public function getEffectTime()
    {
        return $this->container['effectTime'];
    }

    /**
     * Sets effectTime
     *
     * @param string|null $effectTime 证件生效时间
     *
     * @return self
     */
    public function setEffectTime($effectTime)
    {
        $this->container['effectTime'] = $effectTime;

        return $this;
    }

    /**
     * Gets expireTime
     *
     * @return string|null
     */
    public function getExpireTime()
    {
        return $this->container['expireTime'];
    }

    /**
     * Sets expireTime
     *
     * @param string|null $expireTime 证件过期时间
     *
     * @return self
     */
    public function setExpireTime($expireTime)
    {
        $this->container['expireTime'] = $expireTime;

        return $this;
    }

    /**
     * Gets personName
     *
     * @return string|null
     */
    public function getPersonName()
    {
        return $this->container['personName'];
    }

    /**
     * Sets personName
     *
     * @param string|null $personName 受益人姓名
     *
     * @return self
     */
    public function setPersonName($personName)
    {
        $this->container['personName'] = $personName;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


