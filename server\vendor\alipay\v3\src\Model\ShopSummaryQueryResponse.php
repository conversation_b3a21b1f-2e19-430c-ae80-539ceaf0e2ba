<?php
/**
 * ShopSummaryQueryResponse
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * 支付宝开放平台API
 *
 * 支付宝开放平台v3协议文档
 *
 * The version of the OpenAPI document: 2025-02-19
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 6.2.1
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Alipay\OpenAPISDK\Model;

use \ArrayAccess;
use \Alipay\OpenAPISDK\ObjectSerializer;

/**
 * ShopSummaryQueryResponse Class Doc Comment
 *
 * @category Class
 * @package  Alipay\OpenAPISDK
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<TKey, TValue>
 * @template TKey int|null
 * @template TValue mixed|null
 */
class ShopSummaryQueryResponse implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'ShopSummaryQueryResponse';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'address' => 'string',
        'branchShopName' => 'string',
        'brandName' => 'string',
        'businessTime' => 'string',
        'categoryInfos' => '\Alipay\OpenAPISDK\Model\ShopCategoryInfo[]',
        'cityCode' => 'string',
        'districtCode' => 'string',
        'extInfo' => 'string',
        'gmtCreate' => 'string',
        'isShow' => 'string',
        'latitude' => 'string',
        'longitude' => 'string',
        'mainImage' => 'string',
        'mainShopName' => 'string',
        'perPay' => 'string',
        'picColl' => 'string',
        'provinceCode' => 'string',
        'shopCommentInfo' => '\Alipay\OpenAPISDK\Model\ShopCommentInfo',
        'shopId' => 'string',
        'shopType' => 'string',
        'status' => 'string'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'address' => null,
        'branchShopName' => null,
        'brandName' => null,
        'businessTime' => null,
        'categoryInfos' => null,
        'cityCode' => null,
        'districtCode' => null,
        'extInfo' => null,
        'gmtCreate' => null,
        'isShow' => null,
        'latitude' => null,
        'longitude' => null,
        'mainImage' => null,
        'mainShopName' => null,
        'perPay' => null,
        'picColl' => null,
        'provinceCode' => null,
        'shopCommentInfo' => null,
        'shopId' => null,
        'shopType' => null,
        'status' => null
    ];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'address' => 'address',
        'branchShopName' => 'branch_shop_name',
        'brandName' => 'brand_name',
        'businessTime' => 'business_time',
        'categoryInfos' => 'category_infos',
        'cityCode' => 'city_code',
        'districtCode' => 'district_code',
        'extInfo' => 'ext_info',
        'gmtCreate' => 'gmt_create',
        'isShow' => 'is_show',
        'latitude' => 'latitude',
        'longitude' => 'longitude',
        'mainImage' => 'main_image',
        'mainShopName' => 'main_shop_name',
        'perPay' => 'per_pay',
        'picColl' => 'pic_coll',
        'provinceCode' => 'province_code',
        'shopCommentInfo' => 'shop_comment_info',
        'shopId' => 'shop_id',
        'shopType' => 'shop_type',
        'status' => 'status'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'address' => 'setAddress',
        'branchShopName' => 'setBranchShopName',
        'brandName' => 'setBrandName',
        'businessTime' => 'setBusinessTime',
        'categoryInfos' => 'setCategoryInfos',
        'cityCode' => 'setCityCode',
        'districtCode' => 'setDistrictCode',
        'extInfo' => 'setExtInfo',
        'gmtCreate' => 'setGmtCreate',
        'isShow' => 'setIsShow',
        'latitude' => 'setLatitude',
        'longitude' => 'setLongitude',
        'mainImage' => 'setMainImage',
        'mainShopName' => 'setMainShopName',
        'perPay' => 'setPerPay',
        'picColl' => 'setPicColl',
        'provinceCode' => 'setProvinceCode',
        'shopCommentInfo' => 'setShopCommentInfo',
        'shopId' => 'setShopId',
        'shopType' => 'setShopType',
        'status' => 'setStatus'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'address' => 'getAddress',
        'branchShopName' => 'getBranchShopName',
        'brandName' => 'getBrandName',
        'businessTime' => 'getBusinessTime',
        'categoryInfos' => 'getCategoryInfos',
        'cityCode' => 'getCityCode',
        'districtCode' => 'getDistrictCode',
        'extInfo' => 'getExtInfo',
        'gmtCreate' => 'getGmtCreate',
        'isShow' => 'getIsShow',
        'latitude' => 'getLatitude',
        'longitude' => 'getLongitude',
        'mainImage' => 'getMainImage',
        'mainShopName' => 'getMainShopName',
        'perPay' => 'getPerPay',
        'picColl' => 'getPicColl',
        'provinceCode' => 'getProvinceCode',
        'shopCommentInfo' => 'getShopCommentInfo',
        'shopId' => 'getShopId',
        'shopType' => 'getShopType',
        'status' => 'getStatus'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->container['address'] = $data['address'] ?? null;
        $this->container['branchShopName'] = $data['branchShopName'] ?? null;
        $this->container['brandName'] = $data['brandName'] ?? null;
        $this->container['businessTime'] = $data['businessTime'] ?? null;
        $this->container['categoryInfos'] = $data['categoryInfos'] ?? null;
        $this->container['cityCode'] = $data['cityCode'] ?? null;
        $this->container['districtCode'] = $data['districtCode'] ?? null;
        $this->container['extInfo'] = $data['extInfo'] ?? null;
        $this->container['gmtCreate'] = $data['gmtCreate'] ?? null;
        $this->container['isShow'] = $data['isShow'] ?? null;
        $this->container['latitude'] = $data['latitude'] ?? null;
        $this->container['longitude'] = $data['longitude'] ?? null;
        $this->container['mainImage'] = $data['mainImage'] ?? null;
        $this->container['mainShopName'] = $data['mainShopName'] ?? null;
        $this->container['perPay'] = $data['perPay'] ?? null;
        $this->container['picColl'] = $data['picColl'] ?? null;
        $this->container['provinceCode'] = $data['provinceCode'] ?? null;
        $this->container['shopCommentInfo'] = $data['shopCommentInfo'] ?? null;
        $this->container['shopId'] = $data['shopId'] ?? null;
        $this->container['shopType'] = $data['shopType'] ?? null;
        $this->container['status'] = $data['status'] ?? null;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets address
     *
     * @return string|null
     */
    public function getAddress()
    {
        return $this->container['address'];
    }

    /**
     * Sets address
     *
     * @param string|null $address 门店地址
     *
     * @return self
     */
    public function setAddress($address)
    {
        $this->container['address'] = $address;

        return $this;
    }

    /**
     * Gets branchShopName
     *
     * @return string|null
     */
    public function getBranchShopName()
    {
        return $this->container['branchShopName'];
    }

    /**
     * Sets branchShopName
     *
     * @param string|null $branchShopName 分店名
     *
     * @return self
     */
    public function setBranchShopName($branchShopName)
    {
        $this->container['branchShopName'] = $branchShopName;

        return $this;
    }

    /**
     * Gets brandName
     *
     * @return string|null
     */
    public function getBrandName()
    {
        return $this->container['brandName'];
    }

    /**
     * Sets brandName
     *
     * @param string|null $brandName 品牌名，不填写则默认为其它品牌
     *
     * @return self
     */
    public function setBrandName($brandName)
    {
        $this->container['brandName'] = $brandName;

        return $this;
    }

    /**
     * Gets businessTime
     *
     * @return string|null
     */
    public function getBusinessTime()
    {
        return $this->container['businessTime'];
    }

    /**
     * Sets businessTime
     *
     * @param string|null $businessTime 经营时间
     *
     * @return self
     */
    public function setBusinessTime($businessTime)
    {
        $this->container['businessTime'] = $businessTime;

        return $this;
    }

    /**
     * Gets categoryInfos
     *
     * @return \Alipay\OpenAPISDK\Model\ShopCategoryInfo[]|null
     */
    public function getCategoryInfos()
    {
        return $this->container['categoryInfos'];
    }

    /**
     * Sets categoryInfos
     *
     * @param \Alipay\OpenAPISDK\Model\ShopCategoryInfo[]|null $categoryInfos 门店类目列表
     *
     * @return self
     */
    public function setCategoryInfos($categoryInfos)
    {
        $this->container['categoryInfos'] = $categoryInfos;

        return $this;
    }

    /**
     * Gets cityCode
     *
     * @return string|null
     */
    public function getCityCode()
    {
        return $this->container['cityCode'];
    }

    /**
     * Sets cityCode
     *
     * @param string|null $cityCode 城市编码，国标码，详见国家统计局数据 <a href=\"http://aopsdkdownload.cn-hangzhou.alipay-pub.aliyun-inc.com/doc/AreaCodeList.zip\">点此下载</a>
     *
     * @return self
     */
    public function setCityCode($cityCode)
    {
        $this->container['cityCode'] = $cityCode;

        return $this;
    }

    /**
     * Gets districtCode
     *
     * @return string|null
     */
    public function getDistrictCode()
    {
        return $this->container['districtCode'];
    }

    /**
     * Sets districtCode
     *
     * @param string|null $districtCode 区县编码，国标码，详见国家统计局数据 <a href=\"http://aopsdkdownload.cn-hangzhou.alipay-pub.aliyun-inc.com/doc/AreaCodeList.zip\">点此下载</a>
     *
     * @return self
     */
    public function setDistrictCode($districtCode)
    {
        $this->container['districtCode'] = $districtCode;

        return $this;
    }

    /**
     * Gets extInfo
     *
     * @return string|null
     */
    public function getExtInfo()
    {
        return $this->container['extInfo'];
    }

    /**
     * Sets extInfo
     *
     * @param string|null $extInfo json 字符串表示额外信息;    order_biz_mode: 点餐营业模式, REGULAR-正餐、LIGHT_FAST-轻快餐;  pre_order: 预点餐服务可用状态, 1-服务可用、0-服务不可用  order: 到店点餐服务可用状态：1-服务可用、0-服务不可用
     *
     * @return self
     */
    public function setExtInfo($extInfo)
    {
        $this->container['extInfo'] = $extInfo;

        return $this;
    }

    /**
     * Gets gmtCreate
     *
     * @return string|null
     */
    public function getGmtCreate()
    {
        return $this->container['gmtCreate'];
    }

    /**
     * Sets gmtCreate
     *
     * @param string|null $gmtCreate 创建时间
     *
     * @return self
     */
    public function setGmtCreate($gmtCreate)
    {
        $this->container['gmtCreate'] = $gmtCreate;

        return $this;
    }

    /**
     * Gets isShow
     *
     * @return string|null
     */
    public function getIsShow()
    {
        return $this->container['isShow'];
    }

    /**
     * Sets isShow
     *
     * @param string|null $isShow 门店是否在客户端显示，T表示显示，F表示隐藏
     *
     * @return self
     */
    public function setIsShow($isShow)
    {
        $this->container['isShow'] = $isShow;

        return $this;
    }

    /**
     * Gets latitude
     *
     * @return string|null
     */
    public function getLatitude()
    {
        return $this->container['latitude'];
    }

    /**
     * Sets latitude
     *
     * @param string|null $latitude 纬度，只有在query_type=KB_PROMOTER非空
     *
     * @return self
     */
    public function setLatitude($latitude)
    {
        $this->container['latitude'] = $latitude;

        return $this;
    }

    /**
     * Gets longitude
     *
     * @return string|null
     */
    public function getLongitude()
    {
        return $this->container['longitude'];
    }

    /**
     * Sets longitude
     *
     * @param string|null $longitude 经度，只有在query_type=KB_PROMOTER非空
     *
     * @return self
     */
    public function setLongitude($longitude)
    {
        $this->container['longitude'] = $longitude;

        return $this;
    }

    /**
     * Gets mainImage
     *
     * @return string|null
     */
    public function getMainImage()
    {
        return $this->container['mainImage'];
    }

    /**
     * Sets mainImage
     *
     * @param string|null $mainImage 门店首图
     *
     * @return self
     */
    public function setMainImage($mainImage)
    {
        $this->container['mainImage'] = $mainImage;

        return $this;
    }

    /**
     * Gets mainShopName
     *
     * @return string|null
     */
    public function getMainShopName()
    {
        return $this->container['mainShopName'];
    }

    /**
     * Sets mainShopName
     *
     * @param string|null $mainShopName 主门店名
     *
     * @return self
     */
    public function setMainShopName($mainShopName)
    {
        $this->container['mainShopName'] = $mainShopName;

        return $this;
    }

    /**
     * Gets perPay
     *
     * @return string|null
     */
    public function getPerPay()
    {
        return $this->container['perPay'];
    }

    /**
     * Sets perPay
     *
     * @param string|null $perPay 人均价格。单位是分。例如：2000，表示20元
     *
     * @return self
     */
    public function setPerPay($perPay)
    {
        $this->container['perPay'] = $perPay;

        return $this;
    }

    /**
     * Gets picColl
     *
     * @return string|null
     */
    public function getPicColl()
    {
        return $this->container['picColl'];
    }

    /**
     * Sets picColl
     *
     * @param string|null $picColl 图片集，是map转化成的json串，key是图片id,value是图片url
     *
     * @return self
     */
    public function setPicColl($picColl)
    {
        $this->container['picColl'] = $picColl;

        return $this;
    }

    /**
     * Gets provinceCode
     *
     * @return string|null
     */
    public function getProvinceCode()
    {
        return $this->container['provinceCode'];
    }

    /**
     * Sets provinceCode
     *
     * @param string|null $provinceCode 省份编码，国标码，详见国家统计局数据 <a href=\"http://aopsdkdownload.cn-hangzhou.alipay-pub.aliyun-inc.com/doc/AreaCodeList.zip\">点此下载</a>
     *
     * @return self
     */
    public function setProvinceCode($provinceCode)
    {
        $this->container['provinceCode'] = $provinceCode;

        return $this;
    }

    /**
     * Gets shopCommentInfo
     *
     * @return \Alipay\OpenAPISDK\Model\ShopCommentInfo|null
     */
    public function getShopCommentInfo()
    {
        return $this->container['shopCommentInfo'];
    }

    /**
     * Sets shopCommentInfo
     *
     * @param \Alipay\OpenAPISDK\Model\ShopCommentInfo|null $shopCommentInfo shopCommentInfo
     *
     * @return self
     */
    public function setShopCommentInfo($shopCommentInfo)
    {
        $this->container['shopCommentInfo'] = $shopCommentInfo;

        return $this;
    }

    /**
     * Gets shopId
     *
     * @return string|null
     */
    public function getShopId()
    {
        return $this->container['shopId'];
    }

    /**
     * Sets shopId
     *
     * @param string|null $shopId 门店ID
     *
     * @return self
     */
    public function setShopId($shopId)
    {
        $this->container['shopId'] = $shopId;

        return $this;
    }

    /**
     * Gets shopType
     *
     * @return string|null
     */
    public function getShopType()
    {
        return $this->container['shopType'];
    }

    /**
     * Sets shopType
     *
     * @param string|null $shopType COMMON（普通门店）、MALL（商圈）
     *
     * @return self
     */
    public function setShopType($shopType)
    {
        $this->container['shopType'] = $shopType;

        return $this;
    }

    /**
     * Gets status
     *
     * @return string|null
     */
    public function getStatus()
    {
        return $this->container['status'];
    }

    /**
     * Sets status
     *
     * @param string|null $status 门店状态，OPEN：营业中、PAUSE：暂停营业、FREEZE：已冻结、CLOSE:门店已关闭
     *
     * @return self
     */
    public function setStatus($status)
    {
        $this->container['status'] = $status;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


